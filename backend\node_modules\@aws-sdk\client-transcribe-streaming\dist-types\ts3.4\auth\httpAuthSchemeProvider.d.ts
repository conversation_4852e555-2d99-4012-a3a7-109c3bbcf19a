import {
  AwsSdkSigV4AuthInputConfig,
  AwsSdkSigV4AuthResolvedConfig,
  AwsSdkSigV4PreviouslyResolved,
} from "@aws-sdk/core";
import {
  HandlerExecutionContext,
  HttpAuthScheme,
  HttpAuthSchemeParameters,
  HttpAuthSchemeParametersProvider,
  HttpAuthSchemeProvider,
  Provider,
} from "@smithy/types";
import { TranscribeStreamingClientResolvedConfig } from "../TranscribeStreamingClient";
export interface TranscribeStreamingHttpAuthSchemeParameters
  extends HttpAuthSchemeParameters {
  region?: string;
}
export interface TranscribeStreamingHttpAuthSchemeParametersProvider
  extends HttpAuthSchemeParametersProvider<
    TranscribeStreamingClientResolvedConfig,
    HandlerExecutionContext,
    TranscribeStreamingHttpAuthSchemeParameters,
    object
  > {}
export declare const defaultTranscribeStreamingHttpAuthSchemeParametersProvider: (
  config: TranscribeStreamingClientResolvedConfig,
  context: HandlerExecutionContext,
  input: object
) => Promise<TranscribeStreamingHttpAuthSchemeParameters>;
export interface TranscribeStreamingHttpAuthSchemeProvider
  extends HttpAuthSchemeProvider<TranscribeStreamingHttpAuthSchemeParameters> {}
export declare const defaultTranscribeStreamingHttpAuthSchemeProvider: TranscribeStreamingHttpAuthSchemeProvider;
export interface HttpAuthSchemeInputConfig extends AwsSdkSigV4AuthInputConfig {
  authSchemePreference?: string[] | Provider<string[]>;
  httpAuthSchemes?: HttpAuthScheme[];
  httpAuthSchemeProvider?: TranscribeStreamingHttpAuthSchemeProvider;
}
export interface HttpAuthSchemeResolvedConfig
  extends AwsSdkSigV4AuthResolvedConfig {
  readonly authSchemePreference: Provider<string[]>;
  readonly httpAuthSchemes: HttpAuthScheme[];
  readonly httpAuthSchemeProvider: TranscribeStreamingHttpAuthSchemeProvider;
}
export declare const resolveHttpAuthSchemeConfig: <T>(
  config: T & HttpAuthSchemeInputConfig & AwsSdkSigV4PreviouslyResolved
) => T & HttpAuthSchemeResolvedConfig;
