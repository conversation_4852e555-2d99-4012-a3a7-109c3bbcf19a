"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SecurityController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityController = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../database/database.service");
let SecurityController = SecurityController_1 = class SecurityController {
    constructor(databaseService) {
        this.databaseService = databaseService;
        this.logger = new common_1.Logger(SecurityController_1.name);
    }
    async checkPermission(request) {
        try {
            this.logger.log(`权限检查请求 - Agent: ${request.agentType}, 资源: ${request.resource}, 操作: ${request.action}`);
            const result = await this.performPermissionCheck(request.agentType, request.userId, request.resource, request.action, request.requestData);
            return {
                success: true,
                data: result,
            };
        }
        catch (error) {
            this.logger.error(`权限检查失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async getAgentPermissions(agentType) {
        try {
            const permissions = await this.getDefaultPermissions(agentType);
            return {
                success: true,
                data: {
                    agentType,
                    permissions,
                    total: permissions.length,
                },
            };
        }
        catch (error) {
            this.logger.error(`获取权限失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async getAuditLogs(agentType, userId, resource, startDate, endDate, page = 1, limit = 50) {
        try {
            const auditLogs = [];
            return {
                success: true,
                data: {
                    logs: auditLogs,
                    pagination: {
                        page,
                        limit,
                        total: auditLogs.length,
                        totalPages: Math.ceil(auditLogs.length / limit),
                    },
                    filters: {
                        agentType,
                        userId,
                        resource,
                        startDate,
                        endDate,
                    },
                },
            };
        }
        catch (error) {
            this.logger.error(`获取审计日志失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async getSecurityStats(agentType, timeRange = '24h') {
        try {
            const stats = {
                totalRequests: 0,
                allowedRequests: 0,
                deniedRequests: 0,
                byAgent: {},
                byResource: {},
                byAction: {},
                timeline: [],
            };
            return {
                success: true,
                data: stats,
            };
        }
        catch (error) {
            this.logger.error(`获取安全统计失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async createPermission(permission) {
        try {
            this.logger.log(`创建权限规则 - Agent: ${permission.agentType}, 资源: ${permission.resource}`);
            const result = {
                id: `${permission.agentType}:${permission.resource}:${permission.action}`,
                ...permission,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            return {
                success: true,
                data: result,
            };
        }
        catch (error) {
            this.logger.error(`创建权限失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async performPermissionCheck(agentType, userId, resource, action, requestData) {
        if (agentType === 'almighty') {
            return { allowed: true, reason: 'Almighty Agent拥有全部权限' };
        }
        const allowedResources = this.getAllowedResources(agentType);
        if (!allowedResources.includes(resource) && !allowedResources.includes('*')) {
            return {
                allowed: false,
                reason: `Agent ${agentType} 无权访问资源 ${resource}`,
            };
        }
        const deniedResources = this.getDeniedResources(agentType);
        if (deniedResources.includes(resource)) {
            return {
                allowed: false,
                reason: `资源 ${resource} 在Agent ${agentType} 的禁用列表中`,
            };
        }
        const allowedActions = this.getAllowedActions(agentType, resource);
        if (!allowedActions.includes(action) && !allowedActions.includes('*')) {
            return {
                allowed: false,
                reason: `Agent ${agentType} 无权在资源 ${resource} 上执行 ${action} 操作`,
            };
        }
        const isolationCheck = this.checkDataIsolation(agentType, requestData);
        if (!isolationCheck.allowed) {
            return isolationCheck;
        }
        await this.logAccess(agentType, userId, resource, action, true, requestData);
        return { allowed: true, reason: '权限检查通过' };
    }
    getAllowedResources(agentType) {
        const resourceMap = {
            almighty: ['*'],
            taoist: ['conversation_sessions', 'conversation_messages', 'knowledge_entries', 'knowledge_usage_logs'],
            freud: ['conversation_sessions', 'conversation_messages', 'knowledge_entries', 'knowledge_usage_logs'],
            papa: ['conversation_sessions', 'conversation_messages', 'knowledge_entries', 'knowledge_usage_logs'],
            shared: ['conversation_sessions', 'conversation_messages', 'knowledge_entries'],
        };
        return resourceMap[agentType] || [];
    }
    getDeniedResources(agentType) {
        const deniedMap = {
            almighty: [],
            taoist: ['user_wallets', 'payment_records', 'admin_logs'],
            freud: ['user_wallets', 'payment_records', 'admin_logs'],
            papa: ['user_wallets', 'payment_records', 'admin_logs'],
            shared: ['user_wallets', 'payment_records', 'admin_logs'],
        };
        return deniedMap[agentType] || [];
    }
    getAllowedActions(agentType, resource) {
        if (agentType === 'almighty') {
            return ['*'];
        }
        const actionMap = {
            conversation_sessions: ['select', 'insert', 'update'],
            conversation_messages: ['select', 'insert'],
            knowledge_entries: ['select'],
            knowledge_usage_logs: ['select', 'insert'],
        };
        return actionMap[resource] || [];
    }
    checkDataIsolation(agentType, requestData) {
        if (!requestData) {
            return { allowed: true };
        }
        if (agentType === 'almighty') {
            return { allowed: true };
        }
        if (requestData.agent_type && requestData.agent_type !== agentType) {
            return {
                allowed: false,
                reason: `Agent ${agentType} 不能访问其他Agent的数据`,
            };
        }
        return { allowed: true };
    }
    async getDefaultPermissions(agentType) {
        const permissions = [];
        const allowedResources = this.getAllowedResources(agentType);
        for (const resource of allowedResources) {
            const actions = this.getAllowedActions(agentType, resource);
            for (const action of actions) {
                permissions.push({
                    agentType,
                    resource,
                    action,
                    allowed: true,
                    conditions: agentType !== 'almighty' ? { agent_type: agentType } : undefined,
                });
            }
        }
        return permissions;
    }
    async logAccess(agentType, userId, resource, action, allowed, requestData) {
        try {
            const logEntry = {
                agent_type: agentType,
                user_id: userId,
                resource,
                action,
                allowed,
                request_data: this.sanitizeLogData(requestData),
                timestamp: new Date(),
            };
            setImmediate(async () => {
                try {
                    await this.databaseService.executeWithPermission('almighty', {
                        table: 'security_audit_logs',
                        operation: 'insert',
                        data: logEntry,
                    });
                }
                catch (error) {
                    this.logger.warn(`记录安全日志失败: ${error.message}`);
                }
            });
        }
        catch (error) {
            this.logger.warn(`准备安全日志失败: ${error.message}`);
        }
    }
    sanitizeLogData(data) {
        if (!data || typeof data !== 'object') {
            return data;
        }
        const sanitized = { ...data };
        const sensitiveFields = ['password', 'token', 'secret', 'key', 'wallet_address'];
        sensitiveFields.forEach(field => {
            if (sanitized[field]) {
                sanitized[field] = '[REDACTED]';
            }
        });
        return sanitized;
    }
};
exports.SecurityController = SecurityController;
__decorate([
    (0, common_1.Post)('check-permission'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "checkPermission", null);
__decorate([
    (0, common_1.Get)('permissions/:agentType'),
    __param(0, (0, common_1.Param)('agentType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "getAgentPermissions", null);
__decorate([
    (0, common_1.Get)('audit-logs'),
    __param(0, (0, common_1.Query)('agentType')),
    __param(1, (0, common_1.Query)('userId')),
    __param(2, (0, common_1.Query)('resource')),
    __param(3, (0, common_1.Query)('startDate')),
    __param(4, (0, common_1.Query)('endDate')),
    __param(5, (0, common_1.Query)('page')),
    __param(6, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, Number, Number]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "getAuditLogs", null);
__decorate([
    (0, common_1.Get)('stats'),
    __param(0, (0, common_1.Query)('agentType')),
    __param(1, (0, common_1.Query)('timeRange')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "getSecurityStats", null);
__decorate([
    (0, common_1.Post)('permissions'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "createPermission", null);
exports.SecurityController = SecurityController = SecurityController_1 = __decorate([
    (0, common_1.Controller)('security'),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], SecurityController);
//# sourceMappingURL=security.controller.js.map