# Donny 前端开发指南

## 1. 概述

本文档为 Donny 项目前端开发团队提供详细的技术指南和实施标准，确保界面设计的一致性和代码质量。前端应用需实现 HEX 编码静态背景和中心辐射式单页面布局的创新设计，支持文本与语音混合输入，并无缝集成 Web3 功能。

## 2. 技术栈

### 2.1 核心框架与库

* **框架**: React 18+
* **类型系统**: TypeScript 4.9+
* **状态管理**: Redux Toolkit 或 Zustand
* **路由**: React Router v6
* **样式方案**: Styled Components + Ant Design
* **动画库**: Framer Motion 或 React Spring
* **3D 渲染**: Three.js 或 React Three Fiber
* **Web3 集成**: @solana/wallet-adapter, @solana/web3.js
* **表单处理**: React Hook Form + Zod
* **HTTP 客户端**: Axios

### 2.2 开发工具

* **构建工具**: Vite
* **代码规范**: ESLint + Prettier
* **测试工具**: Jest + React Testing Library
* **E2E 测试**: Cypress
* **Git Hooks**: Husky + lint-staged

## 3. 项目结构

```
frontend/
├── public/             # 静态资源
│   ├── fonts/          # 字体文件
│   ├── images/         # 静态图片
│   └── models/         # 3D 模型文件
├── src/
│   ├── assets/         # 需要通过构建工具处理的资源
│   ├── components/     # 共享组件
│   │   ├── common/     # 通用 UI 组件
│   │   ├── layout/     # 布局组件
│   │   ├── donny/      # Donny 相关组件
│   │   └── web3/       # Web3 相关组件
│   ├── context/        # React Context
│   ├── hooks/          # 自定义 Hooks
│   ├── pages/          # 页面组件
│   ├── services/       # API 服务
│   │   ├── api/        # API 调用
│   │   ├── speech/     # 语音服务
│   │   └── web3/       # Web3 交互服务
│   ├── store/          # 状态管理
│   ├── styles/         # 全局样式和主题
│   ├── types/          # TypeScript 类型定义
│   ├── utils/          # 工具函数
│   ├── App.tsx         # 根组件
│   └── main.tsx        # 入口文件
├── tests/              # 测试文件
├── .eslintrc.js        # ESLint 配置
├── .prettierrc         # Prettier 配置
├── tsconfig.json       # TypeScript 配置
├── vite.config.ts      # Vite 配置
└── package.json        # 依赖和脚本
```

## 4. 组件设计原则

### 4.1 组件分类

* **UI 组件**: 纯展示型组件，不包含业务逻辑
* **容器组件**: 负责数据获取和状态管理
* **布局组件**: 负责页面结构和元素排列
* **页面组件**: 对应路由的顶层组件
* **特性组件**: 特定功能的复合组件

### 4.2 组件设计规范

* **单一职责**: 每个组件只负责一个功能
* **组件大小控制**: 保持组件简洁，超过 250 行考虑拆分
* **Props 设计**: 使用解构赋值，提供默认值，添加 PropTypes 或 TypeScript 类型
* **状态管理**: 本地状态用 useState/useReducer，全局状态用状态管理库
* **副作用处理**: 使用 useEffect，注意依赖数组和清理函数
* **条件渲染**: 使用三元运算符或逻辑与(&&)进行简单条件渲染
* **UI 组件库使用**: 
  - 优先使用 Ant Design 提供的组件
  - 对 Ant Design 组件进行二次封装时，保持原有的 API 设计风格
  - 自定义主题时遵循项目设计规范
  - 结合 Styled Components 对 Ant Design 组件进行样式定制

### 4.3 组件示例

```tsx
// Button.tsx
import React from 'react';
import styled from 'styled-components';

type ButtonProps = {
  variant?: 'primary' | 'secondary' | 'outlined';
  size?: 'small' | 'medium' | 'large';
  isLoading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
};

const StyledButton = styled.button<Omit<ButtonProps, 'children'>>`
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  ${({ variant, theme }) => 
    variant === 'primary' && `
      background-color: ${theme.colors.primary};
      color: white;
    `}
  
  // 其他样式变体...
  
  ${({ size }) => 
    size === 'medium' && `
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
    `}
  
  // 其他尺寸...
  
  ${({ disabled }) => 
    disabled && `
      opacity: 0.6;
      cursor: not-allowed;
    `}
`;

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  isLoading = false,
  disabled = false,
  onClick,
  children,
}) => {
  return (
    <StyledButton
      variant={variant}
      size={size}
      disabled={disabled || isLoading}
      onClick={!disabled && !isLoading ? onClick : undefined}
    >
      {isLoading ? <Spinner size="small" /> : children}
    </StyledButton>
  );
};
```

## 5. 中心辐射式布局实现

### 5.1 布局结构

```tsx
// RadialLayout.tsx
import React from 'react';
import styled from 'styled-components';
import { HexBackground } from './HexBackground';
import { CentralContent } from './CentralContent';
import { RadialElements } from './RadialElements';
import { DonnyModel } from './DonnyModel';

const LayoutContainer = styled.div`
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
`;

export const RadialLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <LayoutContainer>
      <HexBackground />
      <CentralContent>
        <DonnyModel />
        <RadialElements />
        {children}
      </CentralContent>
    </LayoutContainer>
  );
};
```

### 5.2 中央内容区实现

```tsx
// CentralContent.tsx
import React from 'react';
import styled from 'styled-components';

const CentralContentContainer = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70vw;
  height: 70vw;
  max-width: 1080px;
  max-height: 1080px;
  min-width: 768px;
  min-height: 768px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  display: grid;
  grid-template-areas:
    "tl tc tr"
    "ml cc mr"
    "bl bc br";
  grid-template-columns: 1fr 3fr 1fr;
  grid-template-rows: 1fr 3fr 1fr;
`;

const CentralInteractionArea = styled.div`
  grid-area: cc;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
`;

export const CentralContent: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <CentralContentContainer>
      <CentralInteractionArea>
        {children}
      </CentralInteractionArea>
    </CentralContentContainer>
  );
};
```

## 6. HEX 编码静态背景实现

### 6.1 使用 Canvas 实现

```tsx
// HexBackground.tsx
import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';
import { hexText } from '../constants/hexText';

const CanvasContainer = styled.canvas`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1;
`;

export const HexBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 设置画布尺寸为窗口尺寸
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    
    // 绘制背景
    ctx.fillStyle = '#2C2A2A';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 绘制 HEX 文本
    ctx.fillStyle = '#B6EBED';
    ctx.font = '12px monospace';
    ctx.globalAlpha = 0.3;
    
    const textLines = hexText.split('\n');
    let yPos = 20;
    
    textLines.forEach(line => {
      ctx.fillText(line, 20, yPos);
      yPos += 15;
    });
    
    // 处理窗口大小变化
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      
      // 重新绘制
      ctx.fillStyle = '#2C2A2A';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      ctx.fillStyle = '#B6EBED';
      ctx.font = '12px monospace';
      ctx.globalAlpha = 0.3;
      
      let yPos = 20;
      textLines.forEach(line => {
        ctx.fillText(line, 20, yPos);
        yPos += 15;
      });
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  return <CanvasContainer ref={canvasRef} />;
};
```

## 7. 语音输入实现

### 7.1 语音录入组件

```tsx
// VoiceInput.tsx
import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { useVoiceRecognition } from '../hooks/useVoiceRecognition';
import { MicIcon, StopIcon, LoadingIcon } from './Icons';

const VoiceButton = styled.button<{ isRecording: boolean }>`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: ${({ isRecording, theme }) => 
    isRecording ? theme.colors.danger : theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
`;

const WaveformContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
  height: 20px;
`;

const WaveformBar = styled.div<{ height: number }>`
  width: 3px;
  height: ${({ height }) => `${height}px`};
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 1px;
  transition: height 0.1s ease;
`;

type VoiceInputProps = {
  onTranscript: (text: string) => void;
};

export const VoiceInput: React.FC<VoiceInputProps> = ({ onTranscript }) => {
  const [visualizerData, setVisualizerData] = useState<number[]>(Array(15).fill(3));
  
  const { 
    isRecording, 
    isProcessing, 
    startRecording, 
    stopRecording 
  } = useVoiceRecognition({
    onAudioLevel: (level) => {
      // 更新可视化数据
      setVisualizerData(prev => {
        const newData = [...prev];
        newData.shift();
        newData.push(Math.min(20, 3 + level * 17));
        return newData;
      });
    },
    onResult: (transcript) => {
      onTranscript(transcript);
    }
  });
  
  const handleToggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };
  
  return (
    <div>
      <VoiceButton 
        isRecording={isRecording}
        onClick={handleToggleRecording}
        disabled={isProcessing}
      >
        {isProcessing ? (
          <LoadingIcon />
        ) : isRecording ? (
          <StopIcon />
        ) : (
          <MicIcon />
        )}
      </VoiceButton>
      
      {isRecording && (
        <WaveformContainer>
          {visualizerData.map((height, index) => (
            <WaveformBar key={index} height={height} />
          ))}
        </WaveformContainer>
      )}
    </div>
  );
};
```

### 7.2 语音识别 Hook

```tsx
// useVoiceRecognition.ts
import { useState, useEffect, useRef } from 'react';
import { speechToTextAPI } from '../services/speech/speechToTextService';

type VoiceRecognitionOptions = {
  onAudioLevel?: (level: number) => void;
  onResult?: (transcript: string) => void;
  onError?: (error: string) => void;
};

export const useVoiceRecognition = ({
  onAudioLevel,
  onResult,
  onError
}: VoiceRecognitionOptions) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  
  // 处理音频级别
  useEffect(() => {
    if (isRecording && onAudioLevel && analyserRef.current) {
      const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
      
      const updateLevel = () => {
        if (!isRecording || !analyserRef.current) return;
        
        analyserRef.current.getByteFrequencyData(dataArray);
        
        // 计算音频级别 (0-1 范围)
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
          sum += dataArray[i];
        }
        const average = sum / dataArray.length / 255;
        
        onAudioLevel(average);
        requestAnimationFrame(updateLevel);
      };
      
      updateLevel();
    }
  }, [isRecording, onAudioLevel]);
  
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // 设置音频分析
      audioContextRef.current = new AudioContext();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      source.connect(analyserRef.current);
      
      // 设置媒体录制
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];
      
      mediaRecorderRef.current.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };
      
      mediaRecorderRef.current.onstop = async () => {
        setIsProcessing(true);
        
        try {
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
          
          // 调用 API 进行语音识别
          const transcript = await speechToTextAPI(audioBlob);
          
          if (onResult) {
            onResult(transcript);
          }
        } catch (error) {
          if (onError) {
            onError('语音识别失败');
          }
          console.error('Speech recognition error:', error);
        } finally {
          setIsProcessing(false);
        }
        
        // 清理
        stream.getTracks().forEach(track => track.stop());
      };
      
      mediaRecorderRef.current.start();
      setIsRecording(true);
    } catch (error) {
      if (onError) {
        onError('无法访问麦克风');
      }
      console.error('Media access error:', error);
    }
  };
  
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };
  
  return {
    isRecording,
    isProcessing,
    startRecording,
    stopRecording
  };
};
```

## 8. Solana 钱包集成

### 8.1 钱包提供者配置

```tsx
// SolanaWalletProvider.tsx
import React, { useMemo } from 'react';
import {
  ConnectionProvider,
  WalletProvider,
} from '@solana/wallet-adapter-react';
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { PhantomWalletAdapter } from '@solana/wallet-adapter-wallets';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import { clusterApiUrl } from '@solana/web3.js';

// 导入默认样式
import '@solana/wallet-adapter-react-ui/styles.css';

export const SolanaWalletProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 可根据环境变量更改网络
  const network = WalletAdapterNetwork.Devnet;
  const endpoint = useMemo(() => clusterApiUrl(network), [network]);
  
  // 初期只支持 Phantom 钱包
  const wallets = useMemo(() => [new PhantomWalletAdapter()], []);
  
  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={wallets} autoConnect>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  );
};
```

### 8.2 钱包连接组件

```tsx
// WalletConnector.tsx
import React from 'react';
import styled from 'styled-components';
import { useWallet } from '@solana/wallet-adapter-react';
import { useWalletModal } from '@solana/wallet-adapter-react-ui';
import { truncateAddress } from '../utils/address';

const ConnectButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
`;

const WalletInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const Address = styled.span`
  font-family: monospace;
`;

export const WalletConnector: React.FC = () => {
  const { wallet, publicKey, connected, disconnect } = useWallet();
  const { setVisible } = useWalletModal();
  
  const handleConnect = () => {
    setVisible(true);
  };
  
  const handleDisconnect = () => {
    disconnect();
  };
  
  if (connected && publicKey) {
    return (
      <WalletInfo>
        <Address>{truncateAddress(publicKey.toString())}</Address>
        <ConnectButton onClick={handleDisconnect}>断开钱包</ConnectButton>
      </WalletInfo>
    );
  }
  
  return (
    <ConnectButton onClick={handleConnect}>
      连接钱包
    </ConnectButton>
  );
};
```

## 9. Donny 交互与状态管理

### 9.1 Donny 状态管理

```tsx
// donnySlice.ts (Redux Toolkit)
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { DonnyType } from '../types/donny';

type DonnyState = {
  activeDonny: DonnyType | null;
  selectedBusinessDonny: DonnyType | null;
  isSwitchingDonny: boolean;
  interactionState: {
    hasDreamSubmitted: boolean;
    pendingFeedback: boolean;
    dreamInterpretationCompleted: boolean;
  };
};

const initialState: DonnyState = {
  activeDonny: 'almighty',
  selectedBusinessDonny: null,
  isSwitchingDonny: false,
  interactionState: {
    hasDreamSubmitted: false,
    pendingFeedback: false,
    dreamInterpretationCompleted: false,
  },
};

export const donnySlice = createSlice({
  name: 'donny',
  initialState,
  reducers: {
    selectBusinessDonny: (state, action: PayloadAction<DonnyType>) => {
      state.selectedBusinessDonny = action.payload;
    },
    setActiveDonny: (state, action: PayloadAction<DonnyType | null>) => {
      state.activeDonny = action.payload;
    },
    startDonnySwitching: (state) => {
      state.isSwitchingDonny = true;
    },
    finishDonnySwitching: (state) => {
      state.isSwitchingDonny = false;
      state.activeDonny = state.selectedBusinessDonny;
    },

    setDreamSubmitted: (state, action: PayloadAction<boolean>) => {
      state.interactionState.hasDreamSubmitted = action.payload;
    },
    setDreamInterpretationCompleted: (state, action: PayloadAction<boolean>) => {
      state.interactionState.dreamInterpretationCompleted = action.payload;
    },
    setPendingFeedback: (state, action: PayloadAction<boolean>) => {
      state.interactionState.pendingFeedback = action.payload;
    },
    resetInteractionState: (state) => {
      state.interactionState = initialState.interactionState;
      state.selectedBusinessDonny = null;
      state.activeDonny = 'almighty';
    },
  },
});

export const {
  selectBusinessDonny,
  setActiveDonny,
  startDonnySwitching,
  finishDonnySwitching,

  setDreamSubmitted,
  setDreamInterpretationCompleted,
  setPendingFeedback,
  resetInteractionState,
} = donnySlice.actions;

export default donnySlice.reducer;
```

### 9.2 聊天状态管理

```tsx
// chatSlice.ts (Redux Toolkit)
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

type Message = {
  id: string;
  sender: 'user' | 'donny';
  donnyType?: string;
  content: string;
  timestamp: number;
  isVoiceInput?: boolean;
};

type ChatState = {
  messages: Record<string, Message[]>; // donnyType -> messages
  isTyping: boolean;
};

const initialState: ChatState = {
  messages: {
    almighty: [],
  },
  isTyping: false,
};

export const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action: PayloadAction<Omit<Message, 'id' | 'timestamp'>>) => {
      const { sender, content, donnyType = 'almighty', isVoiceInput } = action.payload;
      const id = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const timestamp = Date.now();
      
      if (!state.messages[donnyType]) {
        state.messages[donnyType] = [];
      }
      
      state.messages[donnyType].push({
        id,
        sender,
        content,
        timestamp,
        donnyType,
        isVoiceInput,
      });
    },
    setTyping: (state, action: PayloadAction<boolean>) => {
      state.isTyping = action.payload;
    },
    clearChatHistory: (state, action: PayloadAction<string>) => {
      const donnyType = action.payload;
      state.messages[donnyType] = [];
    },
  },
});

export const { addMessage, setTyping, clearChatHistory } = chatSlice.actions;

export default chatSlice.reducer;
```

## 10. 路由和页面结构

```tsx
// App.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { Provider } from 'react-redux';
import { SolanaWalletProvider } from './providers/SolanaWalletProvider';
import { store } from './store';
import { GlobalStyle } from './styles/GlobalStyle';
import { theme } from './styles/theme';
import { AuthGuard } from './components/auth/AuthGuard';
import { MainLayout } from './components/layout/MainLayout';
import HomePage from './pages/HomePage';
import NotFoundPage from './pages/NotFoundPage';

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <GlobalStyle />
        <SolanaWalletProvider>
          <Router>
            <Routes>
              <Route path="/" element={
                <AuthGuard>
                  <MainLayout />
                </AuthGuard>
              }>
                <Route index element={<HomePage />} />
              </Route>
              <Route path="/404" element={<NotFoundPage />} />
              <Route path="*" element={<Navigate to="/404" replace />} />
            </Routes>
          </Router>
        </SolanaWalletProvider>
      </ThemeProvider>
    </Provider>
  );
};

export default App;
```

## 11. 响应式设计实现

```tsx
// 在 theme.ts 中定义断点
export const breakpoints = {
  xs: '480px',
  sm: '768px',
  md: '992px',
  lg: '1200px',
  xl: '1600px',
};

// 媒体查询工具
export const media = {
  xs: `@media (max-width: ${breakpoints.xs})`,
  sm: `@media (max-width: ${breakpoints.sm})`,
  md: `@media (max-width: ${breakpoints.md})`,
  lg: `@media (max-width: ${breakpoints.lg})`,
  xl: `@media (max-width: ${breakpoints.xl})`,
};

// 在组件中使用
const ResponsiveContainer = styled.div`
  width: 70vw;
  
  ${media.lg} {
    width: 80vw;
  }
  
  ${media.md} {
    width: 90vw;
  }
  
  ${media.sm} {
    width: 95vw;
    flex-direction: column;
  }
`;
```

## 12. 性能优化建议

* 使用 React.memo() 缓存组件
* 使用 useMemo() 和 useCallback() 缓存计算值和函数
* 实现代码分割 (React.lazy() 和 Suspense)
* 优化依赖数组，避免不必要的重渲染
* 优化大列表渲染 (react-window 或 react-virtualized)
* 延迟加载图片和非关键资源
* 使用 Web Workers 处理复杂计算
* 预加载关键资源

## 13. 测试策略

* 使用 Jest 和 React Testing Library 编写单元测试
* 为关键组件和页面编写快照测试
* 使用 Mock Service Worker 模拟 API 请求
* 使用 Cypress 编写端到端测试
* 实现视觉回归测试 (如 Percy)

## 14. 部署流程

* 构建优化 (Tree Shaking, 代码分割, 懒加载)
* 静态资源压缩和优化
* CDN 部署静态资源
* 实现 CI/CD 流水线
* 部署前的性能和安全检查