import { API_BASE_URL, getHeaders, handleResponse, getToken } from './config';

// 获取用户的所有梦境
export const getUserDreams = async () => {
  const token = getToken();
  if (!token) throw new Error('No authentication token');
  
  const response = await fetch(`${API_BASE_URL}/dreams`, {
    method: 'GET',
    headers: getHeaders(token),
  });
  
  return handleResponse(response);
};

// 创建新梦境
export const createDream = async (content: string) => {
  const token = getToken();
  if (!token) throw new Error('No authentication token');
  
  const response = await fetch(`${API_BASE_URL}/dreams`, {
    method: 'POST',
    headers: getHeaders(token),
    body: JSON.stringify({
      content,
    }),
  });
  
  return handleResponse(response);
};

// 获取特定梦境
export const getDreamById = async (dreamId: string) => {
  const token = getToken();
  if (!token) throw new Error('No authentication token');
  
  const response = await fetch(`${API_BASE_URL}/dreams/${dreamId}`, {
    method: 'GET',
    headers: getHeaders(token),
  });
  
  return handleResponse(response);
};

// 更新梦境解析
export const updateDreamInterpretation = async (dreamId: string, interpretation: string, donnyType: string) => {
  const token = getToken();
  if (!token) throw new Error('No authentication token');
  
  const response = await fetch(`${API_BASE_URL}/dreams/${dreamId}/interpretation`, {
    method: 'PATCH',
    headers: getHeaders(token),
    body: JSON.stringify({
      interpretation,
      donnyType,
    }),
  });
  
  return handleResponse(response);
};

// 分享梦境
export const shareDream = async (dreamId: string) => {
  const token = getToken();
  if (!token) throw new Error('No authentication token');
  
  const response = await fetch(`${API_BASE_URL}/dreams/${dreamId}/share`, {
    method: 'PATCH',
    headers: getHeaders(token),
  });
  
  return handleResponse(response);
};
