# Donny 项目规则文档

## 1. 项目概述

Donny 是一个基于 Web3 技术的解梦平台，融合人工智能对话技术与 Solana 区块链特性，为用户提供个性化、多维度的梦境解析服务。本文档旨在规范项目的编码标准、协作流程和质量控制，确保开发团队高效协作，保持代码质量和一致性。

## 2. 代码仓库管理

### 2.1 仓库结构

项目采用单体仓库 (Monorepo) 结构，主要包含以下目录：

```
donny-eliza/
├── docs/                  # 项目文档
├── frontend/              # 前端代码
├── backend/               # 后端服务
│   ├── api-gateway/       # API 网关服务
│   ├── ai-service/        # AI Agent 服务
│   ├── stt-service/       # 语音转文本服务
│   ├── solana-service/    # Solana 交互服务
│   ├── user-service/      # 用户服务
│   └── knowledge-service/ # 知识库服务
├── smart-contracts/       # Solana 智能合约
├── scripts/               # 项目脚本
└── tests/                 # 独立测试目录
```

### 2.2 分支管理策略

采用 GitHub Flow 分支管理策略：

* `main`：主分支，始终保持可部署状态
* 特性分支：从 `main` 创建，命名格式 `feature/描述` 或 `bugfix/描述`

### 2.3 提交规范

提交信息必须遵循以下格式：

```
<类型>(<范围>): <简短描述>

<详细描述>

<关联的任务或问题>
```

类型包括：
* `feat`：新功能
* `fix`：Bug修复
* `docs`：文档变更
* `style`：代码风格变更（不影响功能）
* `refactor`：重构
* `perf`：性能优化
* `test`：测试相关
* `chore`：构建过程或辅助工具变动

### 2.4 Pull Request 流程

1. 在创建 PR 前，确保代码已经过测试
2. PR 描述应清晰说明变更内容、影响和测试结果
3. 所有 PR 必须经过至少一名团队成员的代码审查
4. 通过代码审查和 CI 测试后方可合并

## 3. 编码规范

### 3.1 通用规范

* 缩进：使用 2 个空格（不使用制表符）
* 行宽：不超过 100 个字符
* 文件编码：UTF-8
* 行尾：LF（Unix风格）
* 代码块结束：不使用额外的分号

### 3.2 前端编码规范

#### 3.2.1 React/JavaScript 规范

* 使用 ES6+ 语法标准
* 使用 React 函数组件和 Hooks
* 使用 TypeScript 进行类型检查
* Props 命名使用小驼峰 (camelCase)
* 组件命名使用大驼峰 (PascalCase)
* 避免内联样式，使用 CSS-in-JS 解决方案
* 组件文件与组件名保持一致，如 `Button.tsx`

#### 3.2.2 样式规范

* 使用 Styled Components 或 Tailwind CSS
* 颜色使用设计系统中定义的变量，不直接使用硬编码颜色
* 使用媒体查询确保响应式设计

#### 3.2.3 状态管理

* 使用 Redux Toolkit 或 Zustand 进行全局状态管理
* 局部状态使用 React 内置的 useState 和 useReducer
* 遵循数据不可变性原则

### 3.3 后端编码规范

#### 3.3.1 API 设计规范

* 遵循 RESTful 设计原则
* 使用 HTTP 状态码表示请求结果
* API 版本控制：URL 前缀方式，如 `/api/v1/`
* API 返回格式统一：`{ success: boolean, data: any, error?: string }`

#### 3.3.2 服务设计原则

* 遵循单一职责原则
* 使用依赖注入模式
* 模块间通过定义良好的接口通信
* 服务内部逻辑分层：控制器、服务、数据访问

#### 3.3.3 数据库访问

* 使用 ORM/ODM 或查询构建器，避免直接写 SQL
* 数据库表名：小写，多个单词用下划线连接
* 字段命名：小驼峰命名法

### 3.4 Solana 合约规范

* 合约命名清晰表明功能
* 使用 Rust 编写，遵循 Rust 标准编码规范
* 所有公开函数必须有完整的注释
* 严格控制权限访问
* 实现合适的错误处理机制

## 4. 文档规范

### 4.1 注释规范

* 代码注释使用 JSDoc 风格
* 所有公共 API 和函数必须有完整注释
* 复杂算法或业务逻辑必须有详细说明
* 避免无意义的注释

### 4.2 文档类型

* README.md：项目概述和快速开始指南
* API 文档：使用 OpenAPI/Swagger
* 架构文档：描述系统设计和组件交互
* 开发指南：环境设置和开发流程

## 5. 测试规范

### 5.1 测试类型与覆盖率

* 单元测试：覆盖率目标 > 80%
* 集成测试：关键服务间交互
* 端到端测试：核心用户流程
* 性能测试：重要 API 和流程

### 5.2 测试工具

* 前端：Jest, React Testing Library, Cypress
* 后端：Jest, Supertest
* 契约测试：Pact
* 负载测试：k6

### 5.3 测试命名

测试文件命名：`[被测文件名].test.[js|ts]`
测试用例命名：描述预期行为，如 `should return user when valid ID is provided`

## 6. CI/CD 规范

### 6.1 CI 流程

每个 Pull Request 触发以下流程：
* 代码格式检查
* 静态代码分析
* 单元测试与集成测试
* 构建验证

### 6.2 CD 流程

* `main` 分支成功构建后自动部署到测试环境
* 生产环境部署需手动批准
* 遵循蓝绿部署或金丝雀发布策略

## 7. 代码评审规范

### 7.1 评审重点

* 代码正确性：是否实现了预期功能
* 代码质量：可读性、可维护性、性能
* 安全性：是否存在安全漏洞
* 测试覆盖：是否有足够的测试

### 7.2 评审反馈准则

* 保持客观，聚焦代码而非人
* 明确说明问题和改进建议
* 区分必要修改和可选建议
* 对好的实践给予肯定

## 8. 环境管理

### 8.1 环境类型

* 本地开发环境
* 集成测试环境
* 临时测试网环境（Solana Devnet）
* 生产环境（Solana Mainnet）

### 8.2 环境配置管理

* 使用环境变量管理配置
* 敏感配置通过密钥管理服务处理
* 不同环境的配置分离存储
* 本地开发使用 `.env` 文件（不提交到版本控制）

## 9. 安全规范

### 9.1 前端安全

* 防止 XSS：验证和转义用户输入
* 使用 HTTPS
* 实现正确的 CORS 策略
* 非敏感数据才使用本地存储

### 9.2 后端安全

* 输入验证与净化
* 防 SQL 注入
* 合理的认证与授权机制
* 敏感数据加密存储
* 适当的日志级别（不记录敏感信息）

### 9.3 Web3 安全

* 私钥安全管理
* 交易签名验证
* 智能合约审计
* 交易金额限制
* 多重签名机制

## 10. 性能优化准则

### 10.1 前端性能

* 按需加载/代码分割
* 资源优化（图片压缩、CSS/JS 最小化）
* 客户端缓存策略
* 减少网络请求
* 避免主线程阻塞

### 10.2 后端性能

* 合理的数据库索引
* 查询优化
* 数据缓存策略
* 并发和异步处理
* 资源池化
* 负载均衡

## 11. 可访问性规范

* 符合 WCAG 2.1 AA 级标准
* 键盘导航支持
* 屏幕阅读器兼容
* 颜色对比度符合标准
* 响应式设计适配多种设备

## 12. 国际化与本地化

* 使用 i18n 库进行文本管理
* 所有用户可见文本应使用翻译键
* 日期、货币等格式化考虑地区差异
* 支持 RTL 语言（如需要）

## 13. 项目协作流程

### 13.1 任务管理

* 使用 Jira/GitHub Projects 进行任务管理
* 任务状态：待办、进行中、审查中、已完成
* 优先级分级：紧急、高、中、低

### 13.2 沟通渠道

* 日常沟通：Slack/Teams
* 视频会议：Zoom/Teams
* 文档协作：Notion/Google Docs
* 代码相关讨论：GitHub 议题和 PR 评论

### 13.3 会议机制

* 日站会：每日简短同步，讨论进展和阻碍
* 迭代计划会：确定下一迭代的工作范围
* 迭代评审：展示完成的功能
* 迭代回顾：总结经验，改进流程

## 14. 发布管理

### 14.1 版本号规范

采用语义化版本 (SemVer) 规范：
* 主版本号：不兼容的 API 变更
* 次版本号：向后兼容的功能性新增
* 修订号：向后兼容的问题修正

### 14.2 发布流程

1. 创建发布分支 `release/vX.Y.Z`
2. 在发布分支上进行版本更新和最终测试
3. 发布分支合并到 `main`
4. 在 `main` 上创建版本标签
5. 部署到生产环境

### 14.3 发布记录

每次发布需提供详细的变更记录，包括：
* 新增功能
* 修复的问题
* 已知问题
* 升级注意事项

## 15. 技术债务管理

* 定期评估并记录技术债务
* 在每个迭代中分配部分时间处理技术债务
* 重构前必须有足够的测试覆盖
* 大型重构需提前规划并分阶段实施 