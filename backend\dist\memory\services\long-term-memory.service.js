"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LongTermMemoryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LongTermMemoryService = void 0;
const common_1 = require("@nestjs/common");
const memory_types_1 = require("../memory.types");
const database_service_1 = require("../../database/database.service");
const embedding_service_1 = require("../../knowledge/embedding.service");
let LongTermMemoryService = LongTermMemoryService_1 = class LongTermMemoryService {
    constructor(databaseService, embeddingService) {
        this.databaseService = databaseService;
        this.embeddingService = embeddingService;
        this.logger = new common_1.Logger(LongTermMemoryService_1.name);
        this.archivalThreshold = 90;
        this.emotionalSignificanceThreshold = 0.7;
        this.logger.log('长期记忆服务初始化完成');
    }
    async store(memoryData) {
        this.logger.debug(`存储长期记忆 - 用户: ${memoryData.userId}, Agent: ${memoryData.agentType}`);
        try {
            const embedding = await this.generateEmbedding(memoryData.content);
            const emotionalSignificance = this.calculateEmotionalSignificance(memoryData);
            const memory = {
                ...memoryData,
                id: memoryData.id || `ltm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                memoryType: memory_types_1.MemoryType.LONG_TERM,
                consolidationDate: memoryData.consolidationDate || new Date(),
                archivalStatus: memoryData.archivalStatus || 'active',
                emotionalSignificance,
                narrativeContext: memoryData.narrativeContext || this.generateNarrativeContext(memoryData),
                embedding,
                accessCount: 0,
                createdAt: new Date(),
                updatedAt: new Date(),
                lastAccessedAt: new Date(),
                expiresAt: memoryData.expiresAt,
            };
            const { data, error } = await this.databaseService.executeWithPermission(memory.agentType, {
                table: 'agent_memories',
                operation: 'insert',
                data: {
                    id: memory.id,
                    agent_type: memory.agentType,
                    user_id: memory.userId,
                    session_id: memory.sessionId,
                    memory_type: memory.memoryType,
                    importance: memory.importance,
                    content: memory.content,
                    metadata: {
                        ...memory.metadata,
                        consolidationDate: memory.consolidationDate.toISOString(),
                        archivalStatus: memory.archivalStatus,
                        emotionalSignificance: memory.emotionalSignificance,
                        narrativeContext: memory.narrativeContext,
                    },
                    embedding: memory.embedding,
                    access_count: memory.accessCount,
                    last_accessed_at: memory.lastAccessedAt.toISOString(),
                    created_at: memory.createdAt.toISOString(),
                    updated_at: memory.updatedAt.toISOString(),
                    expires_at: memory.expiresAt?.toISOString(),
                },
            });
            if (error) {
                throw new Error(`数据库存储失败: ${error.message}`);
            }
            this.logger.debug(`长期记忆存储成功 - ID: ${memory.id}`);
            return memory;
        }
        catch (error) {
            this.logger.error(`存储长期记忆失败: ${error.message}`);
            throw error;
        }
    }
    async retrieve(query) {
        this.logger.debug(`检索长期记忆 - 查询: ${JSON.stringify(query)}`);
        try {
            let results = [];
            if (query.searchText || query.embedding) {
                results = await this.semanticSearch(query);
            }
            else {
                results = await this.queryDatabase(query);
            }
            results.sort((a, b) => {
                const scoreA = a.importance * 0.6 + a.emotionalSignificance * 0.4;
                const scoreB = b.importance * 0.6 + b.emotionalSignificance * 0.4;
                return scoreB - scoreA;
            });
            const limitedResults = query.limit
                ? results.slice(query.offset || 0, (query.offset || 0) + query.limit)
                : results;
            await this.updateAccessRecords(limitedResults);
            return limitedResults;
        }
        catch (error) {
            this.logger.error(`检索长期记忆失败: ${error.message}`);
            throw error;
        }
    }
    async semanticSearch(query) {
        this.logger.debug('执行长期记忆语义搜索');
        try {
            let queryEmbedding;
            if (query.embedding) {
                queryEmbedding = query.embedding;
            }
            else if (query.searchText) {
                const response = await this.embeddingService.generateEmbedding({ text: query.searchText });
                queryEmbedding = response.embedding;
            }
            else {
                throw new Error('语义搜索需要提供搜索文本或向量');
            }
            const filters = {
                memory_type: memory_types_1.MemoryType.LONG_TERM,
                embedding: { $ne: null },
                'metadata->archivalStatus': { $ne: 'archived' },
            };
            if (query.agentType)
                filters.agent_type = query.agentType;
            if (query.userId)
                filters.user_id = query.userId;
            if (query.category)
                filters['metadata->category'] = query.category;
            if (query.importance)
                filters.importance = query.importance;
            const { data, error } = await this.databaseService.getClient()
                .from('agent_memories')
                .select('*')
                .match(filters)
                .gt('embedding <-> $1', query.similarityThreshold || 0.85)
                .order('embedding <-> $1')
                .limit(query.limit || 50);
            if (error) {
                throw new Error(`向量搜索失败: ${error.message}`);
            }
            return (data || []).map(row => this.mapDbRowToMemory(row));
        }
        catch (error) {
            this.logger.error(`语义搜索失败: ${error.message}`);
            return await this.queryDatabase(query);
        }
    }
    async getUserProfile(agentType, userId) {
        this.logger.debug(`获取用户档案 - Agent: ${agentType}, 用户: ${userId}`);
        try {
            const baseQuery = { agentType, userId, memoryType: memory_types_1.MemoryType.LONG_TERM };
            const [preferences, personalInfo, importantEvents, emotionalPatterns] = await Promise.all([
                this.retrieve({ ...baseQuery, category: 'preference', limit: 20 }),
                this.retrieve({ ...baseQuery, category: 'personal_info', limit: 10 }),
                this.retrieve({
                    ...baseQuery,
                    category: 'important_event',
                    importance: memory_types_1.MemoryImportance.HIGH,
                    limit: 15
                }),
                this.retrieve({
                    ...baseQuery,
                    category: 'emotion',
                    limit: 10
                }).then(memories => memories.filter(m => m.emotionalSignificance >= this.emotionalSignificanceThreshold)),
            ]);
            return {
                preferences,
                personalInfo,
                importantEvents,
                emotionalPatterns,
            };
        }
        catch (error) {
            this.logger.error(`获取用户档案失败: ${error.message}`);
            throw error;
        }
    }
    async update(memoryId, updates) {
        this.logger.debug(`更新长期记忆 - ID: ${memoryId}`);
        try {
            const existing = await this.findById(memoryId);
            if (!existing) {
                throw new Error(`长期记忆不存在: ${memoryId}`);
            }
            let embedding = existing.embedding;
            let narrativeContext = existing.narrativeContext;
            if (updates.content && updates.content !== existing.content) {
                embedding = await this.generateEmbedding(updates.content);
                narrativeContext = this.generateNarrativeContext({ ...existing, ...updates });
            }
            const updated = {
                ...existing,
                ...updates,
                embedding,
                narrativeContext,
                updatedAt: new Date(),
            };
            const { error } = await this.databaseService.executeWithPermission(updated.agentType, {
                table: 'agent_memories',
                operation: 'update',
                data: {
                    content: updated.content,
                    metadata: {
                        ...updated.metadata,
                        consolidationDate: updated.consolidationDate.toISOString(),
                        archivalStatus: updated.archivalStatus,
                        emotionalSignificance: updated.emotionalSignificance,
                        narrativeContext: updated.narrativeContext,
                    },
                    importance: updated.importance,
                    embedding: updated.embedding,
                    access_count: updated.accessCount,
                    last_accessed_at: updated.lastAccessedAt.toISOString(),
                    updated_at: updated.updatedAt.toISOString(),
                },
                filters: { id: memoryId },
            });
            if (error) {
                throw new Error(`数据库更新失败: ${error.message}`);
            }
            return updated;
        }
        catch (error) {
            this.logger.error(`更新长期记忆失败: ${error.message}`);
            throw error;
        }
    }
    async delete(memoryId) {
        this.logger.debug(`删除长期记忆 - ID: ${memoryId}`);
        try {
            const { error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'delete',
                filters: { id: memoryId },
            });
            if (error) {
                this.logger.warn(`数据库删除失败: ${error.message}`);
                return false;
            }
            return true;
        }
        catch (error) {
            this.logger.error(`删除长期记忆失败: ${error.message}`);
            return false;
        }
    }
    async findById(memoryId) {
        try {
            const { data, error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'select',
                filters: {
                    id: memoryId,
                    memory_type: memory_types_1.MemoryType.LONG_TERM,
                },
            });
            if (error || !data || data.length === 0) {
                return null;
            }
            return this.mapDbRowToMemory(data[0]);
        }
        catch (error) {
            this.logger.error(`查找长期记忆失败: ${error.message}`);
            return null;
        }
    }
    async archiveOldMemories() {
        this.logger.log('开始归档旧的长期记忆');
        try {
            const cutoffDate = new Date(Date.now() - this.archivalThreshold * 24 * 60 * 60 * 1000);
            const { data: candidates, error: queryError } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'select',
                filters: {
                    memory_type: memory_types_1.MemoryType.LONG_TERM,
                    created_at: { $lt: cutoffDate.toISOString() },
                    importance: { $lt: memory_types_1.MemoryImportance.CRITICAL },
                    'metadata->archivalStatus': 'active',
                    access_count: { $lt: 5 },
                },
            });
            if (queryError) {
                throw new Error(`查询归档候选失败: ${queryError.message}`);
            }
            if (!candidates || candidates.length === 0) {
                this.logger.log('没有找到需要归档的记忆');
                return 0;
            }
            let archivedCount = 0;
            for (const candidate of candidates) {
                try {
                    await this.update(candidate.id, { archivalStatus: 'archived' });
                    archivedCount++;
                }
                catch (error) {
                    this.logger.warn(`归档记忆失败 - ID: ${candidate.id}, 错误: ${error.message}`);
                }
            }
            this.logger.log(`归档完成 - 共归档${archivedCount}条记忆`);
            return archivedCount;
        }
        catch (error) {
            this.logger.error(`归档旧记忆失败: ${error.message}`);
            return 0;
        }
    }
    async getStats(agentType, userId) {
        try {
            const filters = { memory_type: memory_types_1.MemoryType.LONG_TERM };
            if (agentType)
                filters.agent_type = agentType;
            if (userId)
                filters.user_id = userId;
            const { data, error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'select',
                columns: 'importance, metadata, created_at, LENGTH(content::text) as content_size',
                filters,
            });
            if (error) {
                throw new Error(`获取统计失败: ${error.message}`);
            }
            const memories = data || [];
            const stats = {
                total: memories.length,
                byImportance: {},
                byCategory: {},
                storageUsed: 0,
                averageImportance: 0,
                oldestMemory: undefined,
                newestMemory: undefined,
            };
            if (memories.length === 0) {
                return stats;
            }
            let totalImportance = 0;
            let oldestDate = new Date();
            let newestDate = new Date(0);
            for (const memory of memories) {
                const importance = memory.importance || memory_types_1.MemoryImportance.NORMAL;
                stats.byImportance[importance] = (stats.byImportance[importance] || 0) + 1;
                totalImportance += importance;
                const category = memory.metadata?.category || 'general';
                stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
                stats.storageUsed += memory.content_size || 0;
                const createdAt = new Date(memory.created_at);
                if (createdAt < oldestDate)
                    oldestDate = createdAt;
                if (createdAt > newestDate)
                    newestDate = createdAt;
            }
            stats.averageImportance = totalImportance / memories.length;
            stats.oldestMemory = oldestDate;
            stats.newestMemory = newestDate;
            return stats;
        }
        catch (error) {
            this.logger.error(`获取统计信息失败: ${error.message}`);
            throw error;
        }
    }
    async generateEmbedding(content) {
        try {
            const text = typeof content === 'string' ? content : JSON.stringify(content);
            const response = await this.embeddingService.generateEmbedding({ text });
            return response.embedding;
        }
        catch (error) {
            this.logger.warn(`生成向量失败: ${error.message}`);
            return [];
        }
    }
    calculateEmotionalSignificance(memoryData) {
        let significance = 0.5;
        if (memoryData.metadata?.sentiment) {
            significance += Math.abs(memoryData.metadata.sentiment) * 0.3;
        }
        if (memoryData.importance) {
            significance += (memoryData.importance - 1) * 0.15;
        }
        if (memoryData.metadata?.tags) {
            const emotionalTags = ['joy', 'sadness', 'anger', 'fear', 'love', 'hate', 'excitement'];
            const hasEmotionalTag = memoryData.metadata.tags.some(tag => emotionalTags.includes(tag.toLowerCase()));
            if (hasEmotionalTag)
                significance += 0.2;
        }
        if (memoryData.metadata?.category === 'emotion') {
            significance += 0.1;
        }
        return Math.min(significance, 1.0);
    }
    generateNarrativeContext(memoryData) {
        const parts = [];
        if (memoryData.createdAt) {
            parts.push(`记录于${memoryData.createdAt.toLocaleDateString()}`);
        }
        if (memoryData.importance) {
            const importanceDesc = {
                [memory_types_1.MemoryImportance.LOW]: '日常记忆',
                [memory_types_1.MemoryImportance.NORMAL]: '普通记忆',
                [memory_types_1.MemoryImportance.HIGH]: '重要记忆',
                [memory_types_1.MemoryImportance.CRITICAL]: '关键记忆',
                [memory_types_1.MemoryImportance.PERMANENT]: '永久记忆',
            };
            parts.push(importanceDesc[memoryData.importance]);
        }
        if (memoryData.metadata?.category) {
            const categoryDesc = {
                preference: '个人偏好',
                personal_info: '个人信息',
                emotion: '情感体验',
                important_event: '重要事件',
                conversation: '对话记录',
                general: '一般记忆',
            };
            parts.push(categoryDesc[memoryData.metadata.category] || memoryData.metadata.category);
        }
        if (memoryData.metadata?.sentiment) {
            if (memoryData.metadata.sentiment > 0.3) {
                parts.push('积极情感');
            }
            else if (memoryData.metadata.sentiment < -0.3) {
                parts.push('消极情感');
            }
            else {
                parts.push('中性情感');
            }
        }
        return parts.join(' | ');
    }
    async updateAccessRecords(memories) {
        for (const memory of memories) {
            try {
                await this.update(memory.id, {
                    accessCount: memory.accessCount + 1,
                    lastAccessedAt: new Date(),
                });
            }
            catch (error) {
                this.logger.warn(`更新访问记录失败 - 记忆ID: ${memory.id}, 错误: ${error.message}`);
            }
        }
    }
    async queryDatabase(query) {
        const filters = { memory_type: memory_types_1.MemoryType.LONG_TERM };
        if (query.agentType)
            filters.agent_type = query.agentType;
        if (query.userId)
            filters.user_id = query.userId;
        if (query.sessionId)
            filters.session_id = query.sessionId;
        if (query.category)
            filters['metadata->category'] = query.category;
        if (query.importance)
            filters.importance = query.importance;
        if (query.timeRange) {
            filters.created_at = {
                $gte: query.timeRange.start.toISOString(),
                $lte: query.timeRange.end.toISOString(),
            };
        }
        const { data, error } = await this.databaseService.executeWithPermission(query.agentType || 'almighty', {
            table: 'agent_memories',
            operation: 'select',
            filters,
        });
        if (error) {
            throw new Error(`数据库查询失败: ${error.message}`);
        }
        return (data || []).map(row => this.mapDbRowToMemory(row));
    }
    mapDbRowToMemory(row) {
        return {
            id: row.id,
            agentType: row.agent_type,
            userId: row.user_id,
            sessionId: row.session_id,
            memoryType: row.memory_type,
            importance: row.importance,
            content: row.content,
            metadata: row.metadata,
            embedding: row.embedding,
            consolidationDate: new Date(row.metadata?.consolidationDate || row.created_at),
            archivalStatus: row.metadata?.archivalStatus || 'active',
            emotionalSignificance: row.metadata?.emotionalSignificance || 0.5,
            narrativeContext: row.metadata?.narrativeContext || '',
            accessCount: row.access_count || 0,
            lastAccessedAt: new Date(row.last_accessed_at),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at),
            expiresAt: row.expires_at ? new Date(row.expires_at) : undefined,
        };
    }
};
exports.LongTermMemoryService = LongTermMemoryService;
exports.LongTermMemoryService = LongTermMemoryService = LongTermMemoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService,
        embedding_service_1.EmbeddingService])
], LongTermMemoryService);
//# sourceMappingURL=long-term-memory.service.js.map