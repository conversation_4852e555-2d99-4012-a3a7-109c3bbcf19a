import supabase from './supabase';
import { Message } from '../types';
import { AppError } from '../middlewares/errorHandler';

// 创建新消息
export const createMessage = async (
  userId: string,
  content: string,
  sender: 'user' | 'donny',
  donnyType: string,
  dreamId?: string
): Promise<Message> => {
  const { data, error } = await supabase
    .from('messages')
    .insert([
      { 
        user_id: userId,
        dream_id: dreamId,
        content,
        sender,
        donny_type: donnyType,
      }
    ])
    .select()
    .single();
  
  if (error) {
    throw new AppError(`Error creating message: ${error.message}`, 500);
  }
  
  if (!data) {
    throw new AppError('Failed to create message', 500);
  }
  
  return {
    id: data.id,
    userId: data.user_id,
    dreamId: data.dream_id,
    content: data.content,
    sender: data.sender,
    donnyType: data.donny_type,
    createdAt: new Date(data.created_at),
  };
};

// 获取用户与特定Donny的对话历史
export const getUserDonnyMessages = async (
  userId: string,
  donnyType: string,
  limit: number = 50,
  before?: Date
): Promise<Message[]> => {
  let query = supabase
    .from('messages')
    .select('*')
    .eq('user_id', userId)
    .eq('donny_type', donnyType)
    .order('created_at', { ascending: false })
    .limit(limit);
  
  if (before) {
    query = query.lt('created_at', before.toISOString());
  }
  
  const { data, error } = await query;
  
  if (error) {
    throw new AppError(`Error fetching messages: ${error.message}`, 500);
  }
  
  return data.map(message => ({
    id: message.id,
    userId: message.user_id,
    dreamId: message.dream_id,
    content: message.content,
    sender: message.sender,
    donnyType: message.donny_type,
    createdAt: new Date(message.created_at),
  })).reverse(); // 返回按时间升序排列的消息
};

// 获取特定梦境的对话历史
export const getDreamMessages = async (dreamId: string): Promise<Message[]> => {
  const { data, error } = await supabase
    .from('messages')
    .select('*')
    .eq('dream_id', dreamId)
    .order('created_at', { ascending: true });
  
  if (error) {
    throw new AppError(`Error fetching dream messages: ${error.message}`, 500);
  }
  
  return data.map(message => ({
    id: message.id,
    userId: message.user_id,
    dreamId: message.dream_id,
    content: message.content,
    sender: message.sender,
    donnyType: message.donny_type,
    createdAt: new Date(message.created_at),
  }));
};
