"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DataIsolationMiddleware_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataIsolationMiddleware = void 0;
exports.RequirePermission = RequirePermission;
const common_1 = require("@nestjs/common");
let DataIsolationMiddleware = DataIsolationMiddleware_1 = class DataIsolationMiddleware {
    constructor() {
        this.logger = new common_1.Logger(DataIsolationMiddleware_1.name);
    }
    async use(req, res, next) {
        try {
            if (!this.isAgentRequest(req)) {
                return next();
            }
            const agentInfo = this.extractAgentInfo(req);
            if (!agentInfo) {
                this.logger.warn('无法提取Agent信息');
                throw new common_1.ForbiddenException('无效的Agent请求');
            }
            req.agent = agentInfo;
            const userInfo = this.extractUserInfo(req);
            req.user = userInfo;
            const routePermission = this.checkRoutePermission(req, agentInfo);
            if (!routePermission.allowed) {
                this.logger.warn(`路由访问被拒绝: ${routePermission.reason}`);
                throw new common_1.ForbiddenException(routePermission.reason);
            }
            this.applyDataIsolationFilters(req, agentInfo);
            this.setupResponseFiltering(req, res, agentInfo);
            next();
        }
        catch (error) {
            this.logger.error(`数据隔离中间件错误: ${error.message}`);
            if (error instanceof common_1.ForbiddenException) {
                throw error;
            }
            throw new common_1.ForbiddenException('数据访问权限验证失败');
        }
    }
    isAgentRequest(req) {
        const agentRoutes = [
            '/business-agents',
            '/agent',
            '/knowledge',
            '/conversation',
        ];
        return agentRoutes.some(route => req.path.startsWith(route));
    }
    extractAgentInfo(req) {
        const pathMatch = req.path.match(/\/(taoist|freud|papa|almighty)(?:$|\/)/);
        if (pathMatch) {
            const agentType = pathMatch[1];
            return {
                type: agentType,
                id: `${agentType}-agent`,
                userId: this.extractUserIdFromRequest(req),
            };
        }
        if (req.body && req.body.agentType) {
            return {
                type: req.body.agentType,
                id: `${req.body.agentType}-agent`,
                userId: req.body.userId || this.extractUserIdFromRequest(req),
            };
        }
        if (req.query && req.query.agentType) {
            return {
                type: req.query.agentType,
                id: `${req.query.agentType}-agent`,
                userId: req.query.userId || this.extractUserIdFromRequest(req),
            };
        }
        const agentTypeHeader = req.headers['x-agent-type'];
        if (agentTypeHeader) {
            return {
                type: agentTypeHeader,
                id: `${agentTypeHeader}-agent`,
                userId: req.headers['x-user-id'] || this.extractUserIdFromRequest(req),
            };
        }
        return null;
    }
    extractUserInfo(req) {
        const userId = this.extractUserIdFromRequest(req);
        if (userId) {
            return {
                id: userId,
                walletAddress: req.headers['x-wallet-address'],
            };
        }
        return null;
    }
    extractUserIdFromRequest(req) {
        return (req.body?.userId ||
            req.query?.userId ||
            req.headers['x-user-id'] ||
            req.params?.userId ||
            null);
    }
    checkRoutePermission(req, agentInfo) {
        const resource = this.extractResourceFromPath(req.path);
        const action = this.extractActionFromMethod(req.method);
        if (!resource || !action) {
            return { allowed: true };
        }
        if (agentInfo.type === 'almighty') {
            return { allowed: true };
        }
        const allowedResources = this.getAllowedResources(agentInfo.type);
        if (!allowedResources.includes(resource) && !allowedResources.includes('*')) {
            return {
                allowed: false,
                reason: `Agent ${agentInfo.type} 无权访问资源 ${resource}`,
            };
        }
        const deniedResources = this.getDeniedResources(agentInfo.type);
        if (deniedResources.includes(resource)) {
            return {
                allowed: false,
                reason: `资源 ${resource} 在Agent ${agentInfo.type} 的禁用列表中`,
            };
        }
        const allowedActions = this.getAllowedActions(agentInfo.type, resource);
        if (!allowedActions.includes(action) && !allowedActions.includes('*')) {
            return {
                allowed: false,
                reason: `Agent ${agentInfo.type} 无权在资源 ${resource} 上执行 ${action} 操作`,
            };
        }
        return { allowed: true };
    }
    extractResourceFromPath(path) {
        const resourceMappings = {
            '/business-agents/interpret-dream': 'conversation_sessions',
            '/business-agents/chat': 'conversation_messages',
            '/knowledge': 'knowledge_entries',
            '/conversation': 'conversation_sessions',
        };
        for (const [pattern, resource] of Object.entries(resourceMappings)) {
            if (path.includes(pattern)) {
                return resource;
            }
        }
        return null;
    }
    extractActionFromMethod(method) {
        const methodMappings = {
            'GET': 'select',
            'POST': 'insert',
            'PUT': 'update',
            'PATCH': 'update',
            'DELETE': 'delete',
        };
        return methodMappings[method.toUpperCase()] || null;
    }
    applyDataIsolationFilters(req, agentInfo) {
        if (agentInfo.type !== 'almighty') {
            if (req.body && typeof req.body === 'object') {
                if (!req.body.filters) {
                    req.body.filters = {};
                }
                req.body.filters.agent_type = agentInfo.type;
                req.body._originalFilters = { ...req.body.filters };
            }
            if (req.query && typeof req.query === 'object') {
                req.query.agent_type = agentInfo.type;
            }
        }
        this.logger.debug(`为Agent ${agentInfo.type} 应用数据隔离过滤器`);
    }
    setupResponseFiltering(req, res, agentInfo) {
        const originalSend = res.send;
        res.send = function (data) {
            try {
                const filteredData = filterSensitiveData(data, agentInfo.type);
                return originalSend.call(this, filteredData);
            }
            catch (error) {
                return originalSend.call(this, data);
            }
        };
        function filterSensitiveData(data, agentType) {
            if (!data || typeof data !== 'object') {
                return data;
            }
            if (agentType === 'almighty') {
                return data;
            }
            if (Array.isArray(data)) {
                return data.map(item => filterSensitiveData(item, agentType));
            }
            const filtered = { ...data };
            const sensitiveFields = [
                'wallet_address',
                'payment_info',
                'private_key',
                'secret',
                'token',
            ];
            sensitiveFields.forEach(field => {
                if (field in filtered) {
                    delete filtered[field];
                }
            });
            if (filtered.agent_type && filtered.agent_type !== agentType) {
                return {
                    id: filtered.id,
                    agent_type: filtered.agent_type,
                    created_at: filtered.created_at,
                    message: '此数据属于其他Agent，无权访问详细内容',
                };
            }
            Object.keys(filtered).forEach(key => {
                if (typeof filtered[key] === 'object' && filtered[key] !== null) {
                    filtered[key] = filterSensitiveData(filtered[key], agentType);
                }
            });
            return filtered;
        }
    }
    getAllowedResources(agentType) {
        const resourceMap = {
            almighty: ['*'],
            taoist: ['conversation_sessions', 'conversation_messages', 'knowledge_entries', 'knowledge_usage_logs'],
            freud: ['conversation_sessions', 'conversation_messages', 'knowledge_entries', 'knowledge_usage_logs'],
            papa: ['conversation_sessions', 'conversation_messages', 'knowledge_entries', 'knowledge_usage_logs'],
            shared: ['conversation_sessions', 'conversation_messages', 'knowledge_entries'],
        };
        return resourceMap[agentType] || [];
    }
    getDeniedResources(agentType) {
        const deniedMap = {
            almighty: [],
            taoist: ['user_wallets', 'payment_records', 'admin_logs'],
            freud: ['user_wallets', 'payment_records', 'admin_logs'],
            papa: ['user_wallets', 'payment_records', 'admin_logs'],
            shared: ['user_wallets', 'payment_records', 'admin_logs'],
        };
        return deniedMap[agentType] || [];
    }
    getAllowedActions(agentType, resource) {
        if (agentType === 'almighty') {
            return ['*'];
        }
        const actionMap = {
            conversation_sessions: ['select', 'insert', 'update'],
            conversation_messages: ['select', 'insert'],
            knowledge_entries: ['select'],
            knowledge_usage_logs: ['select', 'insert'],
        };
        return actionMap[resource] || [];
    }
};
exports.DataIsolationMiddleware = DataIsolationMiddleware;
exports.DataIsolationMiddleware = DataIsolationMiddleware = DataIsolationMiddleware_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], DataIsolationMiddleware);
function RequirePermission(resource, action) {
    return function (target, propertyName, descriptor) {
        const method = descriptor.value;
        descriptor.value = async function (...args) {
            const req = args[0];
            if (req.agent && req.agent.type !== 'almighty') {
                const middleware = new DataIsolationMiddleware();
                const permission = middleware['checkRoutePermission'](req, req.agent);
                if (!permission.allowed) {
                    throw new common_1.ForbiddenException(permission.reason);
                }
            }
            return method.apply(this, args);
        };
    };
}
//# sourceMappingURL=data-isolation.middleware.js.map