import { ConfigService } from '@nestjs/config';
import { UsersService } from '../users/users.service';
import { GetChallengeDto, VerifySignatureDto, AuthResponseDto } from './dto/auth.dto';
export declare class AuthService {
    private readonly usersService;
    private readonly configService;
    private challenges;
    private readonly JWT_SECRET;
    private readonly CHALLENGE_EXPIRY;
    constructor(usersService: UsersService, configService: ConfigService);
    getChallenge(getChallengeDto: GetChallengeDto): Promise<{
        message: string;
    }>;
    verifySignature(verifySignatureDto: VerifySignatureDto): Promise<AuthResponseDto>;
    validateToken(token: string): Promise<any>;
    private verifySignatureWithPublicKey;
    private cleanExpiredChallenges;
}
