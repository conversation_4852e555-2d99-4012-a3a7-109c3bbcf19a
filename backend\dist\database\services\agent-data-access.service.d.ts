import { DatabaseService } from '../database.service';
export interface AgentType {
    id: string;
    name: string;
    display_name: string;
    description?: string;
    persona_config?: any;
    tools_config?: any;
    created_at: string;
}
export interface AgentMemory {
    id: string;
    agent_type: string;
    user_id: string;
    memory_type: 'short_term' | 'medium_term' | 'long_term';
    content: any;
    importance_score: number;
    expires_at?: string;
    access_count: number;
    last_accessed_at?: string;
    created_at: string;
}
export interface KnowledgeEntry {
    id: string;
    title?: string;
    content: string;
    category?: string;
    tags?: string[];
    embedding?: number[];
    metadata?: any;
    source?: string;
    created_at: string;
    updated_at: string;
}
export declare class AgentDataAccessService {
    private databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService);
    getAllAgentTypes(): Promise<AgentType[]>;
    getAgentTypeByName(name: string): Promise<AgentType | null>;
    createAgentMemory(agentType: string, userId: string, memoryType: 'short_term' | 'medium_term' | 'long_term', content: any, importanceScore?: number, expiresAt?: Date): Promise<{
        memory: AgentMemory;
        error?: any;
    }>;
    getAgentMemories(agentType: string, userId: string, memoryType?: 'short_term' | 'medium_term' | 'long_term', limit?: number): Promise<AgentMemory[]>;
    updateMemoryAccess(agentType: string, memoryId: string): Promise<{
        success: boolean;
        error?: any;
    }>;
    cleanupExpiredMemories(agentType: string): Promise<{
        deletedCount: number;
        error?: any;
    }>;
    getKnowledgeByCategory(agentType: string, category: string, limit?: number): Promise<KnowledgeEntry[]>;
    searchKnowledgeByTags(agentType: string, tags: string[], limit?: number): Promise<KnowledgeEntry[]>;
    searchKnowledgeByText(agentType: string, searchText: string, category?: string, limit?: number): Promise<KnowledgeEntry[]>;
    createKnowledgeEntry(agentType: string, title: string, content: string, category?: string, tags?: string[], metadata?: any, source?: string): Promise<{
        entry: KnowledgeEntry;
        error?: any;
    }>;
    getAgentConfig(agentType: string): Promise<{
        persona: any;
        tools: any;
        permissions: any[];
        error?: any;
    }>;
    updateMemoryImportance(agentType: string, memoryId: string, importanceScore: number): Promise<{
        success: boolean;
        error?: any;
    }>;
    createBatchMemories(agentType: string, userId: string, memories: Array<{
        type: 'short_term' | 'medium_term' | 'long_term';
        content: any;
        importance: number;
        expiresAt?: Date;
    }>): Promise<{
        successCount: number;
        errors: any[];
    }>;
}
