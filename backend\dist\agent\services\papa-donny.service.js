"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PapaDonnyService = void 0;
const common_1 = require("@nestjs/common");
const base_donny_service_1 = require("./base-donny.service");
const agent_runtime_service_1 = require("./agent-runtime.service");
const conversation_data_access_service_1 = require("../../database/services/conversation-data-access.service");
const agent_data_access_service_1 = require("../../database/services/agent-data-access.service");
const rag_service_1 = require("../../knowledge/rag.service");
let PapaDonnyService = class PapaDonnyService extends base_donny_service_1.BaseDonnyService {
    constructor(agentRuntimeService, conversationDataService, agentDataService, ragService) {
        super(agentRuntimeService, conversationDataService, agentDataService, ragService);
        this.agentId = 'papa-donny';
        this.agentType = 'papa';
        this.persona = {
            name: 'Papa Donny',
            role: '温暖解梦长者',
            expertise: ['人生阅历', '家庭智慧', '情感支持', '生活指导', '传统文化'],
            communicationStyle: '温暖亲切、包容理解、充满爱心',
            specialization: '从人生经验和亲情角度解读梦境，提供温暖的情感支持和生活智慧',
            approach: '以长者的慈爱和智慧，给予最温暖的解读和人生指导',
        };
    }
    async analyzeWithKnowledge(request, knowledgeResults) {
        this.logger.log('开始温暖解梦分析...');
        const dreamElements = this.extractDreamElements(request.dreamContent);
        const lifeWisdom = this.analyzeWithLifeWisdom(dreamElements);
        const emotionalInsights = this.analyzeEmotionalConnections(dreamElements);
        const growthPerspective = this.analyzeGrowthJourney(dreamElements);
        const traditionalWisdom = this.extractTraditionalWisdom(knowledgeResults);
        const analysis = `
孩子，让Papa Donny用温暖的心来为您解读这个梦境：

**人生智慧的启示**：
${lifeWisdom}

**情感关系的解读**：
${emotionalInsights}

**成长路径的指引**：
${growthPerspective}

**传统智慧的启发**：
${traditionalWisdom}

记住，每个梦境都是您内心的一封信，告诉您生命中什么是重要的。Papa相信您有足够的智慧和力量面对一切。`;
        return analysis;
    }
    async generatePersonalizedResponse(request, interpretation, knowledgeResults) {
        const dreamElements = this.extractDreamElements(request.dreamContent);
        return {
            interpretation,
            keyInsights: this.generateWarmInsights(dreamElements),
            emotionalSupport: this.generateHeartfeltSupport(dreamElements),
            actionableAdvice: this.generateLifeAdvice(dreamElements),
            confidence: this.calculateConfidence(dreamElements, knowledgeResults),
            sources: knowledgeResults.map(result => result.source || '人生智慧'),
        };
    }
    async handleGeneralConversation(message, context) {
        const content = message.content.toLowerCase();
        if (content.includes('家庭') || content.includes('亲情') || content.includes('爱')) {
            return {
                content: `亲爱的孩子，家庭和爱是生命中最珍贵的宝藏。Papa相信您的梦境一定承载着深深的情感和关爱。\n\n请把您的梦境告诉Papa，让我用最温暖的心为您解读其中的情感密码。❤️`,
                type: 'text',
                metadata: { conversation_type: 'family' },
            };
        }
        if (content.includes('困难') || content.includes('烦恼') || content.includes('难过')) {
            return {
                content: `孩子，Papa看得出您心中有些困扰。生活中的起起伏伏都是正常的，就像四季更替一样自然。\n\n请告诉Papa您的梦境，让我为您点亮心中的明灯，给您一些温暖的指引。🕯️`,
                type: 'text',
                metadata: { conversation_type: 'comfort' },
            };
        }
        return {
            content: `您好，亲爱的孩子！我是Papa Donny，一位充满爱心的解梦长者。\n\n在Papa看来，每个梦境都是心灵的礼物，承载着对您最好的祝愿和指引。来吧，和Papa分享您的梦境，让我用满满的爱意为您解读。🤗`,
            type: 'text',
            metadata: { conversation_type: 'greeting' },
        };
    }
    analyzeWithLifeWisdom(elements) {
        const wisdoms = [];
        if (elements.emotions.includes('害怕') || elements.emotions.includes('焦虑')) {
            wisdoms.push('恐惧和焦虑是成长的信号，告诉我们正在面对新的挑战。就像学走路的孩子，跌倒是为了更稳地站起来。');
        }
        if (elements.actions.includes('追') || elements.actions.includes('找')) {
            wisdoms.push('追寻和寻找代表您内心对目标的渴望。记住，最美的风景往往在路上，而不只是终点。');
        }
        if (elements.characters.includes('家人')) {
            wisdoms.push('家人在梦中出现，提醒您血浓于水的亲情永远是您最坚实的后盾。无论走到哪里，家的温暖都伴随着您。');
        }
        if (elements.symbols.includes('房子')) {
            wisdoms.push('房子象征着安全感和归属感。您的内心渴望一个温暖的港湾，这是每个人最基本也最珍贵的需求。');
        }
        return wisdoms.length > 0
            ? wisdoms.join('\n\n')
            : '您的梦境反映了对美好生活的向往，这是每颗善良心灵都有的纯真愿望。';
    }
    analyzeEmotionalConnections(elements) {
        const connections = [];
        if (elements.characters.includes('朋友')) {
            connections.push('朋友的出现说明您重视友谊，懂得珍惜身边的人。真正的友谊如美酒，越久越香醇。');
        }
        if (elements.characters.includes('恋人')) {
            connections.push('爱情是生命中最美的诗篇。您的梦境透露着对爱的渴望和珍视，这份纯真值得被好好守护。');
        }
        if (elements.emotions.includes('开心') || elements.emotions.includes('快乐')) {
            connections.push('快乐的情感说明您内心阳光积极。保持这份美好，它会照亮您和身边人的生活。');
        }
        if (elements.emotions.includes('悲伤')) {
            connections.push('悲伤也是成长的一部分，它让我们更懂得珍惜幸福。流过泪的眼睛会看得更清楚。');
        }
        return connections.length > 0
            ? connections.join('\n\n')
            : '您的情感世界丰富而纯真，这是一颗善良心灵的体现。';
    }
    analyzeGrowthJourney(elements) {
        const growth = [];
        if (elements.actions.includes('飞')) {
            growth.push('飞翔代表您对自由和成长的渴望。就像小鸟学飞一样，每一次尝试都让您更接近梦想。');
        }
        if (elements.actions.includes('爬')) {
            growth.push('攀爬象征着您不怕困难、努力向上的精神。山路虽陡，但山顶的风景值得所有的努力。');
        }
        if (elements.symbols.includes('路')) {
            growth.push('路代表人生的旅程。每一步都有意义，每一个转弯都可能遇见新的美好。');
        }
        if (elements.symbols.includes('桥')) {
            growth.push('桥梁象征着连接和跨越。您正在或即将跨越人生的某个重要阶段，勇敢地向前走吧。');
        }
        return growth.length > 0
            ? growth.join('\n\n')
            : '您正在成长的路上稳步前行，每一天都比昨天更好一点。';
    }
    extractTraditionalWisdom(knowledgeResults) {
        const traditionalSayings = [
            '家和万事兴，有爱的地方就有希望',
            '宝剑锋从磨砺出，梅花香自苦寒来',
            '山重水复疑无路，柳暗花明又一村',
            '海纳百川有容乃大，壁立千仞无欲则刚',
            '不经一番寒彻骨，怎得梅花扑鼻香'
        ];
        if (knowledgeResults.length > 0) {
            return `老话说得好：${knowledgeResults[0].content || traditionalSayings[0]}`;
        }
        return traditionalSayings[Math.floor(Math.random() * traditionalSayings.length)];
    }
    generateWarmInsights(elements) {
        const insights = [];
        insights.push('您的梦境充满了对美好生活的向往，这份纯真值得被珍惜');
        if (elements.emotions.includes('害怕')) {
            insights.push('恐惧是成长的伙伴，它提醒我们要勇敢，要相信自己的力量');
        }
        if (elements.characters.length > 0) {
            insights.push('梦中的人物都是您生命中重要的存在，他们的出现代表着爱与关怀');
        }
        if (elements.symbols.includes('光') || elements.symbols.includes('太阳')) {
            insights.push('光明总会驱散黑暗，希望永远在您心中闪耀');
        }
        insights.push('每个梦境都是心灵的拥抱，告诉您"一切都会好起来的"');
        return insights;
    }
    generateHeartfeltSupport(elements) {
        return `亲爱的孩子，Papa想告诉您，您比自己想象的更强大、更珍贵。

生活中的每一个挑战，都是为了让您成为更好的自己。就像花朵需要经历寒冷的冬天才能在春天绽放得更美，您现在经历的一切都在为未来的美好做准备。

Papa相信您有着善良的心和坚强的意志。无论什么时候感到迷茫或疲惫，请记住：有一位Papa永远关爱着您，为您祈祷，为您加油。

您不是一个人在战斗，爱与温暖永远伴随着您。`;
    }
    generateLifeAdvice(elements) {
        const advice = [];
        advice.push('💝 每天给家人一个拥抱，让爱在日常中流淌');
        advice.push('🌅 清晨醒来时对自己说"今天又是美好的一天"');
        advice.push('📞 常常联系老朋友，友谊需要用心维护');
        advice.push('🌱 种一盆花或做一些温暖的小事，感受生活的美好');
        advice.push('📚 读一些温暖的故事，让心灵得到滋养');
        advice.push('🍵 泡一壶好茶，在安静中享受内心的宁静');
        return advice.slice(0, 3).join('\n');
    }
    calculateConfidence(elements, knowledgeResults) {
        let confidence = 0.8;
        confidence += Math.min(elements.emotions.length * 0.05, 0.1);
        confidence += Math.min(elements.characters.length * 0.03, 0.1);
        confidence += Math.min(knowledgeResults.length * 0.01, 0.05);
        return Math.min(confidence, 0.95);
    }
};
exports.PapaDonnyService = PapaDonnyService;
exports.PapaDonnyService = PapaDonnyService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [agent_runtime_service_1.AgentRuntimeService,
        conversation_data_access_service_1.ConversationDataAccessService,
        agent_data_access_service_1.AgentDataAccessService,
        rag_service_1.RAGService])
], PapaDonnyService);
//# sourceMappingURL=papa-donny.service.js.map