# Donny 应用流程文档

## 1. 概述

本文档详细描述了 Donny Web3 解梦平台的用户流程和交互路径，为开发和测试团队提供明确的业务流程指导。

## 2. 用户注册与登录流程

### 2.1 首次登录流程

1. **初始界面加载**
   - 全屏显示 HEX 编码静态背景
   - 中央内容区域加载，显示 Almighty Donny 的 3D 形象
   - "交流互动区"显示欢迎信息和连接钱包提示
   - "用户输入区"初始状态禁用

2. **钱包连接**
   - 用户点击最右下角的"个人中心"图片按钮
   - 交流互动区显示用户中心界面
   - 用户点击"连接钱包"按钮
   - 选择钱包类型(目前仅支持 Phantom 钱包)
   - 钱包插件弹出授权确认窗口
   - 用户在钱包中确认连接请求
   - 连接成功后显示系统通知"钱包连接成功"

3. **用户名设置**
   - 首次登录自动转到用户名设置界面
   - 用户中心界面提供用户名输入框和"保存用户名"按钮
   - 用户设置并保存用户名
   - 完成后显示确认按钮
   - 用户点击确认按钮，进入与 Almighty Donny 的对话界面

### 2.2 常规登录流程

1. **钱包连接**
   - 用户点击"连接钱包"按钮
   - 选择钱包类型
   - 在钱包插件中确认连接请求
   - 连接成功后自动进入主交互界面

## 3. 与 Almighty Donny 交互流程

### 3.1 文本交互

1. **用户发送文本消息**
   - 用户在"用户输入区"的文本框中输入内容
   - 点击发送按钮或按回车键发送消息
   - 消息以用户气泡形式出现在"交流互动区"
   - Almighty Donny 处理请求并生成回复
   - 回复以 Agent 气泡形式显示在"交流互动区"

### 3.2 语音交互

1. **用户发送语音消息**
   - 用户点击"用户输入区"的麦克风按钮
   - 麦克风按钮变为录音状态，显示录音动画和波形反馈
   - 用户说话
   - 用户再次点击麦克风按钮或系统检测到静音自动停止录音
   - 系统将音频发送至后端进行处理
   - 转录文本以用户气泡形式出现在"交流互动区"(标记"语音输入")
   - Almighty Donny 处理转录文本并生成回复
   - 回复以 Agent 气泡形式显示在"交流互动区"

## 4. 解梦服务核心流程

### 4.1 解梦需求表达

1. **用户表达解梦意图**
   - 用户通过文本或语音向 Almighty Donny 表达解梦需求
   - Almighty Donny 确认收到解梦请求

### 4.2 业务型 Donny 选择

1. **Donny 选择界面**
   - Almighty Donny 在"交流互动区"推出四个业务型 Donny 的选项按钮
   - 每个按钮包含 Donny 名称和简短描述
   - 可选择：Taoist Donny(东方道教)、Freud Donny(现代心理学)、Papa Donny(共情式)、Augur Donny(塔罗牌，提示暂不可用)
   - 用户点击选择一个业务型 Donny
   - 选中的按钮显示高亮反馈

### 4.3 Donny 切换流程

1. **Donny 切换与连接**
   - 用户选择业务型 Donny 后，Almighty Donny 确认选择
   - "交流互动区"显示"正在为您连接[所选业务 Donny 名称]，请稍候..."的系统通知

### 4.4 Donny 切换动画

1. **由 Almighty Donny 切换到业务型 Donny**
   - Almighty Donny 从"互动 Donny 区"淡出
   - 被选中的业务 Donny 周边图片执行"漩涡转动"和"灰化"动画
   - 选中的业务型 Donny 的 3D 模型在"互动 Donny 区"淡入
   - 耗时：约 1-2 秒，配有简短音效
   - "交流互动区"清空或保留少量上下文提示
   - 切换到显示用户与此业务型 Donny 的专属聊天记录
   - 输入框占位符更新为"向[所选业务 Donny 名称]叙说你的梦境吧..."

### 4.5 梦境描述与解析

1. **梦境描述**
   - 用户通过文本或语音向业务型 Donny 详细描述梦境
   - 梦境内容以用户气泡形式出现在"交流互动区"

2. **梦境解析**
   - 业务型 Donny 根据其特定知识库和专长分析梦境
   - 解析结果以 Agent 气泡形式在"交流互动区"展示给用户
   - 用户可以在收到解析后，继续补充梦境内容，或者提供新的材料，由业务型donny判断是否需要完善解析。
   - 业务型donny可以主动询问用户是否可以结束解析服务了，用户也可以主动表达出可以结束服务的信息。
   - 只要业务型donny判断用户表达出明确的结束信号时，就可以说"感谢您使用我的服务，下次再见喽"

3. **手动退出**
   - 聊天界面底部显示「完成并退出」按钮。
   - 用户点击按钮 → 业务型 Donny 立即发送告别信息。
   - 3 秒后业务型 Donny 图像淡出，界面切回 Almighty Donny。
   - Almighty Donny 询问用户是否分享梦境，并显示"分享 / 不了"按钮。

### 4.6 返回 Almighty Donny

1. **解梦完成切换**
   - 解梦服务完成后，业务型 Donny 3D 模型淡出
   - 业务型 Donny 周边图片恢复色彩
   - Almighty Donny 3D 模型在"互动 Donny 区"淡入
   - "交流互动区"恢复与 Almighty Donny 的聊天记录
   - Almighty Donny 主动询问解梦体验、是否分享梦境等后续操作

## 5. 交互中断处理流程

### 5.1 选择Donny后未提交梦境的中断处理

1. **状态记录**
   - 系统记录用户当前状态：已选择 Donny、未提交梦境
   - 设置 48 小时倒计时定时器

2. **用户重新进入**
   - 用户在 48 小时内再次登录
   - Almighty Donny 检测到未完成的解梦流程
   - 在"交流互动区"询问："您之前已选择 [Donny名称] 进行解梦，是否继续？"
   - 提供"继续解梦"按钮
   - 用户点击继续，系统跳转到与选定业务型 Donny 的对话窗口
   - 重置倒计时定时器

3. **超时处理(49小时未重新进入)**
   - 系统自动取消解梦业务
   - 系统记录取消原因："用户超时未提交梦境"
   - 用户下次登录时，Almighty Donny 通知："您之前的解梦服务因超时未完成已取消。"

### 5.2 提交梦境后离开的处理

1. **后台处理**
   - 用户提交梦境后离开网站
   - 业务型 Donny 继续在后台处理梦境并生成解梦结果
   - 系统将解梦结果存储到用户的历史数据中

2. **用户重新进入**
   - 用户再次登录网站
   - Almighty Donny 检测到已完成但未查看的解梦结果
   - 通知："您之前的梦境已由 [Donny名称] 解析完成，您可以在历史数据中查看结果。"
   - 提供"查看历史"按钮，点击后跳转到历史数据页面

## 6. 反馈流程

### 6.1 反馈提交流程

1. **反馈提醒**
   - 系统记录解梦完成时间
   - 用户在解梦三天后登录网站
   - Almighty Donny 检测到可反馈的解梦服务
   - 提醒："您三天前由 [Donny 名称] 提供的解梦服务现在可以反馈，您对解梦结果满意吗？"

2. **反馈收集**
   - 用户在输入框中表达反馈意见
   - Almighty Donny 接收反馈并进行分析
   - 如果判断不确定是否满意，会与用户确认

3. **反馈处理流程**
   - 用户表达反馈意见（满意/不满意）
   - Almighty Donny 确认："感谢您的反馈，我们会持续改进服务质量。"
   - 系统记录用户反馈用于服务优化

4. **反馈信息传递**
   - Almighty Donny 将用户反馈发送给相关业务型 Donny
   - 业务型 Donny 接收反馈，并将其与对应的梦境和解析数据绑定
   - 系统更新数据库中的梦境记录，添加反馈内容和处理结果

## 7. 梦境分享与 NFT/Token 奖励流程

### 7.1 分享询问与用户同意

1. **分享询问**
   - 解梦完成后，Almighty Donny 询问："您愿意将这个梦境匿名分享到梦海吗？分享后将获得 Donny Token 奖励。"
   - 提供"同意分享"和"不分享"按钮

2. **用户同意分享**
   - 用户点击"同意分享"或通过文本/语音表达同意
   - Almighty Donny 确认："感谢您的分享！我将评估这个梦境的价值并为您铸造 NFT 和 Token。"

### 7.2 梦境价值评估与奖励发放

1. **梦境价值评估**
   - Almighty Donny 基于梦境复杂度、独特性、社区价值、文化或心理学参考价值等因素评估梦境价值
   - 决定 Token 奖励数量(范围：5-100 Donny Token)

2. **NFT 和 Token 铸造**
   - Almighty Donny 向 Accountant Donny 发送铸造指令
   - Accountant Donny 调用 NFT 铸造服务，创建梦境 NFT
   - Accountant Donny 调用 Token 合约，铸造并发送指定数量的 Donny Token 到用户钱包
   - 系统更新数据库，记录 NFT ID、Token 数量和交易哈希

3. **完成通知**
   - Accountant Donny 向 Almighty Donny 报告操作完成
   - Almighty Donny 通知用户："您的梦境已成功分享到梦海！我们为您铸造了梦境 NFT，并奖励了 [数量] Donny Token，已发送到您的钱包。"
   - 提供"查看 NFT"按钮，点击后跳转到 NFT 展示页面

## 8. 功能型 Donny 交互流程

### 8.1 Accountant Donny 交互

1. **触发**
   - 用户点击中央内容区环绕的"Accountant Donny"图片元素

2. **界面切换**
   - "互动 Donny 区"切换为 Accountant Donny 3D 形象
   - "交流互动区"切换为 Accountant Donny 专属数据展示界面，采用 Tab 形式：
     - **Tab 1 "我的奖励"：** NFT 列表、Token 奖励列表
     - **Tab 2 "账本总览"：** Accountant Donny 公开交易数据和资产数据
   - "交流互动区"右上角出现"关闭"按钮
   - 用户输入区在此场景下不可用

3. **关闭**
   - 用户点击"关闭"按钮
   - 界面恢复至与 Almighty Donny 交互的状态
   - 用户输入区恢复可用状态

### 8.2 Recorder Donny 交互 (初期版本)

1. **触发**
   - 用户点击中央内容区环绕的"Recorder Donny"图片元素

2. **响应**
   - 显示"正在建设梦海中"的提示信息
   - 无进一步交互功能

### 8.3 用户历史梦境数据查看

1. **触发**
   - 用户点击中央内容区环绕的"用户梦境数据"图片元素

2. **界面切换**
   - "互动 Donny 区"的 Almighty Donny 3D 形象保持不变
   - "交流互动区"切换为梦境卡片列表
   - 每张卡片显示：解梦时间、梦境内容摘要、解梦 Donny、解梦内容摘要、用户反馈
   - 卡片可点击展开查看完整详情
   - "交流互动区"右上角出现"关闭"按钮
   - 用户输入区在此场景下不可用

3. **关闭**
   - 用户点击"关闭"按钮
   - "交流互动区"恢复为与 Almighty Donny 的聊天记录
   - 用户输入区恢复可用状态

## 9. 业务型 Donny 介绍查看流程

1. **触发**
   - 用户点击元素环绕区中的任一业务型 Donny 图片元素(Taoist, Augur, Papa, Freud Donny)

2. **界面响应**
   - 弹出与"交流互动区"尺寸一致的"Donny 介绍弹窗"
   - 弹窗内容：该 Donny 的形象大图、名称、身份介绍、解梦特长和风格
   - "互动 Donny 区"的 3D 形象依然是 Almighty Donny，不发生变化
   - 弹窗右上角有"关闭"按钮

3. **关闭**
   - 用户点击弹窗右上角的"关闭"按钮
   - 弹窗消失，主界面状态不变

## 10. 用户认知系统体验流程

### 10.1 初次交互体验

1. **首次对话**
   - 用户首次与Almighty Donny交互，Donny表现友好但不了解用户
   - 询问基本信息和兴趣，建立初步认知
   - 短期记忆储存首次交互内容

### 10.2 持续交互体验增强

1. **第二次及后续访问**
   - Almighty Donny从记忆中提取用户信息
   - 根据用户之前的交互和反馈调整对话风格
   - 示例："欢迎回来，[用户名]。上次您提到喜欢神秘学内容，所以我想您可能会对Taoist Donny的解梦方式感兴趣。"

2. **长期关系构建**
   - 随着使用次数增加，Almighty Donny能记住：
     - 用户偏好的解梦Donny
     - 用户关注的梦境主题
     - 用户的情感反应模式
     - 重要的生活事件（用户主动分享的）
   - 表现为更个性化的推荐和交流方式

### 10.3 个性化推荐流程

1. **基于用户认知的Donny推荐**
   - 用户表达解梦需求
   - Almighty Donny分析梦境内容、用户历史偏好和互动模式
   - 提供个性化Donny推荐："基于您梦中的自然元素和您过往的积极反馈，我认为Taoist Donny可能对这个梦境有独特见解。"

2. **用户认知反馈循环**
   - 用户对推荐的反应被记录到中期记忆
   - 系统定期分析反馈模式并更新长期记忆
   - 下一次推荐进一步优化，形成持续改进的循环

### 10.4 隐私控制

1. **用户数据透明度**
   - 用户可在个人中心查看"Donny对我的了解"概览
   - 显示系统存储的非敏感用户认知数据
   - 提供删除特定记忆或重置所有认知数据的选项

## 新增流程分支

### 未完成解梦直接返回
```
用户进入业务 Donny → 产生 session_id（WAITING_DREAM_INPUT） → 用户未提交梦境/Donny 未解梦 → 返回 Almighty Donny → Almighty 检测 Session 状态 → 弹提示"是否继续解梦？"
```

### 分享前校验 & 异常分享处理
```
Almighty 触发分享按钮渲染 → GET /session 校验 status=COMPLETED & shared=false →
  • 校验通过 → 继续原分享流程
  • 校验失败(NO_INTERPRETATION) → 系统 Toast "暂无可分享的梦境" → 流程结束
``` 