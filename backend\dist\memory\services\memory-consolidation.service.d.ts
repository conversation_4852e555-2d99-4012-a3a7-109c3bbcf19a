import { BaseMemory } from '../memory.types';
import { ShortTermMemoryService } from './short-term-memory.service';
import { MediumTermMemoryService } from './medium-term-memory.service';
import { LongTermMemoryService } from './long-term-memory.service';
export declare class MemoryConsolidationService {
    private readonly shortTermService;
    private readonly mediumTermService;
    private readonly longTermService;
    private readonly logger;
    private consolidationRules;
    private readonly shortToMediumThreshold;
    private readonly mediumToLongThreshold;
    constructor(shortTermService: ShortTermMemoryService, mediumTermService: MediumTermMemoryService, longTermService: LongTermMemoryService);
    performConsolidation(): Promise<{
        shortToMedium: number;
        mediumToLong: number;
        totalProcessed: number;
    }>;
    checkMemoryForConsolidation(memory: BaseMemory): Promise<boolean>;
    getPendingCount(): Promise<number>;
    private initializeConsolidationRules;
    private getShortTermConsolidationCandidates;
    private getMediumTermConsolidationCandidates;
    private shouldConsolidateShortToMedium;
    private shouldConsolidateMediumToLong;
    private consolidateShortToMedium;
    private consolidateMediumToLong;
    private calculateInitialConsolidationScore;
    private calculateEmotionalSignificance;
    private generateNarrativeContext;
    private recordConsolidationEvent;
}
