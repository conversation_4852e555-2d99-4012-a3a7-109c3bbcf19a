"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDonnyService = void 0;
const common_1 = require("@nestjs/common");
const agent_runtime_service_1 = require("./agent-runtime.service");
const conversation_data_access_service_1 = require("../../database/services/conversation-data-access.service");
const agent_data_access_service_1 = require("../../database/services/agent-data-access.service");
const rag_service_1 = require("../../knowledge/rag.service");
let BaseDonnyService = class BaseDonnyService {
    constructor(agentRuntimeService, conversationDataService, agentDataService, ragService) {
        this.agentRuntimeService = agentRuntimeService;
        this.conversationDataService = conversationDataService;
        this.agentDataService = agentDataService;
        this.ragService = ragService;
        this.logger = new common_1.Logger(this.constructor.name);
    }
    async onModuleInit() {
        this.logger.log(`正在初始化${this.persona.name}...`);
        await this.initializeAgent();
    }
    async initializeAgent() {
        try {
            const agentConfig = await this.agentDataService.getAgentConfig(this.agentType);
            const config = {
                agentId: this.agentId,
                agentType: this.agentType,
                name: this.persona.name,
                persona: agentConfig?.persona || this.getPersonaPrompt(),
                tools: agentConfig?.tools || this.getDefaultTools(),
                modelProvider: 'openai',
            };
            await this.agentRuntimeService.createAgent(config);
            this.logger.log(`${this.persona.name} 初始化完成`);
        }
        catch (error) {
            this.logger.error(`初始化${this.persona.name}失败: ${error.message}`);
            throw error;
        }
    }
    async interpretDream(request) {
        this.logger.log(`开始解释梦境 - 用户: ${request.userId}`);
        try {
            const ragResponse = await this.ragService.performRAG(request.dreamContent, this.agentType, request.userId, {
                similarity_threshold: 0.7,
                max_search_results: 5,
            });
            const interpretation = await this.analyzeWithKnowledge(request, ragResponse.search_results);
            const response = await this.generatePersonalizedResponse(request, interpretation, ragResponse.search_results);
            await this.saveInterpretationRecord(request, response);
            this.logger.log(`梦境解释完成 - 置信度: ${response.confidence}`);
            return response;
        }
        catch (error) {
            this.logger.error(`梦境解释失败: ${error.message}`);
            throw error;
        }
    }
    async handleMessage(userId, content, context) {
        try {
            const message = {
                id: `msg_${Date.now()}`,
                userId,
                content,
                type: 'text',
                timestamp: new Date(),
            };
            if (this.isDreamInterpretationRequest(content)) {
                const dreamRequest = {
                    userId,
                    dreamContent: content,
                    sessionId: context?.sessionId,
                };
                const interpretation = await this.interpretDream(dreamRequest);
                return {
                    content: this.formatInterpretationResponse(interpretation),
                    type: 'text',
                    metadata: {
                        agent_type: this.agentType,
                        confidence: interpretation.confidence,
                        sources: interpretation.sources,
                    },
                };
            }
            return await this.handleGeneralConversation(message, context);
        }
        catch (error) {
            this.logger.error(`处理消息失败: ${error.message}`);
            return {
                content: `抱歉，我现在遇到了一些问题。作为${this.persona.name}，我会努力为你提供帮助。请稍后再试。`,
                type: 'text',
                metadata: { error: true },
            };
        }
    }
    getAgentInfo() {
        return this.persona;
    }
    getCapabilities() {
        return [
            '专业梦境解释',
            '情感支持和指导',
            '基于知识库的深度分析',
            '个性化建议和洞察',
        ];
    }
    isDreamInterpretationRequest(content) {
        const dreamKeywords = ['梦见', '做梦', '梦境', '梦到', '昨晚梦', '我梦', '梦中'];
        return dreamKeywords.some(keyword => content.includes(keyword));
    }
    formatInterpretationResponse(interpretation) {
        let response = `🌙 **${this.persona.name}的解读**\n\n`;
        response += `${interpretation.interpretation}\n\n`;
        if (interpretation.keyInsights.length > 0) {
            response += `💡 **关键洞察**\n`;
            interpretation.keyInsights.forEach((insight, index) => {
                response += `${index + 1}. ${insight}\n`;
            });
            response += '\n';
        }
        if (interpretation.emotionalSupport) {
            response += `💖 **情感支持**\n${interpretation.emotionalSupport}\n\n`;
        }
        if (interpretation.actionableAdvice) {
            response += `🎯 **建议行动**\n${interpretation.actionableAdvice}\n\n`;
        }
        response += `🔮 解读置信度: ${Math.round(interpretation.confidence * 100)}%`;
        return response;
    }
    getPersonaPrompt() {
        return {
            role: this.persona.role,
            expertise: this.persona.expertise,
            communication_style: this.persona.communicationStyle,
            specialization: this.persona.specialization,
            approach: this.persona.approach,
        };
    }
    getDefaultTools() {
        return [
            {
                name: 'knowledge_search',
                description: '搜索相关专业知识',
                parameters: {
                    query: 'string',
                    agent_type: this.agentType,
                },
            },
            {
                name: 'emotional_analysis',
                description: '分析梦境中的情感元素',
                parameters: {
                    content: 'string',
                    context: 'string',
                },
            },
            {
                name: 'symbol_interpretation',
                description: '解释梦境符号和意象',
                parameters: {
                    symbols: 'array',
                    cultural_context: 'string',
                },
            },
        ];
    }
    async saveInterpretationRecord(request, response) {
        try {
            const sessionId = request.sessionId || `session_${request.userId}_${Date.now()}`;
            await this.conversationDataService.addMessageToSession(this.agentType, sessionId, 'user', request.userId, request.dreamContent, 'text', { interpretation_request: true });
            await this.conversationDataService.addMessageToSession(this.agentType, sessionId, 'agent', this.agentId, this.formatInterpretationResponse(response), 'text', {
                agent_type: this.agentType,
                confidence: response.confidence,
                key_insights: response.keyInsights,
            });
        }
        catch (error) {
            this.logger.warn(`保存解释记录失败: ${error.message}`);
        }
    }
    extractDreamElements(content) {
        const emotions = this.extractByKeywords(content, [
            '害怕', '恐惧', '焦虑', '开心', '快乐', '兴奋', '悲伤', '愤怒', '困惑', '惊讶'
        ]);
        const symbols = this.extractByKeywords(content, [
            '水', '火', '动物', '房子', '路', '桥', '山', '树', '花', '鸟', '蛇', '狗', '猫'
        ]);
        const actions = this.extractByKeywords(content, [
            '跑', '飞', '追', '逃', '掉', '爬', '游', '走', '找', '失', '死', '哭', '笑'
        ]);
        const characters = this.extractByKeywords(content, [
            '朋友', '家人', '陌生人', '老师', '同事', '父母', '孩子', '恋人'
        ]);
        return { emotions, symbols, actions, characters };
    }
    extractByKeywords(content, keywords) {
        return keywords.filter(keyword => content.includes(keyword));
    }
};
exports.BaseDonnyService = BaseDonnyService;
exports.BaseDonnyService = BaseDonnyService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [agent_runtime_service_1.AgentRuntimeService,
        conversation_data_access_service_1.ConversationDataAccessService,
        agent_data_access_service_1.AgentDataAccessService,
        rag_service_1.RAGService])
], BaseDonnyService);
//# sourceMappingURL=base-donny.service.js.map