"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
let UsersService = class UsersService {
    constructor() {
        this.users = [];
        this.userIdCounter = 1;
    }
    async create(createUserDto) {
        const existingUserByUsername = this.users.find(user => user.username === createUserDto.username);
        if (existingUserByUsername) {
            throw new common_1.ConflictException('用户名已存在');
        }
        const existingUserByWallet = this.users.find(user => user.walletAddresses.includes(createUserDto.walletAddress));
        if (existingUserByWallet) {
            throw new common_1.ConflictException('钱包地址已被注册');
        }
        const now = new Date();
        const user = {
            id: this.userIdCounter.toString(),
            username: createUserDto.username,
            primaryWalletAddress: createUserDto.walletAddress,
            walletAddresses: [createUserDto.walletAddress],
            registrationTime: now,
            status: 'active',
            createdAt: now,
            updatedAt: now,
        };
        this.users.push(user);
        this.userIdCounter++;
        return user;
    }
    async findAll() {
        return this.users;
    }
    async findOne(id) {
        const user = this.users.find(user => user.id === id);
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        return user;
    }
    async findByWalletAddress(walletAddress) {
        return this.users.find(user => user.walletAddresses.includes(walletAddress)) || null;
    }
    async update(id, updateUserDto) {
        const userIndex = this.users.findIndex(user => user.id === id);
        if (userIndex === -1) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const user = this.users[userIndex];
        if (updateUserDto.username) {
            const existingUser = this.users.find(u => u.username === updateUserDto.username && u.id !== id);
            if (existingUser) {
                throw new common_1.ConflictException('用户名已存在');
            }
            user.username = updateUserDto.username;
        }
        if (updateUserDto.newWalletAddress) {
            const existingUser = this.users.find(u => u.walletAddresses.includes(updateUserDto.newWalletAddress));
            if (existingUser) {
                throw new common_1.ConflictException('钱包地址已被注册');
            }
            user.walletAddresses.push(updateUserDto.newWalletAddress);
        }
        user.updatedAt = new Date();
        this.users[userIndex] = user;
        return user;
    }
    async remove(id) {
        const userIndex = this.users.findIndex(user => user.id === id);
        if (userIndex === -1) {
            throw new common_1.NotFoundException('用户不存在');
        }
        this.users.splice(userIndex, 1);
    }
    async updateLastLoginTime(id) {
        const user = await this.findOne(id);
        user.lastLoginTime = new Date();
        user.updatedAt = new Date();
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)()
], UsersService);
//# sourceMappingURL=users.service.js.map