import { MediumTermMemory, MemoryImportance, MemoryQuery } from '../memory.types';
import { AgentType } from '../../database/types/rag.types';
import { DatabaseService } from '../../database/database.service';
import { EmbeddingService } from '../../knowledge/embedding.service';
export declare class MediumTermMemoryService {
    private readonly databaseService;
    private readonly embeddingService;
    private readonly logger;
    private readonly reinforcementThreshold;
    private readonly decayRate;
    private readonly consolidationThreshold;
    constructor(databaseService: DatabaseService, embeddingService: EmbeddingService);
    store(memoryData: Partial<MediumTermMemory>): Promise<MediumTermMemory>;
    retrieve(query: MemoryQuery): Promise<MediumTermMemory[]>;
    semanticSearch(query: MemoryQuery): Promise<MediumTermMemory[]>;
    update(memoryId: string, updates: Partial<MediumTermMemory>): Promise<MediumTermMemory>;
    delete(memoryId: string): Promise<boolean>;
    findById(memoryId: string): Promise<MediumTermMemory | null>;
    cleanupLowImportanceMemories(): Promise<number>;
    getConsolidationCandidates(limit?: number): Promise<MediumTermMemory[]>;
    getStats(agentType?: AgentType, userId?: string): Promise<{
        total: number;
        byImportance: Record<MemoryImportance, number>;
        byCategory: Record<string, number>;
        storageUsed: number;
        averageImportance: number;
        oldestMemory?: Date;
        newestMemory?: Date;
    }>;
    private generateEmbedding;
    private calculateInitialConsolidationScore;
    private calculateSimilarity;
    private applyDecayCalculation;
    private reinforceMemories;
    private queryDatabase;
    private mapDbRowToMemory;
}
