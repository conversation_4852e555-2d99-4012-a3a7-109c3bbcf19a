const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// 从环境变量获取Supabase配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ 缺少Supabase配置：SUPABASE_URL 或 SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
}

// 创建Supabase客户端（使用service_role密钥可以绕过RLS）
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSqlFile(filename) {
    try {
        console.log(`📄 正在执行SQL文件: ${filename}`);
        
        const sqlPath = path.join(__dirname, filename);
        const sqlContent = fs.readFileSync(sqlPath, 'utf8');
        
        // 分割SQL语句（简单分割，以分号为界）
        const statements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        
        console.log(`📊 发现 ${statements.length} 条SQL语句`);
        
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            if (statement.trim()) {
                try {
                    const { data, error } = await supabase.rpc('exec_sql', {
                        sql_query: statement + ';'
                    });
                    
                    if (error) {
                        // 如果没有exec_sql函数，直接使用SQL查询
                        console.log(`⚠️ 尝试直接执行SQL语句 ${i + 1}/${statements.length}`);
                        // 对于某些DDL语句，我们可能需要使用不同的方法
                        console.log(`SQL: ${statement.substring(0, 100)}...`);
                    } else {
                        console.log(`✅ 成功执行语句 ${i + 1}/${statements.length}`);
                    }
                } catch (err) {
                    console.warn(`⚠️ 语句执行失败 ${i + 1}: ${err.message}`);
                    // 继续执行其他语句，某些错误（如重复创建）是可以忽略的
                }
            }
        }
        
        console.log(`✅ 文件 ${filename} 执行完成`);
        
    } catch (error) {
        console.error(`❌ 执行SQL文件失败 ${filename}:`, error.message);
        throw error;
    }
}

async function deployDatabase() {
    try {
        console.log('🚀 开始部署Donny Web3数据库...');
        console.log(`🔗 连接到: ${supabaseUrl}`);
        
        // 测试连接
        const { data: testData, error: testError } = await supabase
            .from('information_schema.tables')
            .select('table_name')
            .limit(1);
        
        if (testError) {
            console.error('❌ Supabase连接失败:', testError.message);
            return;
        }
        
        console.log('✅ Supabase连接成功');
        
        // 按顺序执行SQL文件
        console.log('\n📋 开始执行数据库脚本...');
        
        // 1. 执行基础schema
        await executeSqlFile('schema.sql');
        
        // 2. 执行RAG知识库schema
        await executeSqlFile('rag_schema.sql');
        
        // 3. 执行初始数据
        await executeSqlFile('initial_data.sql');
        
        // 4. 执行RAG初始数据
        await executeSqlFile('rag_initial_data.sql');
        
        console.log('\n🎉 数据库部署完成！');
        console.log('\n📊 数据库结构总览:');
        console.log('   👥 用户系统: users, wallet_addresses, user_sessions');
        console.log('   🤖 Agent系统: agent_types, agent_permissions, agent_memories');
        console.log('   💭 梦境系统: dreams, interpretations, user_feedback');
        console.log('   💬 对话系统: conversation_sessions, conversation_messages');
        console.log('   📚 RAG知识库: knowledge_bases, knowledge_entries, knowledge_usage_logs');
        console.log('   🔍 向量搜索: pgvector扩展, embedding索引, 相似度检索');
        console.log('   💰 支付系统: service_orders');
        console.log('\n🔐 RLS策略已启用，确保数据安全');
        
    } catch (error) {
        console.error('❌ 数据库部署失败:', error.message);
        process.exit(1);
    }
}

// 直接执行SQL语句的辅助函数（用于测试）
async function testConnection() {
    try {
        console.log('🧪 测试数据库连接...');
        
        const { data, error } = await supabase
            .from('agent_types')
            .select('name, display_name')
            .limit(5);
        
        if (error) {
            console.error('❌ 测试查询失败:', error.message);
        } else {
            console.log('✅ 测试查询成功，Agent类型:');
            data.forEach(agent => {
                console.log(`   - ${agent.name}: ${agent.display_name}`);
            });
        }
        
    } catch (error) {
        console.error('❌ 连接测试失败:', error.message);
    }
}

// 命令行参数处理
const command = process.argv[2];

switch (command) {
    case 'deploy':
        deployDatabase();
        break;
    case 'test':
        testConnection();
        break;
    default:
        console.log('📚 用法:');
        console.log('  node deploy.js deploy  - 部署数据库');
        console.log('  node deploy.js test    - 测试连接');
        break;
}

module.exports = {
    deployDatabase,
    testConnection
}; 