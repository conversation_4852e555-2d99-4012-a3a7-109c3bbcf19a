"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DatabaseService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const common_1 = require("@nestjs/common");
const supabase_provider_1 = require("./supabase.provider");
let DatabaseService = DatabaseService_1 = class DatabaseService {
    constructor(supabaseProvider) {
        this.supabaseProvider = supabaseProvider;
        this.logger = new common_1.Logger(DatabaseService_1.name);
        this.supabase = this.supabaseProvider.getClient();
    }
    async checkAgentPermission(agentType, resource, action) {
        try {
            const { data, error } = await this.supabase
                .rpc('check_agent_permission', {
                agent_name: agentType,
                resource_name: resource,
                action_name: action,
            });
            if (error) {
                this.logger.warn(`权限检查失败: ${error.message}`);
                return false;
            }
            return data || false;
        }
        catch (error) {
            this.logger.error(`权限检查错误: ${error.message}`);
            return false;
        }
    }
    async getAgentPermissions(agentType) {
        try {
            const { data, error } = await this.supabase
                .rpc('get_agent_permissions', { agent_name: agentType });
            if (error) {
                this.logger.warn(`获取权限失败: ${error.message}`);
                return [];
            }
            return data || [];
        }
        catch (error) {
            this.logger.error(`获取权限错误: ${error.message}`);
            return [];
        }
    }
    async executeWithPermission(agentType, query, userId) {
        const hasPermission = await this.checkAgentPermission(agentType, query.table, query.operation);
        if (!hasPermission) {
            return {
                data: null,
                error: { message: `Agent ${agentType} 没有权限执行 ${query.operation} 操作在 ${query.table} 上` },
            };
        }
        try {
            let result;
            switch (query.operation) {
                case 'select':
                    let selectQuery = this.supabase.from(query.table).select(query.columns || '*');
                    if (query.filters) {
                        Object.entries(query.filters).forEach(([key, value]) => {
                            selectQuery = selectQuery.eq(key, value);
                        });
                    }
                    result = await selectQuery;
                    break;
                case 'insert':
                    result = await this.supabase.from(query.table).insert(query.data);
                    break;
                case 'update':
                    let updateQuery = this.supabase.from(query.table).update(query.data);
                    if (query.filters) {
                        Object.entries(query.filters).forEach(([key, value]) => {
                            updateQuery = updateQuery.eq(key, value);
                        });
                    }
                    result = await updateQuery;
                    break;
                case 'delete':
                    let deleteQuery = this.supabase.from(query.table).delete();
                    if (query.filters) {
                        Object.entries(query.filters).forEach(([key, value]) => {
                            deleteQuery = deleteQuery.eq(key, value);
                        });
                    }
                    result = await deleteQuery;
                    break;
                default:
                    return { data: null, error: { message: 'Unsupported operation' } };
            }
            this.logger.log(`Agent ${agentType} 执行 ${query.operation} 操作成功`);
            return result;
        }
        catch (error) {
            this.logger.error(`数据库操作失败: ${error.message}`);
            return { data: null, error };
        }
    }
    getClient() {
        return this.supabase;
    }
    async createUserWithWallet(walletAddress, username) {
        try {
            const { data, error } = await this.supabase
                .rpc('create_user_with_wallet', {
                wallet_addr: walletAddress,
                user_name: username,
            });
            if (error) {
                return { userId: null, error };
            }
            return { userId: data };
        }
        catch (error) {
            return { userId: null, error };
        }
    }
    async healthCheck() {
        return this.supabaseProvider.testConnection();
    }
};
exports.DatabaseService = DatabaseService;
exports.DatabaseService = DatabaseService = DatabaseService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_provider_1.SupabaseProvider])
], DatabaseService);
//# sourceMappingURL=database.service.js.map