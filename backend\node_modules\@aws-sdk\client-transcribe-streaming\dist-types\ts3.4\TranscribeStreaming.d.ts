import { HttpHandlerOptions as __HttpHandlerOptions } from "@smithy/types";
import {
  GetMedicalScribeStreamCommandInput,
  GetMedicalScribeStreamCommandOutput,
} from "./commands/GetMedicalScribeStreamCommand";
import {
  StartCallAnalyticsStreamTranscriptionCommandInput,
  StartCallAnalyticsStreamTranscriptionCommandOutput,
} from "./commands/StartCallAnalyticsStreamTranscriptionCommand";
import {
  StartMedicalScribeStreamCommandInput,
  StartMedicalScribeStreamCommandOutput,
} from "./commands/StartMedicalScribeStreamCommand";
import {
  StartMedicalStreamTranscriptionCommandInput,
  StartMedicalStreamTranscriptionCommandOutput,
} from "./commands/StartMedicalStreamTranscriptionCommand";
import {
  StartStreamTranscriptionCommandInput,
  StartStreamTranscriptionCommandOutput,
} from "./commands/StartStreamTranscriptionCommand";
import { TranscribeStreamingClient } from "./TranscribeStreamingClient";
export interface TranscribeStreaming {
  getMedicalScribeStream(
    args: GetMedicalScribeStreamCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetMedicalScribeStreamCommandOutput>;
  getMedicalScribeStream(
    args: GetMedicalScribeStreamCommandInput,
    cb: (err: any, data?: GetMedicalScribeStreamCommandOutput) => void
  ): void;
  getMedicalScribeStream(
    args: GetMedicalScribeStreamCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetMedicalScribeStreamCommandOutput) => void
  ): void;
  startCallAnalyticsStreamTranscription(
    args: StartCallAnalyticsStreamTranscriptionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<StartCallAnalyticsStreamTranscriptionCommandOutput>;
  startCallAnalyticsStreamTranscription(
    args: StartCallAnalyticsStreamTranscriptionCommandInput,
    cb: (
      err: any,
      data?: StartCallAnalyticsStreamTranscriptionCommandOutput
    ) => void
  ): void;
  startCallAnalyticsStreamTranscription(
    args: StartCallAnalyticsStreamTranscriptionCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: StartCallAnalyticsStreamTranscriptionCommandOutput
    ) => void
  ): void;
  startMedicalScribeStream(
    args: StartMedicalScribeStreamCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<StartMedicalScribeStreamCommandOutput>;
  startMedicalScribeStream(
    args: StartMedicalScribeStreamCommandInput,
    cb: (err: any, data?: StartMedicalScribeStreamCommandOutput) => void
  ): void;
  startMedicalScribeStream(
    args: StartMedicalScribeStreamCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: StartMedicalScribeStreamCommandOutput) => void
  ): void;
  startMedicalStreamTranscription(
    args: StartMedicalStreamTranscriptionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<StartMedicalStreamTranscriptionCommandOutput>;
  startMedicalStreamTranscription(
    args: StartMedicalStreamTranscriptionCommandInput,
    cb: (err: any, data?: StartMedicalStreamTranscriptionCommandOutput) => void
  ): void;
  startMedicalStreamTranscription(
    args: StartMedicalStreamTranscriptionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: StartMedicalStreamTranscriptionCommandOutput) => void
  ): void;
  startStreamTranscription(
    args: StartStreamTranscriptionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<StartStreamTranscriptionCommandOutput>;
  startStreamTranscription(
    args: StartStreamTranscriptionCommandInput,
    cb: (err: any, data?: StartStreamTranscriptionCommandOutput) => void
  ): void;
  startStreamTranscription(
    args: StartStreamTranscriptionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: StartStreamTranscriptionCommandOutput) => void
  ): void;
}
export declare class TranscribeStreaming
  extends TranscribeStreamingClient
  implements TranscribeStreaming {}
