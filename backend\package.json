{"name": "backend", "version": "1.0.0", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node src/server.ts", "build": "tsc", "watch": "tsc -w", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Donny解梦平台后端API", "dependencies": {"@supabase/supabase-js": "^2.49.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.15.21", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9"}}