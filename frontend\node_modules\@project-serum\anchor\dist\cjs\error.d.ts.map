{"version": 3, "file": "error.d.ts", "sourceRoot": "", "sources": ["../../src/error.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAG5C,qBAAa,QAAS,SAAQ,KAAK;gBACrB,OAAO,EAAE,MAAM;CAI5B;AAED,UAAU,SAAS;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,UAAU,QAAQ;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;CACd;AAED,KAAK,MAAM,GAAG,MAAM,GAAG,QAAQ,CAAC;AAChC,KAAK,oBAAoB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC7C,KAAK,kBAAkB,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACjD,KAAK,cAAc,GAAG,oBAAoB,GAAG,kBAAkB,CAAC;AAEhE,qBAAa,iBAAiB;IAChB,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE;gBAAlB,KAAK,EAAE,SAAS,EAAE;WAEzB,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE;CAmBnC;AAED,qBAAa,WAAY,SAAQ,KAAK;IAYlC,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE;IAC5B,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE;IAZzB,QAAQ,CAAC,KAAK,EAAE;QACd,SAAS,EAAE,SAAS,CAAC;QACrB,YAAY,EAAE,MAAM,CAAC;QACrB,cAAc,CAAC,EAAE,cAAc,CAAC;QAChC,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB,CAAC;IACF,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAoB;gBAGrD,SAAS,EAAE,SAAS,EACpB,YAAY,EAAE,MAAM,EACX,SAAS,EAAE,MAAM,EAAE,EACnB,IAAI,EAAE,MAAM,EAAE,EACvB,MAAM,CAAC,EAAE,MAAM,EACf,cAAc,CAAC,EAAE,cAAc;WAOnB,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE;IA4GlC,IAAI,OAAO,IAAI,SAAS,CAIvB;IAED,IAAI,iBAAiB,IAAI,SAAS,EAAE,CAEnC;IAEM,QAAQ,IAAI,MAAM;CAG1B;AAGD,qBAAa,YAAa,SAAQ,KAAK;IAInC,QAAQ,CAAC,IAAI,EAAE,MAAM;IACrB,QAAQ,CAAC,GAAG,EAAE,MAAM;IACpB,QAAQ,CAAC,IAAI,CAAC;IALhB,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAoB;gBAG7C,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM,EACX,IAAI,CAAC,sBAAU;WAQZ,KAAK,CACjB,GAAG,EAAE,GAAG,EACR,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAC7B,YAAY,GAAG,IAAI;IA2CtB,IAAI,OAAO,IAAI,SAAS,GAAG,SAAS,CAInC;IAED,IAAI,iBAAiB,IAAI,SAAS,EAAE,GAAG,SAAS,CAE/C;IAEM,QAAQ,IAAI,MAAM;CAG1B;AAED,wBAAgB,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,OAmCtE;AAED,eAAO,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAuEzB,CAAC;AAEF,eAAO,MAAM,gBAAgB,qBA8J3B,CAAC"}