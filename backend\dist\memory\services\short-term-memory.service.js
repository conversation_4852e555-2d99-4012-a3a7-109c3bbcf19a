"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ShortTermMemoryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShortTermMemoryService = void 0;
const common_1 = require("@nestjs/common");
const memory_types_1 = require("../memory.types");
const database_service_1 = require("../../database/database.service");
let ShortTermMemoryService = ShortTermMemoryService_1 = class ShortTermMemoryService {
    constructor(databaseService) {
        this.databaseService = databaseService;
        this.logger = new common_1.Logger(ShortTermMemoryService_1.name);
        this.cache = new Map();
        this.maxCacheSize = 1000;
        this.cacheTTL = 30 * 60 * 1000;
        this.maxContextSize = 20;
        this.defaultExpirationHours = 1;
        this.logger.log('短期记忆服务初始化完成');
        setInterval(() => this.cleanupCache(), 5 * 60 * 1000);
    }
    async store(memoryData) {
        this.logger.debug(`存储短期记忆 - 用户: ${memoryData.userId}, Agent: ${memoryData.agentType}`);
        try {
            const memory = {
                ...memoryData,
                id: memoryData.id || `stm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                memoryType: memory_types_1.MemoryType.SHORT_TERM,
                activationLevel: memoryData.activationLevel || 1.0,
                lastActivated: memoryData.lastActivated || new Date(),
                accessCount: 0,
                createdAt: new Date(),
                updatedAt: new Date(),
                lastAccessedAt: new Date(),
                expiresAt: memoryData.expiresAt || new Date(Date.now() + this.defaultExpirationHours * 60 * 60 * 1000),
            };
            const { data, error } = await this.databaseService.executeWithPermission(memory.agentType, {
                table: 'agent_memories',
                operation: 'insert',
                data: {
                    id: memory.id,
                    agent_type: memory.agentType,
                    user_id: memory.userId,
                    session_id: memory.sessionId,
                    memory_type: memory.memoryType,
                    importance: memory.importance,
                    content: memory.content,
                    metadata: {
                        ...memory.metadata,
                        activationLevel: memory.activationLevel,
                        lastActivated: memory.lastActivated.toISOString(),
                    },
                    access_count: memory.accessCount,
                    last_accessed_at: memory.lastAccessedAt.toISOString(),
                    created_at: memory.createdAt.toISOString(),
                    updated_at: memory.updatedAt.toISOString(),
                    expires_at: memory.expiresAt?.toISOString(),
                },
            });
            if (error) {
                throw new Error(`数据库存储失败: ${error.message}`);
            }
            this.addToCache(memory);
            this.logger.debug(`短期记忆存储成功 - ID: ${memory.id}`);
            return memory;
        }
        catch (error) {
            this.logger.error(`存储短期记忆失败: ${error.message}`);
            throw error;
        }
    }
    async retrieve(query) {
        this.logger.debug(`检索短期记忆 - 查询: ${JSON.stringify(query)}`);
        try {
            const cachedResults = this.searchCache(query);
            if (cachedResults.length > 0 && !query.searchText) {
                return this.limitResults(cachedResults, query);
            }
            const dbResults = await this.queryDatabase(query);
            dbResults.forEach(memory => this.addToCache(memory));
            await this.activateMemories(dbResults);
            return this.limitResults(dbResults, query);
        }
        catch (error) {
            this.logger.error(`检索短期记忆失败: ${error.message}`);
            throw error;
        }
    }
    async getSessionContext(agentType, userId, sessionId) {
        this.logger.debug(`获取会话上下文 - Agent: ${agentType}, 用户: ${userId}, 会话: ${sessionId}`);
        try {
            const query = {
                agentType,
                userId,
                sessionId,
                memoryType: memory_types_1.MemoryType.SHORT_TERM,
                limit: this.maxContextSize,
            };
            const memories = await this.retrieve(query);
            return memories.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        }
        catch (error) {
            this.logger.error(`获取会话上下文失败: ${error.message}`);
            throw error;
        }
    }
    async update(memoryId, updates) {
        this.logger.debug(`更新短期记忆 - ID: ${memoryId}`);
        try {
            const existing = await this.findById(memoryId);
            if (!existing) {
                throw new Error(`短期记忆不存在: ${memoryId}`);
            }
            const updated = {
                ...existing,
                ...updates,
                updatedAt: new Date(),
            };
            const { error } = await this.databaseService.executeWithPermission(updated.agentType, {
                table: 'agent_memories',
                operation: 'update',
                data: {
                    content: updated.content,
                    metadata: {
                        ...updated.metadata,
                        activationLevel: updated.activationLevel,
                        lastActivated: updated.lastActivated.toISOString(),
                    },
                    importance: updated.importance,
                    access_count: updated.accessCount,
                    last_accessed_at: updated.lastAccessedAt.toISOString(),
                    updated_at: updated.updatedAt.toISOString(),
                },
                filters: { id: memoryId },
            });
            if (error) {
                throw new Error(`数据库更新失败: ${error.message}`);
            }
            this.addToCache(updated);
            return updated;
        }
        catch (error) {
            this.logger.error(`更新短期记忆失败: ${error.message}`);
            throw error;
        }
    }
    async delete(memoryId) {
        this.logger.debug(`删除短期记忆 - ID: ${memoryId}`);
        try {
            const { error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'delete',
                filters: { id: memoryId },
            });
            if (error) {
                this.logger.warn(`数据库删除失败: ${error.message}`);
                return false;
            }
            this.cache.delete(memoryId);
            return true;
        }
        catch (error) {
            this.logger.error(`删除短期记忆失败: ${error.message}`);
            return false;
        }
    }
    async findById(memoryId) {
        const cached = this.cache.get(memoryId);
        if (cached && !this.isCacheExpired(cached)) {
            return cached;
        }
        try {
            const { data, error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'select',
                filters: {
                    id: memoryId,
                    memory_type: memory_types_1.MemoryType.SHORT_TERM,
                },
            });
            if (error || !data || data.length === 0) {
                return null;
            }
            const memory = this.mapDbRowToMemory(data[0]);
            this.addToCache(memory);
            return memory;
        }
        catch (error) {
            this.logger.error(`查找短期记忆失败: ${error.message}`);
            return null;
        }
    }
    async cleanupExpiredMemories() {
        this.logger.log('开始清理过期的短期记忆');
        try {
            const now = new Date();
            const { data, error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'delete',
                filters: {
                    memory_type: memory_types_1.MemoryType.SHORT_TERM,
                    expires_at: { $lt: now.toISOString() },
                },
            });
            const deletedCount = data?.affectedRows || 0;
            this.logger.log(`清理完成 - 删除了${deletedCount}条过期短期记忆`);
            this.cleanupCache();
            return deletedCount;
        }
        catch (error) {
            this.logger.error(`清理过期记忆失败: ${error.message}`);
            return 0;
        }
    }
    async getConsolidationCandidates(limit = 100) {
        try {
            const query = {
                memoryType: memory_types_1.MemoryType.SHORT_TERM,
                limit,
            };
            const results = await this.queryDatabase(query);
            const now = new Date();
            const sixHoursAgo = new Date(now.getTime() - 6 * 60 * 60 * 1000);
            return results.filter(memory => memory.accessCount >= 3 &&
                memory.activationLevel >= 1.5 &&
                memory.createdAt <= sixHoursAgo);
        }
        catch (error) {
            this.logger.error(`获取巩固候选失败: ${error.message}`);
            return [];
        }
    }
    async getStats(agentType, userId) {
        try {
            const filters = { memory_type: memory_types_1.MemoryType.SHORT_TERM };
            if (agentType)
                filters.agent_type = agentType;
            if (userId)
                filters.user_id = userId;
            const { data, error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'select',
                columns: 'importance, metadata, created_at, LENGTH(content::text) as content_size',
                filters,
            });
            if (error) {
                throw new Error(`获取统计失败: ${error.message}`);
            }
            const memories = data || [];
            const stats = {
                total: memories.length,
                byImportance: {},
                byCategory: {},
                storageUsed: 0,
                averageImportance: 0,
                oldestMemory: undefined,
                newestMemory: undefined,
            };
            if (memories.length === 0) {
                return stats;
            }
            let totalImportance = 0;
            let oldestDate = new Date();
            let newestDate = new Date(0);
            for (const memory of memories) {
                const importance = memory.importance || memory_types_1.MemoryImportance.NORMAL;
                stats.byImportance[importance] = (stats.byImportance[importance] || 0) + 1;
                totalImportance += importance;
                const category = memory.metadata?.category || 'general';
                stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
                stats.storageUsed += memory.content_size || 0;
                const createdAt = new Date(memory.created_at);
                if (createdAt < oldestDate)
                    oldestDate = createdAt;
                if (createdAt > newestDate)
                    newestDate = createdAt;
            }
            stats.averageImportance = totalImportance / memories.length;
            stats.oldestMemory = oldestDate;
            stats.newestMemory = newestDate;
            return stats;
        }
        catch (error) {
            this.logger.error(`获取统计信息失败: ${error.message}`);
            throw error;
        }
    }
    addToCache(memory) {
        if (this.cache.size >= this.maxCacheSize) {
            this.cleanupCache();
        }
        this.cache.set(memory.id, memory);
    }
    searchCache(query) {
        const results = [];
        for (const memory of this.cache.values()) {
            if (this.isCacheExpired(memory)) {
                continue;
            }
            if (this.matchesQuery(memory, query)) {
                results.push(memory);
            }
        }
        return results;
    }
    isCacheExpired(memory) {
        const now = new Date();
        return (now.getTime() - memory.updatedAt.getTime()) > this.cacheTTL;
    }
    cleanupCache() {
        const now = new Date();
        const toDelete = [];
        for (const [id, memory] of this.cache.entries()) {
            if (this.isCacheExpired(memory) || (memory.expiresAt && memory.expiresAt < now)) {
                toDelete.push(id);
            }
        }
        toDelete.forEach(id => this.cache.delete(id));
    }
    matchesQuery(memory, query) {
        if (query.agentType && memory.agentType !== query.agentType)
            return false;
        if (query.userId && memory.userId !== query.userId)
            return false;
        if (query.sessionId && memory.sessionId !== query.sessionId)
            return false;
        if (query.category && memory.metadata?.category !== query.category)
            return false;
        if (query.importance && memory.importance !== query.importance)
            return false;
        if (query.timeRange) {
            if (memory.createdAt < query.timeRange.start || memory.createdAt > query.timeRange.end) {
                return false;
            }
        }
        return true;
    }
    async activateMemories(memories) {
        for (const memory of memories) {
            try {
                const newActivationLevel = Math.min(memory.activationLevel + 0.1, 2.0);
                await this.update(memory.id, {
                    activationLevel: newActivationLevel,
                    lastActivated: new Date(),
                    accessCount: memory.accessCount + 1,
                    lastAccessedAt: new Date(),
                });
            }
            catch (error) {
                this.logger.warn(`激活记忆失败 - 记忆ID: ${memory.id}, 错误: ${error.message}`);
            }
        }
    }
    limitResults(results, query) {
        results.sort((a, b) => {
            if (a.activationLevel !== b.activationLevel) {
                return b.activationLevel - a.activationLevel;
            }
            return b.lastActivated.getTime() - a.lastActivated.getTime();
        });
        return query.limit
            ? results.slice(query.offset || 0, (query.offset || 0) + query.limit)
            : results;
    }
    async queryDatabase(query) {
        const filters = { memory_type: memory_types_1.MemoryType.SHORT_TERM };
        if (query.agentType)
            filters.agent_type = query.agentType;
        if (query.userId)
            filters.user_id = query.userId;
        if (query.sessionId)
            filters.session_id = query.sessionId;
        if (query.category)
            filters['metadata->category'] = query.category;
        if (query.importance)
            filters.importance = query.importance;
        if (query.timeRange) {
            filters.created_at = {
                $gte: query.timeRange.start.toISOString(),
                $lte: query.timeRange.end.toISOString(),
            };
        }
        const { data, error } = await this.databaseService.executeWithPermission(query.agentType || 'almighty', {
            table: 'agent_memories',
            operation: 'select',
            filters,
        });
        if (error) {
            throw new Error(`数据库查询失败: ${error.message}`);
        }
        return (data || []).map(row => this.mapDbRowToMemory(row));
    }
    mapDbRowToMemory(row) {
        return {
            id: row.id,
            agentType: row.agent_type,
            userId: row.user_id,
            sessionId: row.session_id,
            memoryType: row.memory_type,
            importance: row.importance,
            content: row.content,
            metadata: row.metadata,
            activationLevel: row.metadata?.activationLevel || 1.0,
            lastActivated: new Date(row.metadata?.lastActivated || row.created_at),
            accessCount: row.access_count || 0,
            lastAccessedAt: new Date(row.last_accessed_at),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at),
            expiresAt: row.expires_at ? new Date(row.expires_at) : undefined,
        };
    }
};
exports.ShortTermMemoryService = ShortTermMemoryService;
exports.ShortTermMemoryService = ShortTermMemoryService = ShortTermMemoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], ShortTermMemoryService);
//# sourceMappingURL=short-term-memory.service.js.map