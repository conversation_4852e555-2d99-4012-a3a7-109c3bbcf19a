import { LanguageModelV1, ProviderV1 } from '@ai-sdk/provider';
import { FetchFunction } from '@ai-sdk/provider-utils';
import { z } from 'zod';

type AnthropicMessagesModelId = 'claude-3-5-sonnet-latest' | 'claude-3-5-sonnet-20241022' | 'claude-3-5-sonnet-20240620' | 'claude-3-5-haiku-latest' | 'claude-3-5-haiku-20241022' | 'claude-3-opus-latest' | 'claude-3-opus-20240229' | 'claude-3-sonnet-20240229' | 'claude-3-haiku-20240307' | (string & {});
interface AnthropicMessagesSettings {
    /**
  Only sample from the top K options for each subsequent token.
  
  Used to remove "long tail" low probability responses.
  Recommended for advanced use cases only. You usually only need to use temperature.
  
  @deprecated use the topK setting on the request instead.
     */
    topK?: number;
    /**
  Enable Anthropic cache control (beta feature). This will add the beta header and
  allow you to use provider-specific cacheControl metadata.
     */
    cacheControl?: boolean;
}

type AnthropicMessagesConfig = {
    provider: string;
    baseURL: string;
    headers: () => Record<string, string | undefined>;
    fetch?: FetchFunction;
};
declare class AnthropicMessagesLanguageModel implements LanguageModelV1 {
    readonly specificationVersion = "v1";
    readonly defaultObjectGenerationMode = "tool";
    readonly supportsImageUrls = false;
    readonly modelId: AnthropicMessagesModelId;
    readonly settings: AnthropicMessagesSettings;
    private readonly config;
    constructor(modelId: AnthropicMessagesModelId, settings: AnthropicMessagesSettings, config: AnthropicMessagesConfig);
    get provider(): string;
    private getArgs;
    private getHeaders;
    doGenerate(options: Parameters<LanguageModelV1['doGenerate']>[0]): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>>;
    doStream(options: Parameters<LanguageModelV1['doStream']>[0]): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>>;
}

type ExecuteFunction<PARAMETERS, RESULT> = undefined | ((args: PARAMETERS, options: {
    abortSignal?: AbortSignal;
}) => Promise<RESULT>);
type ToolResultContent = Array<{
    type: 'text';
    text: string;
} | {
    type: 'image';
    data: string;
    mimeType?: string;
}>;
declare const Bash20241022Parameters: z.ZodObject<{
    command: z.ZodString;
    restart: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    command: string;
    restart?: boolean | undefined;
}, {
    command: string;
    restart?: boolean | undefined;
}>;
/**
 * Creates a tool for running a bash command. Must have name "bash".
 *
 * Image results are supported.
 *
 * @param execute - The function to execute the tool. Optional.
 */
declare function bashTool_20241022<RESULT>(options?: {
    execute?: ExecuteFunction<{
        /**
         * The bash command to run. Required unless the tool is being restarted.
         */
        command: string;
        /**
         * Specifying true will restart this tool. Otherwise, leave this unspecified.
         */
        restart?: boolean;
    }, RESULT>;
    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;
}): {
    type: 'provider-defined';
    id: 'anthropic.bash_20241022';
    args: {};
    parameters: typeof Bash20241022Parameters;
    execute: ExecuteFunction<z.infer<typeof Bash20241022Parameters>, RESULT>;
    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;
};
declare const TextEditor20241022Parameters: z.ZodObject<{
    command: z.ZodEnum<["view", "create", "str_replace", "insert", "undo_edit"]>;
    path: z.ZodString;
    file_text: z.ZodOptional<z.ZodString>;
    insert_line: z.ZodOptional<z.ZodNumber>;
    new_str: z.ZodOptional<z.ZodString>;
    old_str: z.ZodOptional<z.ZodString>;
    view_range: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
}, "strip", z.ZodTypeAny, {
    path: string;
    command: "view" | "create" | "str_replace" | "insert" | "undo_edit";
    file_text?: string | undefined;
    insert_line?: number | undefined;
    new_str?: string | undefined;
    old_str?: string | undefined;
    view_range?: number[] | undefined;
}, {
    path: string;
    command: "view" | "create" | "str_replace" | "insert" | "undo_edit";
    file_text?: string | undefined;
    insert_line?: number | undefined;
    new_str?: string | undefined;
    old_str?: string | undefined;
    view_range?: number[] | undefined;
}>;
/**
 * Creates a tool for editing text. Must have name "str_replace_editor".
 *
 * Image results are supported.
 *
 * @param execute - The function to execute the tool. Optional.
 */
declare function textEditorTool_20241022<RESULT>(options?: {
    execute?: ExecuteFunction<{
        /**
         * The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.
         */
        command: 'view' | 'create' | 'str_replace' | 'insert' | 'undo_edit';
        /**
         * Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`.
         */
        path: string;
        /**
         * Required parameter of `create` command, with the content of the file to be created.
         */
        file_text?: string;
        /**
         * Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.
         */
        insert_line?: number;
        /**
         * Optional parameter of `str_replace` command containing the new string (if not given, no string will be added). Required parameter of `insert` command containing the string to insert.
         */
        new_str?: string;
        /**
         * Required parameter of `str_replace` command containing the string in `path` to replace.
         */
        old_str?: string;
        /**
         * Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.
         */
        view_range?: number[];
    }, RESULT>;
    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;
}): {
    type: 'provider-defined';
    id: 'anthropic.text_editor_20241022';
    args: {};
    parameters: typeof TextEditor20241022Parameters;
    execute: ExecuteFunction<z.infer<typeof TextEditor20241022Parameters>, RESULT>;
    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;
};
declare const Computer20241022Parameters: z.ZodObject<{
    action: z.ZodEnum<["key", "type", "mouse_move", "left_click", "left_click_drag", "right_click", "middle_click", "double_click", "screenshot", "cursor_position"]>;
    coordinate: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    text: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    action: "type" | "key" | "mouse_move" | "left_click" | "left_click_drag" | "right_click" | "middle_click" | "double_click" | "screenshot" | "cursor_position";
    text?: string | undefined;
    coordinate?: number[] | undefined;
}, {
    action: "type" | "key" | "mouse_move" | "left_click" | "left_click_drag" | "right_click" | "middle_click" | "double_click" | "screenshot" | "cursor_position";
    text?: string | undefined;
    coordinate?: number[] | undefined;
}>;
/**
 * Creates a tool for executing actions on a computer. Must have name "computer".
 *
 * Image results are supported.
 *
 * @param displayWidthPx - The width of the display being controlled by the model in pixels.
 * @param displayHeightPx - The height of the display being controlled by the model in pixels.
 * @param displayNumber - The display number to control (only relevant for X11 environments). If specified, the tool will be provided a display number in the tool definition.
 * @param execute - The function to execute the tool. Optional.
 */
declare function computerTool_20241022<RESULT>(options: {
    displayWidthPx: number;
    displayHeightPx: number;
    displayNumber?: number;
    execute?: ExecuteFunction<{
        /**
         * The action to perform. The available actions are:
         * - `key`: Press a key or key-combination on the keyboard.
         *   - This supports xdotool's `key` syntax.
         *   - Examples: "a", "Return", "alt+Tab", "ctrl+s", "Up", "KP_0" (for the numpad 0 key).
         * - `type`: Type a string of text on the keyboard.
         * - `cursor_position`: Get the current (x, y) pixel coordinate of the cursor on the screen.
         * - `mouse_move`: Move the cursor to a specified (x, y) pixel coordinate on the screen.
         * - `left_click`: Click the left mouse button.
         * - `left_click_drag`: Click and drag the cursor to a specified (x, y) pixel coordinate on the screen.
         * - `right_click`: Click the right mouse button.
         * - `middle_click`: Click the middle mouse button.
         * - `double_click`: Double-click the left mouse button.
         * - `screenshot`: Take a screenshot of the screen.
         */
        action: 'key' | 'type' | 'mouse_move' | 'left_click' | 'left_click_drag' | 'right_click' | 'middle_click' | 'double_click' | 'screenshot' | 'cursor_position';
        /**
         * (x, y): The x (pixels from the left edge) and y (pixels from the top edge) coordinates to move the mouse to. Required only by `action=mouse_move` and `action=left_click_drag`.
         */
        coordinate?: number[];
        /**
         * Required only by `action=type` and `action=key`.
         */
        text?: string;
    }, RESULT>;
    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;
}): {
    type: 'provider-defined';
    id: 'anthropic.computer_20241022';
    args: {};
    parameters: typeof Computer20241022Parameters;
    execute: ExecuteFunction<z.infer<typeof Computer20241022Parameters>, RESULT>;
    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;
};
declare const anthropicTools: {
    bash_20241022: typeof bashTool_20241022;
    textEditor_20241022: typeof textEditorTool_20241022;
    computer_20241022: typeof computerTool_20241022;
};

interface AnthropicProvider extends ProviderV1 {
    /**
  Creates a model for text generation.
  */
    (modelId: AnthropicMessagesModelId, settings?: AnthropicMessagesSettings): LanguageModelV1;
    /**
  Creates a model for text generation.
  */
    languageModel(modelId: AnthropicMessagesModelId, settings?: AnthropicMessagesSettings): LanguageModelV1;
    /**
  @deprecated Use `.languageModel()` instead.
  */
    chat(modelId: AnthropicMessagesModelId, settings?: AnthropicMessagesSettings): LanguageModelV1;
    /**
  @deprecated Use `.languageModel()` instead.
     */
    messages(modelId: AnthropicMessagesModelId, settings?: AnthropicMessagesSettings): LanguageModelV1;
    /**
  Anthropic-specific computer use tool.
     */
    tools: typeof anthropicTools;
}
interface AnthropicProviderSettings {
    /**
  Use a different URL prefix for API calls, e.g. to use proxy servers.
  The default prefix is `https://api.anthropic.com/v1`.
     */
    baseURL?: string;
    /**
  @deprecated Use `baseURL` instead.
     */
    baseUrl?: string;
    /**
  API key that is being send using the `x-api-key` header.
  It defaults to the `ANTHROPIC_API_KEY` environment variable.
     */
    apiKey?: string;
    /**
  Custom headers to include in the requests.
       */
    headers?: Record<string, string>;
    /**
  Custom fetch implementation. You can use it as a middleware to intercept requests,
  or to provide a custom fetch implementation for e.g. testing.
      */
    fetch?: FetchFunction;
    generateId?: () => string;
}
/**
Create an Anthropic provider instance.
 */
declare function createAnthropic(options?: AnthropicProviderSettings): AnthropicProvider;
/**
Default Anthropic provider instance.
 */
declare const anthropic: AnthropicProvider;

/**
 * @deprecated Use `createAnthropic` instead.
 */
declare class Anthropic {
    /**
     * Base URL for Anthropic API calls.
     */
    readonly baseURL: string;
    readonly apiKey?: string;
    readonly headers?: Record<string, string>;
    /**
     * Creates a new Anthropic provider instance.
     */
    constructor(options?: AnthropicProviderSettings);
    private get baseConfig();
    /**
     * @deprecated Use `chat()` instead.
     */
    messages(modelId: AnthropicMessagesModelId, settings?: AnthropicMessagesSettings): AnthropicMessagesLanguageModel;
    chat(modelId: AnthropicMessagesModelId, settings?: AnthropicMessagesSettings): AnthropicMessagesLanguageModel;
}

export { Anthropic, type AnthropicProvider, type AnthropicProviderSettings, anthropic, createAnthropic };
