import { API_BASE_URL, getHeaders, handleResponse, setToken, removeToken } from './config';

// 验证钱包签名
export const verifySignature = async (walletAddress: string, signature: string, message: string) => {
  const response = await fetch(`${API_BASE_URL}/auth/verify-signature`, {
    method: 'POST',
    headers: getHeaders(),
    body: JSON.stringify({
      walletAddress,
      signature,
      message,
    }),
  });
  
  const data = await handleResponse(response);
  
  if (data.status === 'success' && data.data.token) {
    setToken(data.data.token);
  }
  
  return data;
};

// 更新用户名
export const updateUsername = async (userId: string, username: string, token: string) => {
  const response = await fetch(`${API_BASE_URL}/auth/username`, {
    method: 'PATCH',
    headers: getHeaders(token),
    body: JSON.stringify({
      userId,
      username,
    }),
  });
  
  return handleResponse(response);
};

// 登出
export const logout = () => {
  removeToken();
};
