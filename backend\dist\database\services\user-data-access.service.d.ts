import { DatabaseService } from '../database.service';
export interface User {
    id: string;
    username?: string;
    primary_wallet_address: string;
    registration_time: string;
    last_login_time?: string;
    status: 'active' | 'inactive' | 'banned';
    chat_round_count: number;
    user_status: 'guest' | 'registered';
    created_at: string;
    updated_at: string;
}
export interface WalletAddress {
    id: string;
    user_id: string;
    wallet_address: string;
    is_primary: boolean;
    created_at: string;
}
export interface UserSession {
    id: string;
    user_id: string;
    wallet_address: string;
    challenge_message?: string;
    challenge_timestamp?: string;
    is_verified: boolean;
    expires_at?: string;
    created_at: string;
}
export declare class UserDataAccessService {
    private databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService);
    findUserByWallet(walletAddress: string): Promise<User | null>;
    findUserById(userId: string): Promise<User | null>;
    createUser(walletAddress: string, username?: string): Promise<{
        user: User;
        error?: any;
    }>;
    updateLastLogin(userId: string): Promise<{
        success: boolean;
        error?: any;
    }>;
    incrementChatRound(userId: string): Promise<{
        success: boolean;
        error?: any;
    }>;
    getUserWallets(userId: string): Promise<WalletAddress[]>;
    addWalletToUser(userId: string, walletAddress: string, isPrimary?: boolean): Promise<{
        walletId: string;
        error?: any;
    }>;
    createUserSession(userId: string, walletAddress: string, challengeMessage: string): Promise<{
        sessionId: string;
        error?: any;
    }>;
    verifyUserSession(sessionId: string): Promise<{
        session: UserSession;
        error?: any;
    }>;
    getUserSession(sessionId: string): Promise<UserSession | null>;
}
