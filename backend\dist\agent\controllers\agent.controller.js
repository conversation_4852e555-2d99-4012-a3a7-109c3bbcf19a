"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AgentController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentController = exports.DreamRequestDto = exports.CreateAgentDto = exports.SendMessageDto = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const agent_runtime_service_1 = require("../services/agent-runtime.service");
const almighty_donny_service_1 = require("../services/almighty-donny.service");
class SendMessageDto {
}
exports.SendMessageDto = SendMessageDto;
class CreateAgentDto {
}
exports.CreateAgentDto = CreateAgentDto;
class DreamRequestDto {
}
exports.DreamRequestDto = DreamRequestDto;
let AgentController = AgentController_1 = class AgentController {
    constructor(agentRuntimeService, almightyDonnyService) {
        this.agentRuntimeService = agentRuntimeService;
        this.almightyDonnyService = almightyDonnyService;
        this.logger = new common_1.Logger(AgentController_1.name);
    }
    async chatWithAlmightyDonny(dto) {
        this.logger.log(`接收到Almighty Donny消息 - 用户: ${dto.userId}`);
        try {
            if (!dto.userId || !dto.content) {
                throw new common_1.BadRequestException('userId和content是必填字段');
            }
            const response = await this.almightyDonnyService.handleUserMessage(dto.userId, dto.content, dto.messageId);
            return {
                success: true,
                data: response,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Almighty Donny消息处理失败: ${error.message}`);
            throw new common_1.BadRequestException(`消息处理失败: ${error.message}`);
        }
    }
    async chatWithAgent(agentId, dto) {
        this.logger.log(`接收到Agent消息 - Agent: ${agentId}, 用户: ${dto.userId}`);
        try {
            if (!dto.userId || !dto.content) {
                throw new common_1.BadRequestException('userId和content是必填字段');
            }
            const message = {
                id: dto.messageId || `msg_${Date.now()}`,
                userId: dto.userId,
                content: dto.content,
                type: 'text',
                timestamp: new Date(),
            };
            const response = await this.agentRuntimeService.processMessage(agentId, message);
            return {
                success: true,
                data: response,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Agent消息处理失败: ${error.message}`);
            if (error.message.includes('not found')) {
                throw new common_1.NotFoundException(`Agent ${agentId} 不存在`);
            }
            throw new common_1.BadRequestException(`消息处理失败: ${error.message}`);
        }
    }
    async createAgent(dto) {
        this.logger.log(`创建Agent请求 - 类型: ${dto.agentType}, 名称: ${dto.name}`);
        try {
            if (!dto.agentType || !dto.name) {
                throw new common_1.BadRequestException('agentType和name是必填字段');
            }
            const config = {
                agentId: `${dto.agentType}_${Date.now()}`,
                agentType: dto.agentType,
                name: dto.name,
                persona: dto.persona || {},
                tools: dto.tools || [],
                modelProvider: dto.modelProvider || 'openai',
                apiKey: dto.apiKey,
            };
            const agentId = await this.agentRuntimeService.createAgent(config);
            return {
                success: true,
                data: {
                    agentId,
                    agentType: dto.agentType,
                    name: dto.name,
                    status: 'created',
                },
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`创建Agent失败: ${error.message}`);
            throw new common_1.BadRequestException(`创建Agent失败: ${error.message}`);
        }
    }
    async getAgentStatus(agentId) {
        this.logger.log(`获取Agent状态 - Agent: ${agentId}`);
        try {
            const status = this.agentRuntimeService.getAgentStatus(agentId);
            if (!status) {
                throw new common_1.NotFoundException(`Agent ${agentId} 不存在`);
            }
            return {
                success: true,
                data: status,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`获取Agent状态失败: ${error.message}`);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException(`获取状态失败: ${error.message}`);
        }
    }
    async getActiveAgents() {
        this.logger.log('获取所有活跃的Agent');
        try {
            const activeAgents = this.agentRuntimeService.getActiveAgents();
            return {
                success: true,
                data: {
                    agents: activeAgents,
                    count: activeAgents.length,
                },
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`获取活跃Agent失败: ${error.message}`);
            throw new common_1.BadRequestException(`获取失败: ${error.message}`);
        }
    }
    async stopAgent(agentId) {
        this.logger.log(`停止Agent - Agent: ${agentId}`);
        try {
            await this.agentRuntimeService.stopAgent(agentId);
            return {
                success: true,
                data: {
                    agentId,
                    status: 'stopped',
                },
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`停止Agent失败: ${error.message}`);
            if (error.message.includes('not found')) {
                throw new common_1.NotFoundException(`Agent ${agentId} 不存在`);
            }
            throw new common_1.BadRequestException(`停止失败: ${error.message}`);
        }
    }
    async healthCheck() {
        this.logger.log('Agent系统健康检查');
        try {
            const health = await this.agentRuntimeService.healthCheck();
            return {
                success: true,
                data: health,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`健康检查失败: ${error.message}`);
            throw new common_1.BadRequestException(`健康检查失败: ${error.message}`);
        }
    }
    async submitDreamRequest(dto) {
        this.logger.log(`接收到解梦请求 - 用户: ${dto.userId}`);
        try {
            if (!dto.userId || !dto.dreamContent) {
                throw new common_1.BadRequestException('userId和dreamContent是必填字段');
            }
            const response = await this.almightyDonnyService.handleUserMessage(dto.userId, dto.dreamContent, `dream_${Date.now()}`);
            return {
                success: true,
                data: response,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`处理解梦请求失败: ${error.message}`);
            throw new common_1.BadRequestException(`处理失败: ${error.message}`);
        }
    }
    async getUserFlowState(userId) {
        this.logger.log(`获取用户流程状态 - 用户: ${userId}`);
        try {
            const flowState = this.almightyDonnyService.getUserFlowState(userId);
            if (!flowState) {
                throw new common_1.NotFoundException(`用户 ${userId} 的流程状态不存在`);
            }
            return {
                success: true,
                data: flowState,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`获取用户流程状态失败: ${error.message}`);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException(`获取失败: ${error.message}`);
        }
    }
    async cleanupExpiredFlows() {
        this.logger.log('清理过期用户流程');
        try {
            this.almightyDonnyService.cleanupExpiredFlows();
            return {
                success: true,
                data: {
                    message: '过期流程清理完成',
                },
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`清理过期流程失败: ${error.message}`);
            throw new common_1.BadRequestException(`清理失败: ${error.message}`);
        }
    }
};
exports.AgentController = AgentController;
__decorate([
    (0, common_1.Post)('chat/almighty'),
    (0, swagger_1.ApiOperation)({ summary: '发送消息给Almighty Donny' }),
    (0, swagger_1.ApiBody)({ type: SendMessageDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '消息处理成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [SendMessageDto]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "chatWithAlmightyDonny", null);
__decorate([
    (0, common_1.Post)('chat/:agentId'),
    (0, swagger_1.ApiOperation)({ summary: '发送消息给特定Agent' }),
    (0, swagger_1.ApiParam)({ name: 'agentId', description: 'Agent ID' }),
    (0, swagger_1.ApiBody)({ type: SendMessageDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '消息处理成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent不存在' }),
    __param(0, (0, common_1.Param)('agentId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, SendMessageDto]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "chatWithAgent", null);
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: '创建新的Agent' }),
    (0, swagger_1.ApiBody)({ type: CreateAgentDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '创建失败' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [CreateAgentDto]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "createAgent", null);
__decorate([
    (0, common_1.Get)('status/:agentId'),
    (0, swagger_1.ApiOperation)({ summary: '获取Agent状态' }),
    (0, swagger_1.ApiParam)({ name: 'agentId', description: 'Agent ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '状态获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent不存在' }),
    __param(0, (0, common_1.Param)('agentId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "getAgentStatus", null);
__decorate([
    (0, common_1.Get)('active'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有活跃的Agent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "getActiveAgents", null);
__decorate([
    (0, common_1.Post)('stop/:agentId'),
    (0, swagger_1.ApiOperation)({ summary: '停止Agent' }),
    (0, swagger_1.ApiParam)({ name: 'agentId', description: 'Agent ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '停止成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent不存在' }),
    __param(0, (0, common_1.Param)('agentId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "stopAgent", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: 'Agent系统健康检查' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '健康检查成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "healthCheck", null);
__decorate([
    (0, common_1.Post)('dream/request'),
    (0, swagger_1.ApiOperation)({ summary: '提交解梦请求' }),
    (0, swagger_1.ApiBody)({ type: DreamRequestDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '解梦请求提交成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [DreamRequestDto]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "submitDreamRequest", null);
__decorate([
    (0, common_1.Get)('flow/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户流程状态' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '状态获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户流程不存在' }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "getUserFlowState", null);
__decorate([
    (0, common_1.Post)('cleanup/flows'),
    (0, swagger_1.ApiOperation)({ summary: '清理过期用户流程' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '清理完成' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "cleanupExpiredFlows", null);
exports.AgentController = AgentController = AgentController_1 = __decorate([
    (0, swagger_1.ApiTags)('Agent'),
    (0, common_1.Controller)('agent'),
    __metadata("design:paramtypes", [agent_runtime_service_1.AgentRuntimeService,
        almighty_donny_service_1.AlmightyDonnyService])
], AgentController);
//# sourceMappingURL=agent.controller.js.map