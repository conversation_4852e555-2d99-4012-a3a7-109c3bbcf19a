{"version": 3, "file": "data-isolation.middleware.js", "sourceRoot": "", "sources": ["../../src/security/data-isolation.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAyZA,8CAoBC;AA7aD,2CAAwF;AAiBjF,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAGlC;QAFiB,WAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAEpD,CAAC;IAEhB,KAAK,CAAC,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB;QACpE,IAAI,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;YAGD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC7C,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAChC,MAAM,IAAI,2BAAkB,CAAC,YAAY,CAAC,CAAC;YAC7C,CAAC;YAGD,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC;YAGtB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC3C,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC;YAGpB,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAClE,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;gBACvD,MAAM,IAAI,2BAAkB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACvD,CAAC;YAGD,IAAI,CAAC,yBAAyB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAG/C,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;YAEjD,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAEjD,IAAI,KAAK,YAAY,2BAAkB,EAAE,CAAC;gBACxC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,2BAAkB,CAAC,YAAY,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAKO,cAAc,CAAC,GAAY;QACjC,MAAM,WAAW,GAAG;YAClB,kBAAkB;YAClB,QAAQ;YACR,YAAY;YACZ,eAAe;SAChB,CAAC;QAEF,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/D,CAAC;IAKO,gBAAgB,CAAC,GAAyB;QAIhD,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC3E,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAc,CAAC;YAC5C,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,EAAE,EAAE,GAAG,SAAS,QAAQ;gBACxB,MAAM,EAAE,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC;aAC3C,CAAC;QACJ,CAAC;QAGD,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACnC,OAAO;gBACL,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS;gBACxB,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,QAAQ;gBACjC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC;aAC9D,CAAC;QACJ,CAAC;QAGD,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACrC,OAAO;gBACL,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,SAAsB;gBACtC,EAAE,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,QAAQ;gBAClC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,MAAgB,IAAI,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC;aACzE,CAAC;QACJ,CAAC;QAGD,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAc,CAAC;QACjE,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,IAAI,EAAE,eAAe;gBACrB,EAAE,EAAE,GAAG,eAAe,QAAQ;gBAC9B,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW,IAAI,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC;aACjF,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,eAAe,CAAC,GAAyB;QAE/C,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;QAElD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO;gBACL,EAAE,EAAE,MAAM;gBACV,aAAa,EAAE,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAW;aACzD,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,wBAAwB,CAAC,GAAyB;QACxD,OAAO,CACL,GAAG,CAAC,IAAI,EAAE,MAAM;YAChB,GAAG,CAAC,KAAK,EAAE,MAAgB;YAC3B,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW;YAClC,GAAG,CAAC,MAAM,EAAE,MAAM;YAClB,IAAI,CACL,CAAC;IACJ,CAAC;IAKO,oBAAoB,CAC1B,GAAyB,EACzB,SAA2D;QAE3D,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAExD,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QAGD,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAClC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC;QAGD,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAClE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC5E,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,SAAS,SAAS,CAAC,IAAI,WAAW,QAAQ,EAAE;aACrD,CAAC;QACJ,CAAC;QAGD,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,MAAM,QAAQ,WAAW,SAAS,CAAC,IAAI,SAAS;aACzD,CAAC;QACJ,CAAC;QAGD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACxE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACtE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,SAAS,SAAS,CAAC,IAAI,UAAU,QAAQ,QAAQ,MAAM,KAAK;aACrE,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAKO,uBAAuB,CAAC,IAAY;QAC1C,MAAM,gBAAgB,GAAG;YACvB,kCAAkC,EAAE,uBAAuB;YAC3D,uBAAuB,EAAE,uBAAuB;YAChD,YAAY,EAAE,mBAAmB;YACjC,eAAe,EAAE,uBAAuB;SACzC,CAAC;QAEF,KAAK,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACnE,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC3B,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,uBAAuB,CAAC,MAAc;QAC5C,MAAM,cAAc,GAAG;YACrB,KAAK,EAAE,QAAQ;YACf,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE,QAAQ;SACnB,CAAC;QAEF,OAAO,cAAc,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,IAAI,IAAI,CAAC;IACtD,CAAC;IAKO,yBAAyB,CAC/B,GAAyB,EACzB,SAA2D;QAG3D,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAElC,IAAI,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAE7C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;oBACtB,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;gBACxB,CAAC;gBAGD,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC;gBAG7C,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACtD,CAAC;YAGD,IAAI,GAAG,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC/C,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC;YACxC,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,SAAS,CAAC,IAAI,YAAY,CAAC,CAAC;IAC1D,CAAC;IAKO,sBAAsB,CAC5B,GAAyB,EACzB,GAAa,EACb,SAA2D;QAG3D,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;QAE9B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;YAC3B,IAAI,CAAC;gBAEH,MAAM,YAAY,GAAG,mBAAmB,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC/D,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAC/C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACvC,CAAC;QACH,CAAC,CAAC;QAKF,SAAS,mBAAmB,CAAC,IAAS,EAAE,SAAoB;YAC1D,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACtC,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,mBAAmB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;YAG7B,MAAM,eAAe,GAAG;gBACtB,gBAAgB;gBAChB,cAAc;gBACd,aAAa;gBACb,QAAQ;gBACR,OAAO;aACR,CAAC;YAEF,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC9B,IAAI,KAAK,IAAI,QAAQ,EAAE,CAAC;oBACtB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC,CAAC,CAAC;YAGH,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBAE7D,OAAO;oBACL,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,OAAO,EAAE,uBAAuB;iBACjC,CAAC;YACJ,CAAC;YAGD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAClC,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;oBAChE,QAAQ,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;IACH,CAAC;IAKO,mBAAmB,CAAC,SAAoB;QAC9C,MAAM,WAAW,GAAgC;YAC/C,QAAQ,EAAE,CAAC,GAAG,CAAC;YACf,MAAM,EAAE,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,sBAAsB,CAAC;YACvG,KAAK,EAAE,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,sBAAsB,CAAC;YACtG,IAAI,EAAE,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,sBAAsB,CAAC;YACrG,MAAM,EAAE,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,mBAAmB,CAAC;SAChF,CAAC;QAEF,OAAO,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IACtC,CAAC;IAKO,kBAAkB,CAAC,SAAoB;QAC7C,MAAM,SAAS,GAAgC;YAC7C,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,CAAC,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC;YACzD,KAAK,EAAE,CAAC,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC;YACxD,IAAI,EAAE,CAAC,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC;YACvD,MAAM,EAAE,CAAC,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC;SAC1D,CAAC;QAEF,OAAO,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IACpC,CAAC;IAKO,iBAAiB,CAAC,SAAoB,EAAE,QAAgB;QAE9D,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,CAAC;QACf,CAAC;QAGD,MAAM,SAAS,GAA6B;YAC1C,qBAAqB,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YACrD,qBAAqB,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAC3C,iBAAiB,EAAE,CAAC,QAAQ,CAAC;YAC7B,oBAAoB,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;SAC3C,CAAC;QAEF,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;CACF,CAAA;AAnYY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;;GACA,uBAAuB,CAmYnC;AAKD,SAAgB,iBAAiB,CAAC,QAAgB,EAAE,MAAc;IAChE,OAAO,UAAS,MAAW,EAAE,YAAoB,EAAE,UAA8B;QAC/E,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC;QAEhC,UAAU,CAAC,KAAK,GAAG,KAAK,WAAU,GAAG,IAAW;YAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAyB,CAAC;YAE5C,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAE/C,MAAM,UAAU,GAAG,IAAI,uBAAuB,EAAE,CAAC;gBACjD,MAAM,UAAU,GAAG,UAAU,CAAC,sBAAsB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBAEtE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,MAAM,IAAI,2BAAkB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;YAED,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC"}