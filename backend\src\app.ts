import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import config from './utils/config';
import { errorHandler } from './middlewares/errorHandler';
import authRoutes from './routes/auth';
import dreamsRoutes from './routes/dreams';
import messagesRoutes from './routes/messages';

// 创建Express应用
const app = express();

// 中间件
app.use(helmet());
app.use(cors({
  origin: config.corsOrigin,
  credentials: true,
}));
app.use(express.json());

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/dreams', dreamsRoutes);
app.use('/api/messages', messagesRoutes);

// 健康检查
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// 错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  errorHandler(err, req, res, next);
});

export default app;
