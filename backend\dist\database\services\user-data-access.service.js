"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserDataAccessService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserDataAccessService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../database.service");
let UserDataAccessService = UserDataAccessService_1 = class UserDataAccessService {
    constructor(databaseService) {
        this.databaseService = databaseService;
        this.logger = new common_1.Logger(UserDataAccessService_1.name);
    }
    async findUserByWallet(walletAddress) {
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'users',
            operation: 'select',
            filters: { primary_wallet_address: walletAddress },
        });
        if (result.error || !result.data || result.data.length === 0) {
            return null;
        }
        return result.data[0];
    }
    async findUserById(userId) {
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'users',
            operation: 'select',
            filters: { id: userId },
        });
        if (result.error || !result.data || result.data.length === 0) {
            return null;
        }
        return result.data[0];
    }
    async createUser(walletAddress, username) {
        try {
            const { userId, error } = await this.databaseService.createUserWithWallet(walletAddress, username);
            if (error) {
                return { user: null, error };
            }
            const user = await this.findUserById(userId);
            return { user };
        }
        catch (error) {
            this.logger.error(`创建用户失败: ${error.message}`);
            return { user: null, error };
        }
    }
    async updateLastLogin(userId) {
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'users',
            operation: 'update',
            data: { last_login_time: new Date().toISOString() },
            filters: { id: userId },
        });
        return { success: !result.error, error: result.error };
    }
    async incrementChatRound(userId) {
        const user = await this.findUserById(userId);
        if (!user) {
            return { success: false, error: { message: 'User not found' } };
        }
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'users',
            operation: 'update',
            data: { chat_round_count: user.chat_round_count + 1 },
            filters: { id: userId },
        });
        return { success: !result.error, error: result.error };
    }
    async getUserWallets(userId) {
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'wallet_addresses',
            operation: 'select',
            filters: { user_id: userId },
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data;
    }
    async addWalletToUser(userId, walletAddress, isPrimary = false) {
        if (isPrimary) {
            await this.databaseService.executeWithPermission('almighty', {
                table: 'wallet_addresses',
                operation: 'update',
                data: { is_primary: false },
                filters: { user_id: userId },
            });
        }
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'wallet_addresses',
            operation: 'insert',
            data: {
                user_id: userId,
                wallet_address: walletAddress,
                is_primary: isPrimary,
            },
        });
        if (result.error) {
            return { walletId: null, error: result.error };
        }
        return { walletId: result.data[0]?.id };
    }
    async createUserSession(userId, walletAddress, challengeMessage) {
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'user_sessions',
            operation: 'insert',
            data: {
                user_id: userId,
                wallet_address: walletAddress,
                challenge_message: challengeMessage,
                challenge_timestamp: new Date().toISOString(),
                is_verified: false,
                expires_at: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
            },
        });
        if (result.error) {
            return { sessionId: null, error: result.error };
        }
        return { sessionId: result.data[0]?.id };
    }
    async verifyUserSession(sessionId) {
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'user_sessions',
            operation: 'update',
            data: { is_verified: true },
            filters: { id: sessionId },
        });
        if (result.error) {
            return { session: null, error: result.error };
        }
        const sessionResult = await this.databaseService.executeWithPermission('almighty', {
            table: 'user_sessions',
            operation: 'select',
            filters: { id: sessionId },
        });
        return { session: sessionResult.data?.[0] };
    }
    async getUserSession(sessionId) {
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'user_sessions',
            operation: 'select',
            filters: { id: sessionId },
        });
        if (result.error || !result.data || result.data.length === 0) {
            return null;
        }
        return result.data[0];
    }
};
exports.UserDataAccessService = UserDataAccessService;
exports.UserDataAccessService = UserDataAccessService = UserDataAccessService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], UserDataAccessService);
//# sourceMappingURL=user-data-access.service.js.map