{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@project-serum/anchor": "^0.26.0", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@solana/wallet-adapter-base": "^0.9.26", "@solana/wallet-adapter-react": "^0.15.38", "@solana/wallet-adapter-react-ui": "^0.9.38", "@solana/wallet-adapter-wallets": "^0.19.36", "@solana/web3.js": "^1.98.2", "@supabase/supabase-js": "^2.49.8", "@types/styled-components": "^5.1.34", "framer-motion": "^12.12.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "styled-components": "^6.1.18", "three": "^0.176.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}