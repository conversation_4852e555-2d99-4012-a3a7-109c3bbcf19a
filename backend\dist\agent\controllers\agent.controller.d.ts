import { AgentRuntimeService } from '../services/agent-runtime.service';
import { AlmightyDonnyService } from '../services/almighty-donny.service';
export declare class SendMessageDto {
    userId: string;
    content: string;
    messageId?: string;
}
export declare class CreateAgentDto {
    agentType: string;
    name: string;
    persona?: any;
    tools?: any[];
    modelProvider?: string;
    apiKey?: string;
}
export declare class DreamRequestDto {
    userId: string;
    dreamContent: string;
    selectedDonny?: string;
    additionalInfo?: string;
}
export declare class AgentController {
    private readonly agentRuntimeService;
    private readonly almightyDonnyService;
    private readonly logger;
    constructor(agentRuntimeService: AgentRuntimeService, almightyDonnyService: AlmightyDonnyService);
    chatWithAlmightyDonny(dto: SendMessageDto): Promise<{
        success: boolean;
        data: import("../services/agent-runtime.service").AgentResponse;
        timestamp: Date;
    }>;
    chatWithAgent(agentId: string, dto: SendMessageDto): Promise<{
        success: boolean;
        data: import("../services/agent-runtime.service").AgentResponse;
        timestamp: Date;
    }>;
    createAgent(dto: CreateAgentDto): Promise<{
        success: boolean;
        data: {
            agentId: string;
            agentType: string;
            name: string;
            status: string;
        };
        timestamp: Date;
    }>;
    getAgentStatus(agentId: string): Promise<{
        success: boolean;
        data: any;
        timestamp: Date;
    }>;
    getActiveAgents(): Promise<{
        success: boolean;
        data: {
            agents: string[];
            count: number;
        };
        timestamp: Date;
    }>;
    stopAgent(agentId: string): Promise<{
        success: boolean;
        data: {
            agentId: string;
            status: string;
        };
        timestamp: Date;
    }>;
    healthCheck(): Promise<{
        success: boolean;
        data: {
            status: string;
            agentCount: number;
            elizaosInitialized: boolean;
            details: any;
        };
        timestamp: Date;
    }>;
    submitDreamRequest(dto: DreamRequestDto): Promise<{
        success: boolean;
        data: import("../services/agent-runtime.service").AgentResponse;
        timestamp: Date;
    }>;
    getUserFlowState(userId: string): Promise<{
        success: boolean;
        data: import("../services/almighty-donny.service").UserFlowState;
        timestamp: Date;
    }>;
    cleanupExpiredFlows(): Promise<{
        success: boolean;
        data: {
            message: string;
        };
        timestamp: Date;
    }>;
}
