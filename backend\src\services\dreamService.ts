import supabase from './supabase';
import { Dream } from '../types';
import { AppError } from '../middlewares/errorHandler';

// 创建新梦境
export const createDream = async (userId: string, content: string): Promise<Dream> => {
  const { data, error } = await supabase
    .from('dreams')
    .insert([
      { 
        user_id: userId,
        content,
        is_shared: false,
      }
    ])
    .select()
    .single();
  
  if (error) {
    throw new AppError(`Error creating dream: ${error.message}`, 500);
  }
  
  if (!data) {
    throw new AppError('Failed to create dream', 500);
  }
  
  return {
    id: data.id,
    userId: data.user_id,
    content: data.content,
    interpretation: data.interpretation,
    donnyType: data.donny_type,
    isShared: data.is_shared,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
  };
};

// 获取用户的所有梦境
export const getUserDreams = async (userId: string): Promise<Dream[]> => {
  const { data, error } = await supabase
    .from('dreams')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  
  if (error) {
    throw new AppError(`Error fetching dreams: ${error.message}`, 500);
  }
  
  return data.map(dream => ({
    id: dream.id,
    userId: dream.user_id,
    content: dream.content,
    interpretation: dream.interpretation,
    donnyType: dream.donny_type,
    isShared: dream.is_shared,
    createdAt: new Date(dream.created_at),
    updatedAt: new Date(dream.updated_at),
  }));
};

// 获取特定梦境
export const getDreamById = async (dreamId: string): Promise<Dream | null> => {
  const { data, error } = await supabase
    .from('dreams')
    .select('*')
    .eq('id', dreamId)
    .single();
  
  if (error) {
    if (error.code === 'PGRST116') {
      return null;
    }
    throw new AppError(`Error fetching dream: ${error.message}`, 500);
  }
  
  return data ? {
    id: data.id,
    userId: data.user_id,
    content: data.content,
    interpretation: data.interpretation,
    donnyType: data.donny_type,
    isShared: data.is_shared,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
  } : null;
};

// 更新梦境解析
export const updateDreamInterpretation = async (
  dreamId: string, 
  interpretation: string, 
  donnyType: string
): Promise<Dream> => {
  const { data, error } = await supabase
    .from('dreams')
    .update({ 
      interpretation, 
      donny_type: donnyType,
    })
    .eq('id', dreamId)
    .select()
    .single();
  
  if (error) {
    throw new AppError(`Error updating dream: ${error.message}`, 500);
  }
  
  if (!data) {
    throw new AppError('Dream not found', 404);
  }
  
  return {
    id: data.id,
    userId: data.user_id,
    content: data.content,
    interpretation: data.interpretation,
    donnyType: data.donny_type,
    isShared: data.is_shared,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
  };
};

// 更新梦境分享状态
export const updateDreamShareStatus = async (dreamId: string, isShared: boolean): Promise<Dream> => {
  const { data, error } = await supabase
    .from('dreams')
    .update({ is_shared: isShared })
    .eq('id', dreamId)
    .select()
    .single();
  
  if (error) {
    throw new AppError(`Error updating dream: ${error.message}`, 500);
  }
  
  if (!data) {
    throw new AppError('Dream not found', 404);
  }
  
  return {
    id: data.id,
    userId: data.user_id,
    content: data.content,
    interpretation: data.interpretation,
    donnyType: data.donny_type,
    isShared: data.is_shared,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
  };
};
