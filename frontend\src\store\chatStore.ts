import { create } from 'zustand';
import type { Message, DonnyType } from '../types/donny';

interface ChatState {
  messages: Record<string, Message[]>; // donnyType -> messages
  isTyping: boolean;

  // Actions
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>) => void;
  setTyping: (isTyping: boolean) => void;
  clearChatHistory: (donnyType: string) => void;
}

export const useChatStore = create<ChatState>((set) => ({
  messages: {
    almighty: [],
  },
  isTyping: false,

  // Actions
  addMessage: (message) => set((state) => {
    const { sender, content, donnyType = 'almighty', isVoiceInput } = message;
    const id = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = Date.now();

    // 确保donnyType对应的消息数组存在
    const donnyMessages = state.messages[donnyType] || [];

    return {
      messages: {
        ...state.messages,
        [donnyType]: [
          ...donnyMessages,
          {
            id,
            sender,
            content,
            timestamp,
            donnyType,
            isVoiceInput,
          },
        ],
      },
    };
  }),

  setTyping: (isTyping) => set({ isTyping }),

  clearChatHistory: (donnyType) => set((state) => ({
    messages: {
      ...state.messages,
      [donnyType]: [],
    },
  })),
}));
