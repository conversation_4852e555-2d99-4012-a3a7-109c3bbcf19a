{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../src/program/namespace/index.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAC7C,OAAO,QAAQ,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AACnD,OAAqB,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AACvD,OAA2B,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAC5E,OAA2B,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAC5E,OAAmB,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AACpD,OAAuB,EAAE,gBAAgB,EAAE,MAAM,cAAc,CAAC;AAChE,OAAwB,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAEnE,OAAO,EAAyB,gBAAgB,EAAE,MAAM,WAAW,CAAC;AACpE,OAAoB,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AACrD,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAGhE,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AACzC,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACvE,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACvE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAC/C,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC/E,OAAO,EAAE,iBAAiB,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAC1E,OAAO,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AACpE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC;AAEhD,MAAM,CAAC,OAAO,OAAO,gBAAgB;IACnC;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,GAAG,EACjC,GAAG,EAAE,GAAG,EACR,KAAK,EAAE,KAAK,EACZ,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,QAAQ,EAClB,iBAAiB,CAAC,EAAE,CAClB,WAAW,EAAE,cAAc,KACxB,qBAAqB,CAAC,GAAG,CAAC,GAAG,SAAS,GAC1C;QACD,YAAY,CAAC,GAAG,CAAC;QACjB,oBAAoB,CAAC,GAAG,CAAC;QACzB,oBAAoB,CAAC,GAAG,CAAC;QACzB,gBAAgB,CAAC,GAAG,CAAC;QACrB,iBAAiB,CAAC,GAAG,CAAC;QACtB,gBAAgB,CAAC,GAAG,CAAC;QACrB,WAAW,CAAC,GAAG,CAAC,GAAG,SAAS;QAC5B,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS;KAC/B;CAsEF"}