-- Donny Web3 数据库模式

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    primary_wallet_address VARCHAR(50) NOT NULL,
    registration_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'banned')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建钱包地址表
CREATE TABLE IF NOT EXISTS wallet_addresses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    wallet_address VARCHAR(50) UNIQUE NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    wallet_address VARCHAR(50) NOT NULL,
    challenge_message TEXT,
    challenge_timestamp TIMESTAMP WITH TIME ZONE,
    is_verified BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_primary_wallet ON users(primary_wallet_address);
CREATE INDEX IF NOT EXISTS idx_wallet_addresses_address ON wallet_addresses(wallet_address);
CREATE INDEX IF NOT EXISTS idx_wallet_addresses_user ON wallet_addresses(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_wallet ON user_sessions(wallet_address);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为users表创建自动更新触发器
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) 策略

-- 启用RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- 用户表RLS策略：用户只能访问自己的数据
CREATE POLICY "用户只能查看自己的信息" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "用户只能更新自己的信息" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);

-- 钱包地址表RLS策略
CREATE POLICY "用户只能查看自己的钱包地址" ON wallet_addresses
    FOR SELECT USING (
        user_id IN (SELECT id FROM users WHERE auth.uid()::text = id::text)
    );

CREATE POLICY "用户只能管理自己的钱包地址" ON wallet_addresses
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE auth.uid()::text = id::text)
    );

-- 会话表RLS策略
CREATE POLICY "用户只能访问自己的会话" ON user_sessions
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE auth.uid()::text = id::text)
    );

-- 服务端访问策略（使用service_role密钥时跳过RLS）
-- 实际应用中，后端服务使用service_role密钥，可以绕过RLS进行管理操作 