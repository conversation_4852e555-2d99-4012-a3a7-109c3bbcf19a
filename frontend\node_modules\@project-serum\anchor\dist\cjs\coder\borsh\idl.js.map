{"version": 3, "file": "idl.js", "sourceRoot": "", "sources": ["../../../../src/coder/borsh/idl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0DAAkC;AAElC,wDAA0C;AAE1C,6CAA0C;AAE1C,MAAa,QAAQ;IACZ,MAAM,CAAC,WAAW,CACvB,KAAiD,EACjD,KAAoB;QAEpB,MAAM,SAAS,GACb,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,mBAAS,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/D,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,MAAM,CAAC,CAAC;gBACX,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;YACD,KAAK,IAAI,CAAC,CAAC;gBACT,OAAO,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;aAC5B;YACD,KAAK,IAAI,CAAC,CAAC;gBACT,OAAO,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;aAC5B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,MAAM,CAAC,CAAC;gBACX,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;YACD,KAAK,MAAM,CAAC,CAAC;gBACX,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;YACD,KAAK,MAAM,CAAC,CAAC;gBACX,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;YACD,KAAK,MAAM,CAAC,CAAC;gBACX,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;YACD,KAAK,OAAO,CAAC,CAAC;gBACZ,OAAO,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;aAC/B;YACD,KAAK,QAAQ,CAAC,CAAC;gBACb,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,WAAW,CAAC,CAAC;gBAChB,OAAO,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;aACnC;YACD,OAAO,CAAC,CAAC;gBACP,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE;oBACvB,OAAO,KAAK,CAAC,GAAG,CACd,QAAQ,CAAC,WAAW,CAClB;wBACE,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG;qBACrB,EACD,KAAK,CACN,EACD,SAAS,CACV,CAAC;iBACH;qBAAM,IAAI,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE;oBACjC,OAAO,KAAK,CAAC,MAAM,CACjB,QAAQ,CAAC,WAAW,CAClB;wBACE,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM;qBACxB,EACD,KAAK,CACN,EACD,SAAS,CACV,CAAC;iBACH;qBAAM,IAAI,SAAS,IAAI,KAAK,CAAC,IAAI,EAAE;oBAClC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;oBACnC,qBAAqB;oBACrB,IAAI,KAAK,KAAK,SAAS,EAAE;wBACvB,MAAM,IAAI,mBAAQ,CAAC,iCAAiC,CAAC,CAAC;qBACvD;oBACD,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;oBACzD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;wBACzB,MAAM,IAAI,mBAAQ,CAAC,mBAAmB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;qBAChE;oBACD,OAAO,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;iBAC9D;qBAAM,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;oBAChC,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAClC,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACnC,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,CACpC;wBACE,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,OAAO;qBACd,EACD,KAAK,CACN,CAAC;oBACF,OAAO,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;iBACtD;qBAAM;oBACL,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,EAAE,CAAC,CAAC;iBAClD;aACF;SACF;IACH,CAAC;IAEM,MAAM,CAAC,aAAa,CACzB,OAAmB,EACnB,QAAsB,EAAE,EACxB,IAAa;QAEb,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YAClC,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACrD,MAAM,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC7C,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YACH,OAAO,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;SACzC;aAAM,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;YACvC,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAuB,EAAE,EAAE;gBACnE,MAAM,IAAI,GAAG,IAAA,mBAAS,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;oBAChC,OAAO,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;iBAC/B;gBACD,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CACrC,CAAC,CAAqB,EAAE,CAAS,EAAE,EAAE;oBACnC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;wBAC7B,OAAO,QAAQ,CAAC,WAAW,CACzB,EAAE,IAAI,EAAE,CAAY,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,EAC1C,KAAK,CACN,CAAC;qBACH;oBACD,mCAAmC;oBACnC,oCAAoC;oBACpC,uCAAuC;oBACvC,oCAAoC;oBACpC,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAa,EAAE,KAAK,CAAC,CAAC;gBACpD,CAAC,CACF,CAAC;gBACF,OAAO,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,mEAAmE;gBACnE,wBAAwB;gBACxB,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;aACjD;YAED,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SACvC;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,sBAAsB,OAAO,EAAE,CAAC,CAAC;SAClD;IACH,CAAC;CACF;AAhKD,4BAgKC"}