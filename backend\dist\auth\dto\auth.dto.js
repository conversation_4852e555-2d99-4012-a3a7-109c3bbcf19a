"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthResponseDto = exports.VerifySignatureDto = exports.GetChallengeDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class GetChallengeDto {
}
exports.GetChallengeDto = GetChallengeDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '钱包地址', example: '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.Matches)(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/, {
        message: '无效的Solana钱包地址格式',
    }),
    __metadata("design:type", String)
], GetChallengeDto.prototype, "walletAddress", void 0);
class VerifySignatureDto {
}
exports.VerifySignatureDto = VerifySignatureDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '钱包地址', example: '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.Matches)(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/, {
        message: '无效的Solana钱包地址格式',
    }),
    __metadata("design:type", String)
], VerifySignatureDto.prototype, "walletAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '签名', example: 'base64_encoded_signature' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], VerifySignatureDto.prototype, "signature", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '挑战消息', example: 'challenge_message' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], VerifySignatureDto.prototype, "message", void 0);
class AuthResponseDto {
}
exports.AuthResponseDto = AuthResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'JWT访问令牌' }),
    __metadata("design:type", String)
], AuthResponseDto.prototype, "accessToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户信息' }),
    __metadata("design:type", Object)
], AuthResponseDto.prototype, "user", void 0);
//# sourceMappingURL=auth.dto.js.map