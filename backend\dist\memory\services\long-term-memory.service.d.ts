import { LongTermMemory, MemoryImportance, MemoryQuery } from '../memory.types';
import { AgentType } from '../../database/types/rag.types';
import { DatabaseService } from '../../database/database.service';
import { EmbeddingService } from '../../knowledge/embedding.service';
export declare class LongTermMemoryService {
    private readonly databaseService;
    private readonly embeddingService;
    private readonly logger;
    private readonly archivalThreshold;
    private readonly emotionalSignificanceThreshold;
    constructor(databaseService: DatabaseService, embeddingService: EmbeddingService);
    store(memoryData: Partial<LongTermMemory>): Promise<LongTermMemory>;
    retrieve(query: MemoryQuery): Promise<LongTermMemory[]>;
    semanticSearch(query: MemoryQuery): Promise<LongTermMemory[]>;
    getUserProfile(agentType: AgentType, userId: string): Promise<{
        preferences: LongTermMemory[];
        personalInfo: LongTermMemory[];
        importantEvents: LongTermMemory[];
        emotionalPatterns: LongTermMemory[];
    }>;
    update(memoryId: string, updates: Partial<LongTermMemory>): Promise<LongTermMemory>;
    delete(memoryId: string): Promise<boolean>;
    findById(memoryId: string): Promise<LongTermMemory | null>;
    archiveOldMemories(): Promise<number>;
    getStats(agentType?: AgentType, userId?: string): Promise<{
        total: number;
        byImportance: Record<MemoryImportance, number>;
        byCategory: Record<string, number>;
        storageUsed: number;
        averageImportance: number;
        oldestMemory?: Date;
        newestMemory?: Date;
    }>;
    private generateEmbedding;
    private calculateEmotionalSignificance;
    private generateNarrativeContext;
    private updateAccessRecords;
    private queryDatabase;
    private mapDbRowToMemory;
}
