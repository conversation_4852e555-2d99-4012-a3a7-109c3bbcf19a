"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseModule = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("./database.service");
const supabase_provider_1 = require("./supabase.provider");
const user_data_access_service_1 = require("./services/user-data-access.service");
const dream_data_access_service_1 = require("./services/dream-data-access.service");
const agent_data_access_service_1 = require("./services/agent-data-access.service");
const conversation_data_access_service_1 = require("./services/conversation-data-access.service");
const database_test_service_1 = require("./test/database-test.service");
const database_test_controller_1 = require("./test/database-test.controller");
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        controllers: [database_test_controller_1.DatabaseTestController],
        providers: [
            supabase_provider_1.SupabaseProvider,
            database_service_1.DatabaseService,
            user_data_access_service_1.UserDataAccessService,
            dream_data_access_service_1.DreamDataAccessService,
            agent_data_access_service_1.AgentDataAccessService,
            conversation_data_access_service_1.ConversationDataAccessService,
            database_test_service_1.DatabaseTestService,
        ],
        exports: [
            database_service_1.DatabaseService,
            user_data_access_service_1.UserDataAccessService,
            dream_data_access_service_1.DreamDataAccessService,
            agent_data_access_service_1.AgentDataAccessService,
            conversation_data_access_service_1.ConversationDataAccessService,
        ],
    })
], DatabaseModule);
//# sourceMappingURL=database.module.js.map