"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FreudDonnyService = void 0;
const common_1 = require("@nestjs/common");
const base_donny_service_1 = require("./base-donny.service");
const agent_runtime_service_1 = require("./agent-runtime.service");
const conversation_data_access_service_1 = require("../../database/services/conversation-data-access.service");
const agent_data_access_service_1 = require("../../database/services/agent-data-access.service");
const rag_service_1 = require("../../knowledge/rag.service");
let FreudDonnyService = class FreudDonnyService extends base_donny_service_1.BaseDonnyService {
    constructor(agentRuntimeService, conversationDataService, agentDataService, ragService) {
        super(agentRuntimeService, conversationDataService, agentDataService, ragService);
        this.agentId = 'freud-donny';
        this.agentType = 'freud';
        this.persona = {
            name: 'Freud Donny',
            role: '心理分析解梦专家',
            expertise: ['精神分析', '潜意识理论', '梦的解析', '性心理学', '防御机制'],
            communicationStyle: '理性分析、深度挖掘、科学严谨',
            specialization: '从精神分析角度深入探索梦境中的潜意识冲突和压抑欲望',
            approach: '运用自由联想、象征分析等经典精神分析技术解读梦境',
        };
    }
    async analyzeWithKnowledge(request, knowledgeResults) {
        this.logger.log('开始精神分析解梦...');
        const dreamElements = this.extractDreamElements(request.dreamContent);
        const manifestContent = this.analyzeManifestContent(dreamElements);
        const latentContent = this.analyzeLatentContent(dreamElements);
        const dreamWork = this.analyzeDreamWork(dreamElements);
        const psychoanalyticalInsights = this.extractPsychoanalyticalInsights(knowledgeResults);
        const analysis = `
从精神分析的角度，您的梦境包含以下层次的心理内容：

**显意内容（表面层）**：
${manifestContent}

**隐意内容（深层无意识）**：
${latentContent}

**梦的工作机制**：
${dreamWork}

**心理学洞察**：
${psychoanalyticalInsights}

梦境是通往无意识的王者之路，反映了您内心深处的欲望、冲突和未解决的心理议题。`;
        return analysis;
    }
    async generatePersonalizedResponse(request, interpretation, knowledgeResults) {
        const dreamElements = this.extractDreamElements(request.dreamContent);
        return {
            interpretation,
            keyInsights: this.generatePsychoanalyticalInsights(dreamElements),
            emotionalSupport: this.generateTherapeuticSupport(dreamElements),
            actionableAdvice: this.generatePsychologicalAdvice(dreamElements),
            confidence: this.calculateConfidence(dreamElements, knowledgeResults),
            sources: knowledgeResults.map(result => result.source || '心理学文献'),
        };
    }
    async handleGeneralConversation(message, context) {
        const content = message.content.toLowerCase();
        if (content.includes('心理') || content.includes('分析') || content.includes('潜意识')) {
            return {
                content: `从精神分析的角度来看，每一个梦境都是有意义的，都是潜意识试图向意识传达重要信息的方式。\n\n请详细描述您的梦境，我将运用弗洛伊德的理论为您深入分析其中的心理动力学机制。🧠`,
                type: 'text',
                metadata: { conversation_type: 'psychology' },
            };
        }
        return {
            content: `您好！我是Freud Donny，专门从精神分析的角度解读梦境。\n\n根据弗洛伊德的理论，梦是愿望的达成，是被压抑的无意识欲望的象征性表达。让我帮助您探索梦境背后的深层心理含义。🔍`,
            type: 'text',
            metadata: { conversation_type: 'greeting' },
        };
    }
    analyzeManifestContent(elements) {
        const manifestElements = [];
        if (elements.characters.length > 0) {
            manifestElements.push(`人物角色：${elements.characters.join('、')}`);
        }
        if (elements.symbols.length > 0) {
            manifestElements.push(`梦境符号：${elements.symbols.join('、')}`);
        }
        if (elements.actions.length > 0) {
            manifestElements.push(`行为动作：${elements.actions.join('、')}`);
        }
        if (elements.emotions.length > 0) {
            manifestElements.push(`情感体验：${elements.emotions.join('、')}`);
        }
        return manifestElements.length > 0
            ? manifestElements.join('\n')
            : '梦境的表面内容包含了日常生活的残余和当前关注的事务。';
    }
    analyzeLatentContent(elements) {
        const interpretations = [];
        if (elements.characters.includes('父母')) {
            interpretations.push('父母形象可能代表超我的权威和道德约束');
        }
        if (elements.characters.includes('陌生人')) {
            interpretations.push('陌生人可能象征自我的未知部分或被压抑的人格面向');
        }
        if (elements.symbols.includes('水')) {
            interpretations.push('水象征着情感的流动、潜意识的深层内容');
        }
        if (elements.symbols.includes('房子')) {
            interpretations.push('房子代表心理结构，不同房间象征人格的不同层面');
        }
        if (elements.actions.includes('飞')) {
            interpretations.push('飞行体验可能表达对自由的渴望或逃避现实的欲望');
        }
        if (elements.actions.includes('追')) {
            interpretations.push('追逐行为可能反映对目标的强烈欲望或内心冲突');
        }
        return interpretations.length > 0
            ? interpretations.join('\n')
            : '梦境的深层含义涉及被压抑的欲望和无意识的心理冲突。';
    }
    analyzeDreamWork(elements) {
        const mechanisms = [];
        if (elements.symbols.length > 3) {
            mechanisms.push('凝缩：多个概念被压缩成单一象征，显示心理内容的集中化');
        }
        if (elements.emotions.length > 0) {
            mechanisms.push('移置：情感从原始对象转移到中性对象，起到心理防御作用');
        }
        mechanisms.push('象征化：抽象的心理内容通过具体形象表达，便于梦的构建');
        mechanisms.push('二次加工：无意识内容被整理成相对连贯的梦境叙述');
        return mechanisms.join('\n');
    }
    extractPsychoanalyticalInsights(knowledgeResults) {
        const concepts = [
            '梦是愿望的达成',
            '潜意识通过象征语言表达',
            '防御机制保护自我免受焦虑',
            '移情现象反映早期关系模式',
            '强迫性重复体现创伤的影响'
        ];
        if (knowledgeResults.length > 0) {
            return `根据精神分析理论：${knowledgeResults[0].content || concepts[0]}`;
        }
        return concepts[Math.floor(Math.random() * concepts.length)];
    }
    generatePsychoanalyticalInsights(elements) {
        const insights = [];
        if (elements.emotions.includes('焦虑') || elements.emotions.includes('害怕')) {
            insights.push('焦虑情绪可能指向未解决的俄狄浦斯情结或分离焦虑');
        }
        if (elements.actions.includes('逃')) {
            insights.push('逃避行为反映了对现实冲突的心理防御机制');
        }
        if (elements.characters.includes('恋人')) {
            insights.push('恋爱关系可能投射了早期客体关系的模式');
        }
        insights.push('梦境显示了本我、自我、超我之间的动态平衡');
        return insights;
    }
    generateTherapeuticSupport(elements) {
        return `从心理健康的角度来看，您的梦境反映了正常的心理防御机制在发挥作用。每个人都有无意识的冲突和欲望，这是人类心理的自然组成部分。

重要的是要认识到，梦境为我们提供了一个安全的空间来处理这些心理内容。通过理解梦境的象征意义，您可以更好地了解自己的内心世界。

记住，心理成长是一个渐进的过程，需要时间和耐心。接纳自己的所有方面，包括那些被压抑的部分，是心理健康的重要步骤。`;
    }
    generatePsychologicalAdvice(elements) {
        const advice = [];
        advice.push('📝 记录梦境日记，观察重复出现的主题和模式');
        advice.push('🛋️ 考虑寻求专业心理咨询，深入探索无意识内容');
        advice.push('🧘 练习自由联想，让思绪自然流淌而不加判断');
        advice.push('📚 阅读心理学著作，了解人类心理的复杂性');
        advice.push('💭 定期反思情感和行为模式，增强自我觉察');
        return advice.slice(0, 3).join('\n');
    }
    calculateConfidence(elements, knowledgeResults) {
        let confidence = 0.75;
        const totalElements = elements.emotions.length + elements.symbols.length +
            elements.actions.length + elements.characters.length;
        confidence += Math.min(totalElements * 0.03, 0.15);
        confidence += Math.min(knowledgeResults.length * 0.02, 0.1);
        return Math.min(confidence, 0.95);
    }
};
exports.FreudDonnyService = FreudDonnyService;
exports.FreudDonnyService = FreudDonnyService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [agent_runtime_service_1.AgentRuntimeService,
        conversation_data_access_service_1.ConversationDataAccessService,
        agent_data_access_service_1.AgentDataAccessService,
        rag_service_1.RAGService])
], FreudDonnyService);
//# sourceMappingURL=freud-donny.service.js.map