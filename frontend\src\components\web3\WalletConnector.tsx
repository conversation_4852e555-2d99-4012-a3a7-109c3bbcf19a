import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useWallet } from '@solana/wallet-adapter-react';
import { useWalletModal } from '@solana/wallet-adapter-react-ui';
import { theme } from '../../styles/theme';
import { useUserStore } from '../../store/userStore';
import { bs58 } from '@project-serum/anchor/dist/cjs/utils/bytes';

const ConnectButton = styled.button`
  background-color: ${theme.colors.primary};
  color: ${theme.colors.background};
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${theme.colors.secondary};
    transform: scale(1.05);
  }
`;

const WalletInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const Address = styled.span`
  font-family: ${theme.fonts.monospace};
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 4px;
`;

const Username = styled.span`
  font-weight: bold;
  color: ${theme.colors.primary};
`;

const DisconnectButton = styled.button`
  background-color: rgba(255, 255, 255, 0.1);
  color: ${theme.colors.text};
  border: 1px solid ${theme.colors.primary};
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;

const ErrorMessage = styled.div`
  color: ${theme.colors.danger};
  font-size: 14px;
  margin-top: 8px;
`;

// 截断地址显示
const truncateAddress = (address: string): string => {
  if (!address) return '';
  return `${address.slice(0, 4)}...${address.slice(-4)}`;
};

interface WalletConnectorProps {
  onConnect?: () => void;
  onDisconnect?: () => void;
}

export const WalletConnector: React.FC<WalletConnectorProps> = ({
  onConnect,
  onDisconnect
}) => {
  const { wallet, publicKey, connected, disconnect, signMessage } = useWallet();
  const { setVisible } = useWalletModal();
  const {
    isAuthenticated,
    walletAddress,
    username,
    verifySignature,
    logout,
    error,
    clearError
  } = useUserStore();
  const [isSigningIn, setIsSigningIn] = useState(false);

  // 当钱包连接状态改变时处理
  useEffect(() => {
    if (connected && publicKey && !isAuthenticated && !isSigningIn) {
      handleSignMessage();
    }
  }, [connected, publicKey, isAuthenticated]);

  // 处理签名消息
  const handleSignMessage = async () => {
    if (!publicKey || !signMessage) return;

    setIsSigningIn(true);
    clearError();

    try {
      // 创建签名消息
      const message = `Sign this message to authenticate with Donny: ${Date.now()}`;
      const encodedMessage = new TextEncoder().encode(message);

      // 请求用户签名
      const signature = await signMessage(encodedMessage);

      // 验证签名
      await verifySignature(
        publicKey.toString(),
        bs58.encode(signature),
        message
      );

      if (onConnect) {
        onConnect();
      }
    } catch (err) {
      console.error('Authentication error:', err);
    } finally {
      setIsSigningIn(false);
    }
  };

  const handleConnect = () => {
    setVisible(true);
  };

  const handleDisconnect = () => {
    disconnect();
    logout();
    if (onDisconnect) {
      onDisconnect();
    }
  };

  if (isAuthenticated && walletAddress) {
    return (
      <WalletInfo>
        {username && <Username>{username}</Username>}
        <Address>{truncateAddress(walletAddress)}</Address>
        <DisconnectButton onClick={handleDisconnect}>断开钱包</DisconnectButton>
      </WalletInfo>
    );
  }

  return (
    <div>
      <ConnectButton onClick={handleConnect} disabled={isSigningIn}>
        {isSigningIn ? '正在验证...' : '连接钱包'}
      </ConnectButton>
      {error && <ErrorMessage>{error}</ErrorMessage>}
    </div>
  );
};
