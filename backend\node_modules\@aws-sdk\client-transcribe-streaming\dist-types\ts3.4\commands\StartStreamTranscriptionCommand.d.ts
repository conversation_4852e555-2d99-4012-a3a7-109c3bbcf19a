import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  StartStreamTranscriptionRequest,
  StartStreamTranscriptionResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  TranscribeStreamingClientResolvedConfig,
} from "../TranscribeStreamingClient";
export { __MetadataBearer };
export { $Command };
export interface StartStreamTranscriptionCommandInput
  extends StartStreamTranscriptionRequest {}
export interface StartStreamTranscriptionCommandOutput
  extends StartStreamTranscriptionResponse,
    __MetadataBearer {}
declare const StartStreamTranscriptionCommand_base: {
  new (
    input: StartStreamTranscriptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartStreamTranscriptionCommandInput,
    StartStreamTranscriptionCommandOutput,
    TranscribeStreamingClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartStreamTranscriptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartStreamTranscriptionCommandInput,
    StartStreamTranscriptionCommandOutput,
    TranscribeStreamingClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartStreamTranscriptionCommand extends StartStreamTranscriptionCommand_base {
  protected static __types: {
    api: {
      input: StartStreamTranscriptionRequest;
      output: StartStreamTranscriptionResponse;
    };
    sdk: {
      input: StartStreamTranscriptionCommandInput;
      output: StartStreamTranscriptionCommandOutput;
    };
  };
}
