"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BusinessAgentController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessAgentController = void 0;
const common_1 = require("@nestjs/common");
const agent_factory_service_1 = require("./services/agent-factory.service");
let BusinessAgentController = BusinessAgentController_1 = class BusinessAgentController {
    constructor(agentFactory) {
        this.agentFactory = agentFactory;
        this.logger = new common_1.Logger(BusinessAgentController_1.name);
    }
    async getSupportedAgentTypes() {
        this.logger.log('获取支持的Agent类型');
        return await this.agentFactory.getSupportedAgentTypes();
    }
    async recommendAgent(request) {
        this.logger.log(`获取Agent推荐 - 用户: ${request.userId}`);
        if (!request.content) {
            throw new common_1.BadRequestException('内容不能为空');
        }
        return await this.agentFactory.recommendAgent(request.content, request.context);
    }
    async interpretDream(request) {
        this.logger.log(`梦境解释请求 - 用户: ${request.userId}, Agent: ${request.agentType}`);
        if (!request.userId || !request.dreamContent) {
            throw new common_1.BadRequestException('用户ID和梦境内容不能为空');
        }
        try {
            let agentType = request.agentType;
            if (!agentType) {
                const recommendation = await this.agentFactory.recommendAgent(request.dreamContent, { userId: request.userId });
                agentType = recommendation.recommended;
                this.logger.log(`自动推荐Agent: ${agentType} (置信度: ${recommendation.confidence})`);
            }
            const agent = await this.agentFactory.createAgent({
                agentType,
                userId: request.userId,
                sessionId: request.sessionId,
            });
            const interpretationRequest = {
                userId: request.userId,
                dreamContent: request.dreamContent,
                emotionalState: request.emotionalState,
                additionalContext: request.additionalContext,
                sessionId: request.sessionId,
            };
            const result = await agent.interpretDream(interpretationRequest);
            this.logger.log(`梦境解释完成 - 用户: ${request.userId}, 置信度: ${result.confidence}`);
            return result;
        }
        catch (error) {
            this.logger.error(`梦境解释失败: ${error.message}`);
            throw new common_1.BadRequestException(`梦境解释失败: ${error.message}`);
        }
    }
    async chatWithAgent(agentType, request) {
        this.logger.log(`Agent对话 - Agent: ${agentType}, 用户: ${request.userId}`);
        if (!request.userId || !request.content) {
            throw new common_1.BadRequestException('用户ID和内容不能为空');
        }
        try {
            const agent = await this.agentFactory.createAgent({
                agentType,
                userId: request.userId,
                sessionId: request.sessionId,
            });
            const response = await agent.handleMessage(request.userId, request.content, request.context);
            this.logger.log(`Agent对话完成 - Agent: ${agentType}, 用户: ${request.userId}`);
            return response;
        }
        catch (error) {
            this.logger.error(`Agent对话失败: ${error.message}`);
            throw new common_1.BadRequestException(`Agent对话失败: ${error.message}`);
        }
    }
    async getAgentInfo(agentType) {
        this.logger.log(`获取Agent信息: ${agentType}`);
        try {
            const agent = await this.agentFactory.createAgent({ agentType });
            return {
                agentType,
                persona: agent.getAgentInfo(),
                capabilities: agent.getCapabilities(),
                status: 'active',
            };
        }
        catch (error) {
            this.logger.error(`获取Agent信息失败: ${error.message}`);
            throw new common_1.NotFoundException(`Agent类型不存在: ${agentType}`);
        }
    }
    async getActiveAgentStats() {
        this.logger.log('获取活跃Agent统计');
        return this.agentFactory.getActiveAgentStats();
    }
    async cleanupInactiveAgents(maxIdleMinutes = '30') {
        this.logger.log(`清理非活跃Agent - 最大空闲时间: ${maxIdleMinutes}分钟`);
        const cleanedCount = await this.agentFactory.cleanupInactiveAgents(parseInt(maxIdleMinutes, 10));
        return {
            message: `成功清理${cleanedCount}个非活跃Agent`,
            cleanedCount,
        };
    }
    async reloadAgentConfiguration(agentType) {
        this.logger.log(`重载Agent配置: ${agentType}`);
        const success = await this.agentFactory.reloadAgentConfiguration(agentType);
        if (success) {
            return { message: `Agent配置重载成功: ${agentType}` };
        }
        else {
            throw new common_1.BadRequestException(`Agent配置重载失败: ${agentType}`);
        }
    }
    async batchInterpretDreams(request) {
        this.logger.log(`批量梦境解释 - 数量: ${request.dreams.length}`);
        if (!request.dreams || request.dreams.length === 0) {
            throw new common_1.BadRequestException('梦境列表不能为空');
        }
        const results = [];
        const errors = [];
        for (const [index, dream] of request.dreams.entries()) {
            try {
                const result = await this.interpretDream(dream);
                results.push({
                    index,
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                errors.push({
                    index,
                    error: error.message,
                    dreamContent: dream.dreamContent.substring(0, 50) + '...',
                });
            }
        }
        return {
            total: request.dreams.length,
            successful: results.length,
            failed: errors.length,
            results,
            errors,
        };
    }
};
exports.BusinessAgentController = BusinessAgentController;
__decorate([
    (0, common_1.Get)('types'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BusinessAgentController.prototype, "getSupportedAgentTypes", null);
__decorate([
    (0, common_1.Post)('recommend'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BusinessAgentController.prototype, "recommendAgent", null);
__decorate([
    (0, common_1.Post)('interpret-dream'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BusinessAgentController.prototype, "interpretDream", null);
__decorate([
    (0, common_1.Post)('chat/:agentType'),
    __param(0, (0, common_1.Param)('agentType')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BusinessAgentController.prototype, "chatWithAgent", null);
__decorate([
    (0, common_1.Get)('info/:agentType'),
    __param(0, (0, common_1.Param)('agentType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BusinessAgentController.prototype, "getAgentInfo", null);
__decorate([
    (0, common_1.Get)('stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BusinessAgentController.prototype, "getActiveAgentStats", null);
__decorate([
    (0, common_1.Post)('cleanup'),
    __param(0, (0, common_1.Query)('maxIdleMinutes')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BusinessAgentController.prototype, "cleanupInactiveAgents", null);
__decorate([
    (0, common_1.Post)('reload/:agentType'),
    __param(0, (0, common_1.Param)('agentType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BusinessAgentController.prototype, "reloadAgentConfiguration", null);
__decorate([
    (0, common_1.Post)('batch-interpret'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BusinessAgentController.prototype, "batchInterpretDreams", null);
exports.BusinessAgentController = BusinessAgentController = BusinessAgentController_1 = __decorate([
    (0, common_1.Controller)('business-agents'),
    __metadata("design:paramtypes", [agent_factory_service_1.AgentFactoryService])
], BusinessAgentController);
//# sourceMappingURL=business-agent.controller.js.map