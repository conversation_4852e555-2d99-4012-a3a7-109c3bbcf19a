"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessAgentModule = void 0;
const common_1 = require("@nestjs/common");
const taoist_donny_service_1 = require("./services/taoist-donny.service");
const freud_donny_service_1 = require("./services/freud-donny.service");
const papa_donny_service_1 = require("./services/papa-donny.service");
const agent_factory_service_1 = require("./services/agent-factory.service");
const business_agent_controller_1 = require("./business-agent.controller");
const agent_module_1 = require("./agent.module");
const database_module_1 = require("../database/database.module");
const knowledge_module_1 = require("../knowledge/knowledge.module");
let BusinessAgentModule = class BusinessAgentModule {
};
exports.BusinessAgentModule = BusinessAgentModule;
exports.BusinessAgentModule = BusinessAgentModule = __decorate([
    (0, common_1.Module)({
        imports: [
            agent_module_1.AgentModule,
            database_module_1.DatabaseModule,
            knowledge_module_1.KnowledgeModule,
        ],
        controllers: [
            business_agent_controller_1.BusinessAgentController,
        ],
        providers: [
            taoist_donny_service_1.TaoistDonnyService,
            freud_donny_service_1.FreudDonnyService,
            papa_donny_service_1.PapaDonnyService,
            agent_factory_service_1.AgentFactoryService,
        ],
        exports: [
            taoist_donny_service_1.TaoistDonnyService,
            freud_donny_service_1.FreudDonnyService,
            papa_donny_service_1.PapaDonnyService,
            agent_factory_service_1.AgentFactoryService,
        ],
    })
], BusinessAgentModule);
//# sourceMappingURL=business-agent.module.js.map