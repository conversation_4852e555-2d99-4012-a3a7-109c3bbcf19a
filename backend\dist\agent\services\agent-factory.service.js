"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AgentFactoryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentFactoryService = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const taoist_donny_service_1 = require("./taoist-donny.service");
const freud_donny_service_1 = require("./freud-donny.service");
const papa_donny_service_1 = require("./papa-donny.service");
let AgentFactoryService = AgentFactoryService_1 = class AgentFactoryService {
    constructor(moduleRef) {
        this.moduleRef = moduleRef;
        this.logger = new common_1.Logger(AgentFactoryService_1.name);
        this.activeAgents = new Map();
        this.agentRegistry = new Map();
        this.initializeAgentRegistry();
    }
    initializeAgentRegistry() {
        this.agentRegistry.set('taoist', taoist_donny_service_1.TaoistDonnyService);
        this.agentRegistry.set('freud', freud_donny_service_1.FreudDonnyService);
        this.agentRegistry.set('papa', papa_donny_service_1.PapaDonnyService);
        this.logger.log(`Agent注册表初始化完成，支持${this.agentRegistry.size}种Agent类型`);
    }
    async createAgent(request) {
        this.logger.log(`创建Agent: ${request.agentType}`);
        try {
            if (!this.agentRegistry.has(request.agentType)) {
                throw new Error(`不支持的Agent类型: ${request.agentType}`);
            }
            const agentInstanceId = this.generateAgentInstanceId(request);
            if (this.activeAgents.has(agentInstanceId)) {
                this.logger.log(`返回已存在的Agent实例: ${agentInstanceId}`);
                return this.activeAgents.get(agentInstanceId);
            }
            const AgentServiceClass = this.agentRegistry.get(request.agentType);
            const agentInstance = await this.moduleRef.create(AgentServiceClass);
            if (request.configuration) {
                await this.applyConfiguration(agentInstance, request.configuration);
            }
            this.activeAgents.set(agentInstanceId, agentInstance);
            this.logger.log(`Agent实例创建成功: ${agentInstanceId}`);
            return agentInstance;
        }
        catch (error) {
            this.logger.error(`创建Agent失败: ${error.message}`);
            throw error;
        }
    }
    getAgent(agentType, userId, sessionId) {
        const agentInstanceId = this.generateAgentInstanceId({
            agentType,
            userId,
            sessionId
        });
        return this.activeAgents.get(agentInstanceId) || null;
    }
    async getSupportedAgentTypes() {
        const agentInfos = [];
        for (const [agentType] of this.agentRegistry) {
            try {
                const tempInstance = await this.createAgent({ agentType });
                const persona = tempInstance.getAgentInfo();
                const capabilities = tempInstance.getCapabilities();
                agentInfos.push({
                    id: agentType,
                    type: agentType,
                    name: persona.name,
                    persona,
                    capabilities,
                    status: 'active',
                    createdAt: new Date(),
                });
            }
            catch (error) {
                this.logger.warn(`获取Agent类型信息失败: ${agentType} - ${error.message}`);
            }
        }
        return agentInfos;
    }
    getActiveAgentStats() {
        const byType = {};
        let oldestActivity = null;
        let newestActivity = null;
        for (const [instanceId, agent] of this.activeAgents) {
            const agentType = instanceId.split(':')[0];
            byType[agentType] = (byType[agentType] || 0) + 1;
            const now = new Date();
            if (!oldestActivity || now < oldestActivity) {
                oldestActivity = now;
            }
            if (!newestActivity || now > newestActivity) {
                newestActivity = now;
            }
        }
        return {
            totalActive: this.activeAgents.size,
            byType,
            oldestActivity,
            newestActivity,
        };
    }
    async recommendAgent(content, context) {
        this.logger.log('开始Agent推荐分析...');
        const recommendations = [];
        const contentLower = content.toLowerCase();
        let taoistScore = 0;
        if (contentLower.includes('道') || contentLower.includes('阴阳') || contentLower.includes('自然')) {
            taoistScore += 0.8;
        }
        if (contentLower.includes('平衡') || contentLower.includes('和谐')) {
            taoistScore += 0.6;
        }
        if (contentLower.includes('哲学') || contentLower.includes('智慧')) {
            taoistScore += 0.4;
        }
        recommendations.push({
            type: 'taoist',
            confidence: Math.min(taoistScore, 0.95),
            reasoning: '内容涉及道教哲学、阴阳平衡等东方智慧概念'
        });
        let freudScore = 0;
        if (contentLower.includes('心理') || contentLower.includes('分析') || contentLower.includes('潜意识')) {
            freudScore += 0.8;
        }
        if (contentLower.includes('焦虑') || contentLower.includes('恐惧') || contentLower.includes('压抑')) {
            freudScore += 0.6;
        }
        if (contentLower.includes('童年') || contentLower.includes('回忆')) {
            freudScore += 0.4;
        }
        recommendations.push({
            type: 'freud',
            confidence: Math.min(freudScore, 0.95),
            reasoning: '内容涉及心理分析、情感冲突等精神分析主题'
        });
        let papaScore = 0;
        if (contentLower.includes('家庭') || contentLower.includes('亲情') || contentLower.includes('爱')) {
            papaScore += 0.8;
        }
        if (contentLower.includes('温暖') || contentLower.includes('关怀') || contentLower.includes('支持')) {
            papaScore += 0.6;
        }
        if (contentLower.includes('困难') || contentLower.includes('迷茫') || contentLower.includes('需要帮助')) {
            papaScore += 0.4;
        }
        recommendations.push({
            type: 'papa',
            confidence: Math.min(papaScore, 0.95),
            reasoning: '内容涉及情感支持、家庭关爱等温暖主题'
        });
        recommendations.sort((a, b) => b.confidence - a.confidence);
        if (recommendations[0].confidence < 0.3) {
            return {
                recommended: 'almighty',
                confidence: 0.8,
                reasoning: '内容主题不明确，推荐全能型Almighty Donny进行综合分析',
                alternatives: recommendations,
            };
        }
        return {
            recommended: recommendations[0].type,
            confidence: recommendations[0].confidence,
            reasoning: recommendations[0].reasoning,
            alternatives: recommendations.slice(1),
        };
    }
    async cleanupInactiveAgents(maxIdleMinutes = 30) {
        this.logger.log(`开始清理超过${maxIdleMinutes}分钟未活动的Agent...`);
        let cleanedCount = 0;
        const cutoffTime = new Date(Date.now() - maxIdleMinutes * 60 * 1000);
        this.logger.log(`清理了${cleanedCount}个非活跃Agent`);
        return cleanedCount;
    }
    async reloadAgentConfiguration(agentType) {
        this.logger.log(`重新加载Agent配置: ${agentType}`);
        try {
            const instancesToReload = [];
            for (const [instanceId, agent] of this.activeAgents) {
                if (instanceId.startsWith(`${agentType}:`)) {
                    instancesToReload.push(instanceId);
                }
            }
            for (const instanceId of instancesToReload) {
                this.activeAgents.delete(instanceId);
            }
            this.logger.log(`成功重载${instancesToReload.length}个${agentType}实例`);
            return true;
        }
        catch (error) {
            this.logger.error(`重载Agent配置失败: ${error.message}`);
            return false;
        }
    }
    generateAgentInstanceId(request) {
        const parts = [request.agentType];
        if (request.userId) {
            parts.push(`user-${request.userId}`);
        }
        if (request.sessionId) {
            parts.push(`session-${request.sessionId}`);
        }
        return parts.join(':');
    }
    async applyConfiguration(agentInstance, configuration) {
        this.logger.log('应用Agent配置...');
        this.logger.log('Agent配置应用完成');
    }
};
exports.AgentFactoryService = AgentFactoryService;
exports.AgentFactoryService = AgentFactoryService = AgentFactoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.ModuleRef])
], AgentFactoryService);
//# sourceMappingURL=agent-factory.service.js.map