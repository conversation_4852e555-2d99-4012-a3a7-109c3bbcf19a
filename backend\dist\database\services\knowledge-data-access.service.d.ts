import { DatabaseService } from '../database.service';
import { KnowledgeBase, KnowledgeEntry, SearchResult, AgentType, CreateKnowledgeBaseRequest, CreateKnowledgeEntryRequest, UpdateKnowledgeEntryRequest } from '../types/rag.types';
export declare class KnowledgeDataAccessService {
    private databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService);
    createKnowledgeBase(request: CreateKnowledgeBaseRequest, userId?: string): Promise<KnowledgeBase>;
    getKnowledgeBases(agentType?: AgentType, userId?: string, includePublic?: boolean): Promise<KnowledgeBase[]>;
    getKnowledgeBaseById(id: string): Promise<KnowledgeBase>;
    deleteKnowledgeBase(id: string, userId?: string): Promise<void>;
    createKnowledgeEntry(request: CreateKnowledgeEntryRequest, embedding?: number[]): Promise<KnowledgeEntry>;
    updateKnowledgeEntry(id: string, request: UpdateKnowledgeEntryRequest, embedding?: number[]): Promise<KnowledgeEntry>;
    getKnowledgeEntries(knowledgeBaseId: string, limit?: number, offset?: number): Promise<KnowledgeEntry[]>;
    deleteKnowledgeEntry(id: string): Promise<void>;
    searchKnowledgeEntries(queryEmbedding: number[], agentType?: AgentType, matchThreshold?: number, matchCount?: number): Promise<SearchResult[]>;
    logKnowledgeUsage(agentType: AgentType, knowledgeEntryId: string, queryText: string, similarityScore: number, usageContext: 'conversation' | 'dream_analysis' | 'guidance', userId?: string): Promise<void>;
    getUsageStats(agentType?: AgentType, userId?: string, days?: number): Promise<any>;
    private aggregateUsageStats;
}
