import { create } from 'zustand';
import { authApi, getToken } from '../services/api';

interface UserState {
  userId: string | null;
  walletAddress: string | null;
  username: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setUser: (userId: string, walletAddress: string, username?: string) => void;
  verifySignature: (walletAddress: string, signature: string, message: string) => Promise<void>;
  updateUsername: (username: string) => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

export const useUserStore = create<UserState>((set, get) => ({
  userId: null,
  walletAddress: null,
  username: null,
  isAuthenticated: !!getToken(),
  isLoading: false,
  error: null,
  
  // Actions
  setUser: (userId, walletAddress, username) => set({
    userId,
    walletAddress,
    username: username || null,
    isAuthenticated: true,
  }),
  
  verifySignature: async (walletAddress, signature, message) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await authApi.verifySignature(walletAddress, signature, message);
      
      if (response.status === 'success') {
        const { userId, walletAddress: address, username } = response.data.user;
        set({
          userId,
          walletAddress: address,
          username,
          isAuthenticated: true,
          isLoading: false,
        });
      } else {
        throw new Error('Authentication failed');
      }
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      });
    }
  },
  
  updateUsername: async (username) => {
    const { userId } = get();
    if (!userId) return;
    
    set({ isLoading: true, error: null });
    
    try {
      const token = getToken();
      if (!token) throw new Error('No authentication token');
      
      await authApi.updateUsername(userId, username, token);
      
      set({
        username,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update username',
      });
    }
  },
  
  logout: () => {
    authApi.logout();
    set({
      userId: null,
      walletAddress: null,
      username: null,
      isAuthenticated: false,
    });
  },
  
  clearError: () => set({ error: null }),
}));
