{"version": 3, "file": "workspace.js", "sourceRoot": "", "sources": ["../../src/workspace.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,WAAW,CAAC;AAClC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAE7C,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAEhC;;;;;;GAMG;AACH,MAAM,SAAS,GAAG,IAAI,KAAK,CAAC,EAAS,EAAE;IACrC,GAAG,CAAC,cAA0C,EAAE,WAAmB;QACjE,IAAI,SAAS,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAEnC,IAAI,CAAC,mBAAmB,EAAE;YACxB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YAE7B,IAAI,WAAW,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YAChC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,EAAE;gBAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBAC5C,IAAI,SAAS,KAAK,WAAW,EAAE;oBAC7B,WAAW,GAAG,SAAS,CAAC;iBACzB;gBACD,WAAW,GAAG,SAAS,CAAC;aACzB;YAED,IAAI,WAAW,KAAK,SAAS,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;aACnD;YAED,MAAM,SAAS,GAAG,GAAG,WAAW,aAAa,CAAC;YAC9C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;gBAC7B,MAAM,IAAI,KAAK,CACb,GAAG,SAAS,6CAA6C,CAC1D,CAAC;aACH;YAED,MAAM,MAAM,GAAG,IAAI,GAAG,EAAe,CAAC;YACtC,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC;iBACtB,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;iBACxC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAChB,MAAM,QAAQ,GAAG,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACzC,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAC/B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC1B,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;gBACvD,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE;oBACxC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,OAAO,CAChC,GAAG,EACH,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CACpC,CAAC;iBACH;YACH,CAAC,CAAC,CAAC;YAEL,sEAAsE;YACtE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAC3B,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,EAAE,OAAO,CAAC,CAChE,CAAC;YACF,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC9C,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;gBACzD,uBAAuB,CACrB,cAAc,EACd,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAC9B,MAAM,CACP,CAAC;aACH;YAED,mBAAmB,GAAG,IAAI,CAAC;SAC5B;QAED,OAAO,cAAc,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,uBAAuB,CAC9B,cAA0C,EAC1C,cAA6E,EAC7E,MAAwB;IAExB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;QAClD,MAAM,aAAa,GAAG,SAAS,CAAC,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;QACnE,MAAM,KAAK,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC;QAC1C,MAAM,eAAe,GAAG,IAAI,SAAS,CACnC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAClD,CAAC;QACF,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAClC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,GAAG,EAAE;YAC1C,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;SAClE;QACD,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,IAAI,KAAK,CAAC,mCAAmC,WAAW,EAAE,CAAC,CAAC;SACnE;QACD,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;IACpE,CAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAe,SAAS,CAAC"}