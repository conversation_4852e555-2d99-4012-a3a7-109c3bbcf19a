import { SupabaseClient } from '@supabase/supabase-js';
import { SupabaseProvider } from './supabase.provider';
export interface AgentPermission {
    resource: string;
    action: string;
    conditions: any;
}
export interface DatabaseQuery {
    table: string;
    operation: 'select' | 'insert' | 'update' | 'delete';
    data?: any;
    filters?: any;
    columns?: string;
}
export declare class DatabaseService {
    private supabaseProvider;
    private readonly logger;
    private supabase;
    constructor(supabaseProvider: SupabaseProvider);
    checkAgentPermission(agentType: string, resource: string, action: string): Promise<boolean>;
    getAgentPermissions(agentType: string): Promise<AgentPermission[]>;
    executeWithPermission(agentType: string, query: DatabaseQuery, userId?: string): Promise<{
        data: any;
        error: any;
    }>;
    getClient(): SupabaseClient;
    createUserWithWallet(walletAddress: string, username?: string): Promise<{
        userId: string;
        error?: any;
    }>;
    healthCheck(): Promise<boolean>;
}
