import { ShortTermMemory, MemoryImportance, MemoryQuery } from '../memory.types';
import { AgentType } from '../../database/types/rag.types';
import { DatabaseService } from '../../database/database.service';
export declare class ShortTermMemoryService {
    private readonly databaseService;
    private readonly logger;
    private readonly cache;
    private readonly maxCacheSize;
    private readonly cacheTTL;
    private readonly maxContextSize;
    private readonly defaultExpirationHours;
    constructor(databaseService: DatabaseService);
    store(memoryData: Partial<ShortTermMemory>): Promise<ShortTermMemory>;
    retrieve(query: MemoryQuery): Promise<ShortTermMemory[]>;
    getSessionContext(agentType: AgentType, userId: string, sessionId: string): Promise<ShortTermMemory[]>;
    update(memoryId: string, updates: Partial<ShortTermMemory>): Promise<ShortTermMemory>;
    delete(memoryId: string): Promise<boolean>;
    findById(memoryId: string): Promise<ShortTermMemory | null>;
    cleanupExpiredMemories(): Promise<number>;
    getConsolidationCandidates(limit?: number): Promise<ShortTermMemory[]>;
    getStats(agentType?: AgentType, userId?: string): Promise<{
        total: number;
        byImportance: Record<MemoryImportance, number>;
        byCategory: Record<string, number>;
        storageUsed: number;
        averageImportance: number;
        oldestMemory?: Date;
        newestMemory?: Date;
    }>;
    private addToCache;
    private searchCache;
    private isCacheExpired;
    private cleanupCache;
    private matchesQuery;
    private activateMemories;
    private limitResults;
    private queryDatabase;
    private mapDbRowToMemory;
}
