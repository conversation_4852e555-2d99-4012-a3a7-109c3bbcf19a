import { OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SupabaseClient } from '@supabase/supabase-js';
export declare class SupabaseService implements OnModuleInit {
    private configService;
    private client;
    constructor(configService: ConfigService);
    onModuleInit(): void;
    getClient(): SupabaseClient<any> | null;
    isAvailable(): boolean;
    createUser(userData: {
        username: string;
        primaryWalletAddress: string;
    }): Promise<any>;
    findUserById(id: string): Promise<any>;
    findUserByWalletAddress(walletAddress: string): Promise<any>;
    updateUser(id: string, updateData: any): Promise<any>;
    deleteUser(id: string): Promise<void>;
    addWalletAddress(userId: string, walletAddress: string, isPrimary?: boolean): Promise<any>;
    findAllUsers(): Promise<any[]>;
    createSession(sessionData: {
        userId: string;
        walletAddress: string;
        challengeMessage: string;
        expiresAt: Date;
    }): Promise<any>;
    findSessionByWalletAddress(walletAddress: string): Promise<any>;
    verifySession(sessionId: string): Promise<any>;
}
