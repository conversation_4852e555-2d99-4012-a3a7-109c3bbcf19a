{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/coder/borsh/index.ts"], "names": [], "mappings": ";;;AACA,qDAAyD;AACzD,+CAAmD;AACnD,yCAA6C;AAC7C,yCAA6C;AAC7C,yCAA6C;AAG7C,mDAAyD;AAAhD,uHAAA,qBAAqB,OAAA;AAC9B,6CAA+E;AAAtE,iHAAA,kBAAkB,OAAA;AAAE,yHAAA,0BAA0B,OAAA;AACvD,uCAAiE;AAAxD,2GAAA,eAAe,OAAA;AAAE,8GAAA,kBAAkB,OAAA;AAC5C,uCAAiE;AAAxD,2GAAA,eAAe,OAAA;AAAE,8GAAA,kBAAkB,OAAA;AAE5C;;;GAGG;AACH,MAAa,UAAU;IA4BrB,YAAY,GAAQ;QAClB,IAAI,CAAC,WAAW,GAAG,IAAI,sCAAqB,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,gCAAkB,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,GAAG,IAAI,0BAAe,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,GAAG,CAAC,KAAK,EAAE;YACb,IAAI,CAAC,KAAK,GAAG,IAAI,0BAAe,CAAC,GAAG,CAAC,CAAC;SACvC;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,0BAAe,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;CACF;AArCD,gCAqCC"}