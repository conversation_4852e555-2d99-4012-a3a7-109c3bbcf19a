import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AgentType } from '../database/types/rag.types';
export interface AuthenticatedRequest extends Request {
    agent?: {
        type: AgentType;
        id: string;
        userId?: string;
    };
    user?: {
        id: string;
        walletAddress?: string;
    };
}
export declare class DataIsolationMiddleware implements NestMiddleware {
    private readonly logger;
    constructor();
    use(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void>;
    private isAgentRequest;
    private extractAgentInfo;
    private extractUserInfo;
    private extractUserIdFromRequest;
    private checkRoutePermission;
    private extractResourceFromPath;
    private extractActionFromMethod;
    private applyDataIsolationFilters;
    private setupResponseFiltering;
    private getAllowedResources;
    private getDeniedResources;
    private getAllowedActions;
}
export declare function RequirePermission(resource: string, action: string): (target: any, propertyName: string, descriptor: PropertyDescriptor) => void;
