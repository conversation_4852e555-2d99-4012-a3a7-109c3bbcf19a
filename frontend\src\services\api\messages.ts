import { API_BASE_URL, getHeaders, handleResponse, getToken } from './config';
import type { DonnyType } from '../../types/donny';

// 获取用户与特定Donny的对话历史
export const getUserDonnyMessages = async (donnyType: DonnyType, limit: number = 50, before?: Date) => {
  const token = getToken();
  if (!token) throw new Error('No authentication token');

  let url = `${API_BASE_URL}/messages/donny/${donnyType}?limit=${limit}`;

  if (before) {
    url += `&before=${before.toISOString()}`;
  }

  const response = await fetch(url, {
    method: 'GET',
    headers: getHeaders(token),
  });

  return handleResponse(response);
};

// 获取特定梦境的对话历史
export const getDreamMessages = async (dreamId: string) => {
  const token = getToken();
  if (!token) throw new Error('No authentication token');

  const response = await fetch(`${API_BASE_URL}/messages/dream/${dreamId}`, {
    method: 'GET',
    headers: getHeaders(token),
  });

  return handleResponse(response);
};

// 创建新消息
export const createMessage = async (
  content: string,
  sender: 'user' | 'donny',
  donnyType: DonnyType,
  dreamId?: string
) => {
  const token = getToken();
  if (!token) throw new Error('No authentication token');

  const response = await fetch(`${API_BASE_URL}/messages`, {
    method: 'POST',
    headers: getHeaders(token),
    body: JSON.stringify({
      content,
      sender,
      donnyType,
      dreamId,
    }),
  });

  return handleResponse(response);
};
