{"version": 3, "file": "accounts.js", "sourceRoot": "", "sources": ["../../../../src/coder/borsh/accounts.ts"], "names": [], "mappings": "AAAA,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEhC,OAAO,SAAS,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAEnC,OAAO,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAC;AAEpC,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,MAAM,0BAA0B,GAAG,CAAC,CAAC;AAE5C;;GAEG;AACH,MAAM,OAAO,kBAAkB;IAa7B,YAAmB,GAAQ;QACzB,IAAI,GAAG,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;YAChC,OAAO;SACR;QACD,MAAM,OAAO,GAAkB,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACtD,OAAO,CAAC,GAAG,CAAC,IAAS,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,MAAM,CAAU,WAAc,EAAE,OAAU;QACrD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,8BAA8B;QACjE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,EAAE,CAAC,CAAC;SACpD;QACD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC3C,IAAI,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACvC,IAAI,aAAa,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACzE,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;IACrD,CAAC;IAEM,MAAM,CAAU,WAAc,EAAE,IAAY;QACjD,+CAA+C;QAC/C,MAAM,aAAa,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC3E,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAEM,SAAS,CAAU,IAAY;QACpC,MAAM,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CACtE,kBAAkB,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAC1E,CAAC;QACF,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACpD;QAED,OAAO,IAAI,CAAC,eAAe,CAAI,WAAkB,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAEM,eAAe,CAAU,WAAc,EAAE,EAAU;QACxD,8CAA8C;QAC9C,MAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,EAAE,CAAC,CAAC;SACpD;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEM,MAAM,CAAC,WAAc,EAAE,UAAmB;QAC/C,MAAM,aAAa,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC3E,OAAO;YACL,MAAM,EAAE,CAAC;YACT,KAAK,EAAE,IAAI,CAAC,MAAM,CAChB,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CACxE;SACF,CAAC;IACJ,CAAC;IAEM,IAAI,CAAC,UAAsB;;QAChC,OAAO,CACL,0BAA0B,GAAG,CAAC,MAAA,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,mCAAI,CAAC,CAAC,CACtE,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,oBAAoB,CAAC,IAAY;QAC7C,OAAO,MAAM,CAAC,IAAI,CAChB,MAAM,CAAC,MAAM,CACX,WAAW,SAAS,CAAC,IAAI,EAAE;YACzB,UAAU,EAAE,IAAI;YAChB,4BAA4B,EAAE,IAAI;SACnC,CAAC,EAAE,CACL,CACF,CAAC,KAAK,CAAC,CAAC,EAAE,0BAA0B,CAAC,CAAC;IACzC,CAAC;CACF"}