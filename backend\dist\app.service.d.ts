import { DatabaseService } from './database/database.service';
export declare class AppService {
    private databaseService;
    constructor(databaseService: DatabaseService);
    getHello(): string;
    getHealth(): Promise<{
        status: string;
        timestamp: string;
        service: string;
        version: string;
        database: {
            status: string;
            provider: string;
        };
        components: {
            api: string;
            database: string;
        };
    }>;
}
