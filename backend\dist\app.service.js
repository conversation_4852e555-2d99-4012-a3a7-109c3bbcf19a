"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("./database/database.service");
let AppService = class AppService {
    constructor(databaseService) {
        this.databaseService = databaseService;
    }
    getHello() {
        return 'Donny Web3 解梦平台 API 服务正在运行！';
    }
    async getHealth() {
        const databaseHealth = await this.databaseService.healthCheck();
        return {
            status: databaseHealth ? 'ok' : 'degraded',
            timestamp: new Date().toISOString(),
            service: 'donny-web3-api',
            version: '1.0.0',
            database: {
                status: databaseHealth ? 'connected' : 'disconnected',
                provider: 'supabase',
            },
            components: {
                api: 'ok',
                database: databaseHealth ? 'ok' : 'error',
            },
        };
    }
};
exports.AppService = AppService;
exports.AppService = AppService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], AppService);
//# sourceMappingURL=app.service.js.map