"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaoistDonnyService = void 0;
const common_1 = require("@nestjs/common");
const base_donny_service_1 = require("./base-donny.service");
const agent_runtime_service_1 = require("./agent-runtime.service");
const conversation_data_access_service_1 = require("../../database/services/conversation-data-access.service");
const agent_data_access_service_1 = require("../../database/services/agent-data-access.service");
const rag_service_1 = require("../../knowledge/rag.service");
let TaoistDonnyService = class TaoistDonnyService extends base_donny_service_1.BaseDonnyService {
    constructor(agentRuntimeService, conversationDataService, agentDataService, ragService) {
        super(agentRuntimeService, conversationDataService, agentDataService, ragService);
        this.agentId = 'taoist-donny';
        this.agentType = 'taoist';
        this.persona = {
            name: 'Taoist Donny',
            role: '道教解梦大师',
            expertise: ['道教哲学', '阴阳五行', '易经八卦', '太极思想', '自然和谐'],
            communicationStyle: '温和智慧、深邃淡然、注重平衡',
            specialization: '从道教和东方哲学角度解读梦境中的自然规律和人生哲理',
            approach: '强调顺其自然、阴阳平衡、天人合一的东方智慧',
        };
    }
    async analyzeWithKnowledge(request, knowledgeResults) {
        this.logger.log('开始道教解梦分析...');
        const dreamElements = this.extractDreamElements(request.dreamContent);
        const wuxingAnalysis = this.analyzeWuxing(dreamElements);
        const yinyangAnalysis = this.analyzeYinyang(dreamElements);
        const taoistWisdom = this.extractTaoistWisdom(knowledgeResults);
        const analysis = `
从道家的角度来看，您的梦境反映了以下深层含义：

**五行解读**：
${wuxingAnalysis}

**阴阳平衡**：
${yinyangAnalysis}

**道家智慧**：
${taoistWisdom}

梦境是您内在世界与宇宙大道的对话，体现了自然规律在您生命中的映射。`;
        return analysis;
    }
    async generatePersonalizedResponse(request, interpretation, knowledgeResults) {
        const dreamElements = this.extractDreamElements(request.dreamContent);
        return {
            interpretation,
            keyInsights: this.generateTaoistInsights(dreamElements),
            emotionalSupport: this.generateTaoistSupport(dreamElements),
            actionableAdvice: this.generateTaoistAdvice(dreamElements),
            confidence: this.calculateConfidence(dreamElements, knowledgeResults),
            sources: knowledgeResults.map(result => result.source || '道家典籍'),
        };
    }
    async handleGeneralConversation(message, context) {
        const content = message.content.toLowerCase();
        if (content.includes('道') || content.includes('太极') || content.includes('阴阳')) {
            return {
                content: `道法自然，无为而治。作为道教解梦师，我相信梦境是宇宙大道在您内心的显现。\n\n请告诉我您的梦境，让我从道家智慧的角度为您解读其中的天道玄机。🌿`,
                type: 'text',
                metadata: { conversation_type: 'philosophy' },
            };
        }
        return {
            content: `您好！我是Taoist Donny，一位专研道教哲学的解梦师。\n\n在道家看来，梦境是心与道的交融，是自然规律在潜意识中的体现。请分享您的梦境，让我为您揭示其中蕴含的东方智慧。🎋`,
            type: 'text',
            metadata: { conversation_type: 'greeting' },
        };
    }
    analyzeWuxing(elements) {
        const wuxingMapping = {
            water: ['水', '河', '海', '雨', '泪'],
            fire: ['火', '热', '光', '太阳', '燃烧'],
            wood: ['树', '花', '草', '竹', '森林'],
            metal: ['金', '刀', '车', '机器', '硬'],
            earth: ['土', '山', '石', '房', '稳']
        };
        let analysis = '';
        const dreamText = elements.symbols.join(' ');
        Object.entries(wuxingMapping).forEach(([element, keywords]) => {
            const found = keywords.some(keyword => dreamText.includes(keyword));
            if (found) {
                switch (element) {
                    case 'water':
                        analysis += '水行主智，您的梦境显示情感流动和智慧启发。';
                        break;
                    case 'fire':
                        analysis += '火行主礼，梦中显现热情活力和精神光明。';
                        break;
                    case 'wood':
                        analysis += '木行主仁，体现生命力和成长发展的力量。';
                        break;
                    case 'metal':
                        analysis += '金行主义，反映坚定意志和理性判断。';
                        break;
                    case 'earth':
                        analysis += '土行主信，象征稳重踏实和包容之心。';
                        break;
                }
            }
        });
        return analysis || '五行调和，梦境显现您内在的平衡状态。';
    }
    analyzeYinyang(elements) {
        const yangSymbols = ['太阳', '火', '上', '明', '动', '刚'];
        const yinSymbols = ['月亮', '水', '下', '暗', '静', '柔'];
        const dreamText = elements.symbols.join(' ') + elements.actions.join(' ');
        const yangCount = yangSymbols.filter(symbol => dreamText.includes(symbol)).length;
        const yinCount = yinSymbols.filter(symbol => dreamText.includes(symbol)).length;
        if (yangCount > yinCount) {
            return '梦境阳气较盛，显示您当前处于积极进取、外向发展的状态。建议适当内敛，寻求平衡。';
        }
        else if (yinCount > yangCount) {
            return '梦境阴气较重，反映您当前偏向内省、宁静的状态。可适当增加行动力，动静结合。';
        }
        else {
            return '阴阳和谐，您的内在状态较为平衡，这是很好的征象。';
        }
    }
    extractTaoistWisdom(knowledgeResults) {
        const wisdoms = [
            '道生一，一生二，二生三，三生万物',
            '无为而无不为，顺其自然乃至高境界',
            '天地不仁，以万物为刍狗',
            '知足不辱，知止不殆',
            '上善若水，水善利万物而不争'
        ];
        if (knowledgeResults.length > 0) {
            return `根据道家典籍：${knowledgeResults[0].content || wisdoms[0]}`;
        }
        return wisdoms[Math.floor(Math.random() * wisdoms.length)];
    }
    generateTaoistInsights(elements) {
        const insights = [];
        if (elements.emotions.includes('焦虑') || elements.emotions.includes('害怕')) {
            insights.push('内心的不安源于对道的偏离，回归自然本性可得安宁');
        }
        if (elements.actions.includes('追') || elements.actions.includes('逃')) {
            insights.push('追逐与逃避皆为执着，学会无为而治，随顺自然');
        }
        if (elements.symbols.includes('水')) {
            insights.push('水的智慧：柔弱胜刚强，以柔克刚是生命的艺术');
        }
        insights.push('梦境提醒您保持心境澄明，如镜水般反映真实');
        return insights;
    }
    generateTaoistSupport(elements) {
        return `道家认为，人生如梦，梦如人生。您的梦境是内在智慧的显现，无需过分执着于得失成败。

天地万物皆有其规律，您的困惑和迷茫都是成长路上的必经之路。保持一颗平常心，顺应自然，道法自然，您自然会找到内心的宁静与和谐。

记住：无为不是无所作为，而是不强求、不执着，顺其自然地生活。`;
    }
    generateTaoistAdvice(elements) {
        const advice = [];
        advice.push('🌿 早起观日出，感受天地之气的循环变化');
        advice.push('🧘 每日静坐冥想，让心境如止水般澄明');
        advice.push('📖 阅读道德经，体悟无为而治的人生智慧');
        advice.push('🍃 多接触自然，在山水中寻找内心的平衡');
        advice.push('💨 练习呼吸调息，调和体内的阴阳之气');
        return advice.slice(0, 3).join('\n');
    }
    calculateConfidence(elements, knowledgeResults) {
        let confidence = 0.7;
        confidence += Math.min(elements.symbols.length * 0.05, 0.2);
        confidence += Math.min(knowledgeResults.length * 0.02, 0.1);
        return Math.min(confidence, 0.95);
    }
};
exports.TaoistDonnyService = TaoistDonnyService;
exports.TaoistDonnyService = TaoistDonnyService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [agent_runtime_service_1.AgentRuntimeService,
        conversation_data_access_service_1.ConversationDataAccessService,
        agent_data_access_service_1.AgentDataAccessService,
        rag_service_1.RAGService])
], TaoistDonnyService);
//# sourceMappingURL=taoist-donny.service.js.map