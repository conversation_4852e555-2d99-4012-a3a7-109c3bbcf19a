import { Router } from 'express';
import { authenticateJWT } from '../middlewares/auth';
import { AppError } from '../middlewares/errorHandler';
import { createMessage, getUserDonnyMessages, getDreamMessages } from '../services/messageService';

const router = Router();

// 获取用户与特定Donny的对话历史
router.get('/donny/:donnyType', authenticateJWT, async (req, res, next) => {
  try {
    if (!req.user || !req.user.userId) {
      throw new AppError('User not authenticated', 401);
    }
    
    const { donnyType } = req.params;
    const { limit, before } = req.query;
    
    const limitNum = limit ? parseInt(limit as string, 10) : 50;
    const beforeDate = before ? new Date(before as string) : undefined;
    
    const messages = await getUserDonnyMessages(req.user.userId, donnyType, limitNum, beforeDate);
    
    res.json({
      status: 'success',
      data: {
        messages,
      },
    });
  } catch (error) {
    next(error);
  }
});

// 获取特定梦境的对话历史
router.get('/dream/:dreamId', authenticateJWT, async (req, res, next) => {
  try {
    const { dreamId } = req.params;
    
    const messages = await getDreamMessages(dreamId);
    
    res.json({
      status: 'success',
      data: {
        messages,
      },
    });
  } catch (error) {
    next(error);
  }
});

// 创建新消息
router.post('/', authenticateJWT, async (req, res, next) => {
  try {
    if (!req.user || !req.user.userId) {
      throw new AppError('User not authenticated', 401);
    }
    
    const { content, sender, donnyType, dreamId } = req.body;
    
    if (!content || !sender || !donnyType) {
      throw new AppError('Missing required fields', 400);
    }
    
    if (sender !== 'user' && sender !== 'donny') {
      throw new AppError('Invalid sender value', 400);
    }
    
    const message = await createMessage(
      req.user.userId,
      content,
      sender,
      donnyType,
      dreamId
    );
    
    res.status(201).json({
      status: 'success',
      data: {
        message,
      },
    });
  } catch (error) {
    next(error);
  }
});

export default router;
