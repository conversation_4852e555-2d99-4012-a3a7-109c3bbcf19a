import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  StartCallAnalyticsStreamTranscriptionRequest,
  StartCallAnalyticsStreamTranscriptionResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  TranscribeStreamingClientResolvedConfig,
} from "../TranscribeStreamingClient";
export { __MetadataBearer };
export { $Command };
export interface StartCallAnalyticsStreamTranscriptionCommandInput
  extends StartCallAnalyticsStreamTranscriptionRequest {}
export interface StartCallAnalyticsStreamTranscriptionCommandOutput
  extends StartCallAnalyticsStreamTranscriptionResponse,
    __MetadataBearer {}
declare const StartCallAnalyticsStreamTranscriptionCommand_base: {
  new (
    input: StartCallAnalyticsStreamTranscriptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartCallAnalyticsStreamTranscriptionCommandInput,
    StartCallAnalyticsStreamTranscriptionCommandOutput,
    TranscribeStreamingClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartCallAnalyticsStreamTranscriptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartCallAnalyticsStreamTranscriptionCommandInput,
    StartCallAnalyticsStreamTranscriptionCommandOutput,
    TranscribeStreamingClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartCallAnalyticsStreamTranscriptionCommand extends StartCallAnalyticsStreamTranscriptionCommand_base {
  protected static __types: {
    api: {
      input: StartCallAnalyticsStreamTranscriptionRequest;
      output: StartCallAnalyticsStreamTranscriptionResponse;
    };
    sdk: {
      input: StartCallAnalyticsStreamTranscriptionCommandInput;
      output: StartCallAnalyticsStreamTranscriptionCommandOutput;
    };
  };
}
