import { DatabaseService } from '../database.service';
export interface Dream {
    id: string;
    user_id: string;
    title?: string;
    content: string;
    metadata?: any;
    status: 'pending' | 'processing' | 'completed' | 'archived';
    privacy_level: 'private' | 'public' | 'anonymous';
    created_at: string;
    updated_at: string;
}
export interface Interpretation {
    id: string;
    dream_id: string;
    agent_type: string;
    content: string;
    confidence_score?: number;
    metadata?: any;
    status: 'draft' | 'final' | 'revised';
    created_at: string;
    updated_at: string;
}
export interface UserFeedback {
    id: string;
    interpretation_id: string;
    user_id: string;
    rating?: number;
    feedback_text?: string;
    feedback_type?: string;
    created_at: string;
}
export declare class DreamDataAccessService {
    private databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService);
    createDream(agentType: string, userId: string, content: string, title?: string, metadata?: any, privacyLevel?: 'private' | 'public' | 'anonymous'): Promise<{
        dream: Dream;
        error?: any;
    }>;
    getUserDreams(agentType: string, userId: string, status?: string, limit?: number): Promise<Dream[]>;
    getDreamById(agentType: string, dreamId: string): Promise<Dream | null>;
    updateDreamStatus(agentType: string, dreamId: string, status: 'pending' | 'processing' | 'completed' | 'archived'): Promise<{
        success: boolean;
        error?: any;
    }>;
    createInterpretation(agentType: string, dreamId: string, content: string, confidenceScore?: number, metadata?: any, status?: 'draft' | 'final' | 'revised'): Promise<{
        interpretation: Interpretation;
        error?: any;
    }>;
    getDreamInterpretations(agentType: string, dreamId: string): Promise<Interpretation[]>;
    getAgentInterpretations(agentType: string, dreamId: string, targetAgentType?: string): Promise<Interpretation[]>;
    updateInterpretationStatus(agentType: string, interpretationId: string, status: 'draft' | 'final' | 'revised'): Promise<{
        success: boolean;
        error?: any;
    }>;
    updateInterpretationContent(agentType: string, interpretationId: string, content: string, confidenceScore?: number, metadata?: any): Promise<{
        success: boolean;
        error?: any;
    }>;
    createUserFeedback(userId: string, interpretationId: string, rating?: number, feedbackText?: string, feedbackType?: string): Promise<{
        feedback: UserFeedback;
        error?: any;
    }>;
    getInterpretationFeedback(agentType: string, interpretationId: string): Promise<UserFeedback[]>;
    getUserFeedback(agentType: string, userId: string): Promise<UserFeedback[]>;
    getDreamFullInfo(agentType: string, dreamId: string): Promise<{
        dream: Dream;
        interpretations: Interpretation[];
        feedback: UserFeedback[];
        error?: any;
    }>;
}
