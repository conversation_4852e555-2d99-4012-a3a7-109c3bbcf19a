import { DatabaseService } from '../database/database.service';
import { AgentType } from '../database/types/rag.types';
export interface SecurityPermission {
    agentType: AgentType;
    resource: string;
    action: string;
    allowed: boolean;
    conditions?: any;
}
export interface SecurityAuditLog {
    id: string;
    agentType: AgentType;
    userId: string;
    resource: string;
    action: string;
    allowed: boolean;
    timestamp: Date;
    metadata?: any;
}
export declare class SecurityController {
    private readonly databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService);
    checkPermission(request: {
        agentType: AgentType;
        userId: string;
        resource: string;
        action: string;
        requestData?: any;
    }): Promise<{
        success: boolean;
        data: {
            allowed: boolean;
            reason?: string;
            metadata?: any;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data?: undefined;
    }>;
    getAgentPermissions(agentType: AgentType): Promise<{
        success: boolean;
        data: {
            agentType: AgentType;
            permissions: SecurityPermission[];
            total: number;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data?: undefined;
    }>;
    getAuditLogs(agentType?: AgentType, userId?: string, resource?: string, startDate?: string, endDate?: string, page?: number, limit?: number): Promise<{
        success: boolean;
        data: {
            logs: SecurityAuditLog[];
            pagination: {
                page: number;
                limit: number;
                total: number;
                totalPages: number;
            };
            filters: {
                agentType: AgentType;
                userId: string;
                resource: string;
                startDate: string;
                endDate: string;
            };
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data?: undefined;
    }>;
    getSecurityStats(agentType?: AgentType, timeRange?: string): Promise<{
        success: boolean;
        data: {
            totalRequests: number;
            allowedRequests: number;
            deniedRequests: number;
            byAgent: Record<AgentType, number>;
            byResource: Record<string, number>;
            byAction: Record<string, number>;
            timeline: any[];
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data?: undefined;
    }>;
    createPermission(permission: {
        agentType: AgentType;
        resource: string;
        action: string;
        conditions?: any;
        metadata?: any;
    }): Promise<{
        success: boolean;
        data: {
            createdAt: Date;
            updatedAt: Date;
            agentType: AgentType;
            resource: string;
            action: string;
            conditions?: any;
            metadata?: any;
            id: string;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data?: undefined;
    }>;
    private performPermissionCheck;
    private getAllowedResources;
    private getDeniedResources;
    private getAllowedActions;
    private checkDataIsolation;
    private getDefaultPermissions;
    private logAccess;
    private sanitizeLogData;
}
