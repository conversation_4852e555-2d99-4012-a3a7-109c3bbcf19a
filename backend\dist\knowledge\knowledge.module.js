"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const database_module_1 = require("../database/database.module");
const embedding_service_1 = require("./embedding.service");
const rag_service_1 = require("./rag.service");
const knowledge_controller_1 = require("./knowledge.controller");
const knowledge_data_access_service_1 = require("../database/services/knowledge-data-access.service");
let KnowledgeModule = class KnowledgeModule {
};
exports.KnowledgeModule = KnowledgeModule;
exports.KnowledgeModule = KnowledgeModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            database_module_1.DatabaseModule,
        ],
        providers: [
            embedding_service_1.EmbeddingService,
            rag_service_1.RAGService,
            knowledge_data_access_service_1.KnowledgeDataAccessService,
        ],
        controllers: [
            knowledge_controller_1.KnowledgeController,
        ],
        exports: [
            embedding_service_1.EmbeddingService,
            rag_service_1.RAGService,
            knowledge_data_access_service_1.KnowledgeDataAccessService,
        ],
    })
], KnowledgeModule);
//# sourceMappingURL=knowledge.module.js.map