{"version": 3, "file": "provider.d.ts", "sourceRoot": "", "sources": ["../../src/provider.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,UAAU,EACV,MAAM,EACN,SAAS,EACT,WAAW,EACX,oBAAoB,EACpB,cAAc,EAEd,UAAU,EAEV,WAAW,EACZ,MAAM,iBAAiB,CAAC;AAGzB,OAAO,EAEL,8BAA8B,EAC/B,MAAM,gBAAgB,CAAC;AAExB,MAAM,CAAC,OAAO,WAAW,QAAQ;IAC/B,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC;IAChC,QAAQ,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;IAE/B,IAAI,CAAC,CACH,EAAE,EAAE,WAAW,EACf,OAAO,CAAC,EAAE,MAAM,EAAE,EAClB,IAAI,CAAC,EAAE,WAAW,GACjB,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACjC,cAAc,CAAC,CACb,EAAE,EAAE,WAAW,EACf,OAAO,CAAC,EAAE,MAAM,EAAE,EAClB,IAAI,CAAC,EAAE,cAAc,GACpB,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACjC,OAAO,CAAC,CACN,aAAa,EAAE;QAAE,EAAE,EAAE,WAAW,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAA;KAAE,EAAE,EACxD,IAAI,CAAC,EAAE,cAAc,GACpB,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;IACxC,QAAQ,CAAC,CACP,EAAE,EAAE,WAAW,EACf,OAAO,CAAC,EAAE,MAAM,EAAE,EAClB,UAAU,CAAC,EAAE,UAAU,EACvB,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,EAAE,GACtC,OAAO,CAAC,8BAA8B,CAAC,CAAC;CAC5C;AAED;;;GAGG;AACH,qBAAa,cAAe,YAAW,QAAQ;IAS3C,QAAQ,CAAC,UAAU,EAAE,UAAU;IAC/B,QAAQ,CAAC,MAAM,EAAE,MAAM;IACvB,QAAQ,CAAC,IAAI,EAAE,cAAc;IAV/B,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC;IAE9B;;;;OAIG;gBAEQ,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,cAAc;IAK/B,MAAM,CAAC,cAAc,IAAI,cAAc;IAOvC;;;;;;;OAOG;IACH,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,cAAc,GAAG,cAAc;IAcjE;;;;;OAKG;IACH,MAAM,CAAC,GAAG,IAAI,cAAc;IAkB5B;;;;;;OAMG;IACG,cAAc,CAClB,EAAE,EAAE,WAAW,EACf,OAAO,CAAC,EAAE,MAAM,EAAE,EAClB,IAAI,CAAC,EAAE,cAAc,GACpB,OAAO,CAAC,oBAAoB,CAAC;IA4ChC;;;;;OAKG;IACG,OAAO,CACX,aAAa,EAAE;QAAE,EAAE,EAAE,WAAW,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAA;KAAE,EAAE,EACxD,IAAI,CAAC,EAAE,cAAc,GACpB,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IA8DvC;;;;;;OAMG;IACG,QAAQ,CACZ,EAAE,EAAE,WAAW,EACf,OAAO,CAAC,EAAE,MAAM,EAAE,EAClB,UAAU,CAAC,EAAE,UAAU,EACvB,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,EAAE,GACtC,OAAO,CAAC,8BAA8B,CAAC;CAwB3C;AAWD,MAAM,MAAM,aAAa,GAAG;IAC1B,EAAE,EAAE,WAAW,CAAC;IAChB,OAAO,EAAE,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;CACpC,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,MAAM;IACrB,eAAe,CAAC,EAAE,EAAE,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;IACvD,mBAAmB,CAAC,GAAG,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IAChE,SAAS,EAAE,SAAS,CAAC;CACtB;AAyCD;;GAEG;AACH,wBAAgB,WAAW,CAAC,QAAQ,EAAE,QAAQ,QAE7C;AAED;;GAEG;AACH,wBAAgB,WAAW,IAAI,QAAQ,CAKtC"}