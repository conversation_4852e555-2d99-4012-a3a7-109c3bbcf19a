{"version": 3, "file": "memory-manager.service.js", "sourceRoot": "", "sources": ["../../../src/memory/services/memory-manager.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,kDAQyB;AAEzB,2EAAqE;AACrE,6EAAuE;AACvE,yEAAmE;AACnE,iFAA4E;AAC5E,yEAAoE;AAG7D,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAG/B,YACmB,sBAA8C,EAC9C,uBAAgD,EAChD,qBAA4C,EAC5C,oBAAgD,EAChD,gBAAwC;QAJxC,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,4BAAuB,GAAvB,uBAAuB,CAAyB;QAChD,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,yBAAoB,GAApB,oBAAoB,CAA4B;QAChD,qBAAgB,GAAhB,gBAAgB,CAAwB;QAP1C,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;QAS9D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,UAA+B;QAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;QAEzD,IAAI,CAAC;YACH,QAAQ,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC9B,KAAK,yBAAU,CAAC,UAAU;oBACxB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,UAAsC,CAAC,CAAC;gBACzF,KAAK,yBAAU,CAAC,WAAW;oBACzB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,UAAuC,CAAC,CAAC;gBAC3F,KAAK,yBAAU,CAAC,SAAS;oBACvB,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,UAAqC,CAAC,CAAC;gBACvF;oBAEE,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,UAAsC,CAAC,CAAC;YAC3F,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,KAAkB;QACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAEzD,IAAI,CAAC;YACH,MAAM,OAAO,GAAiB,EAAE,CAAC;YAGjC,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;gBAErB,QAAQ,KAAK,CAAC,UAAU,EAAE,CAAC;oBACzB,KAAK,yBAAU,CAAC,UAAU;wBACxB,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBACnE,MAAM;oBACR,KAAK,yBAAU,CAAC,WAAW;wBACzB,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBACpE,MAAM;oBACR,KAAK,yBAAU,CAAC,SAAS;wBACvB,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBAClE,MAAM;gBACV,CAAC;YACH,CAAC;iBAAM,CAAC;gBAEN,MAAM,CAAC,YAAY,EAAE,aAAa,EAAE,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;oBACnE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,KAAK,CAAC;oBAC3C,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,KAAK,CAAC;oBAC5C,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,KAAK,CAAC;iBAC3C,CAAC,CAAC;gBAEH,OAAO,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;gBAC/B,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;YAChC,CAAC;YAGD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACpB,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU,EAAE,CAAC;oBAClC,OAAO,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;gBACrC,CAAC;gBACD,OAAO,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACvD,CAAC,CAAC,CAAC;YAGH,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK;gBACzB,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;gBACrE,CAAC,CAAC,OAAO,CAAC;YAEZ,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,SAAoB,EACpB,MAAc,EACd,YAAoB,EACpB,aAAqB,EAAE;QAEvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,SAAS,SAAS,MAAM,EAAE,CAAC,CAAC;QAElE,IAAI,CAAC;YAEH,MAAM,KAAK,GAAgB;gBACzB,SAAS;gBACT,MAAM;gBACN,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,UAAU;gBACjB,mBAAmB,EAAE,GAAG;aACzB,CAAC;YAGF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAGlD,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QACvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAA4B;QAC/D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,QAAQ,EAAE,CAAC,CAAC;QAE5C,IAAI,CAAC;YAEH,IAAI,MAAM,GAAsB,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACrF,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAmC,CAAC,CAAC;YACjG,CAAC;YAED,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC/D,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAoC,CAAC,CAAC;YACnG,CAAC;YAED,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC7D,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAkC,CAAC,CAAC;YAC/F,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,EAAE,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,QAAQ,EAAE,CAAC,CAAC;QAE5C,IAAI,CAAC;YAEH,IAAI,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACjE,IAAI,OAAO;gBAAE,OAAO,IAAI,CAAC;YAEzB,OAAO,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC9D,IAAI,OAAO;gBAAE,OAAO,IAAI,CAAC;YAEzB,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC5D,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,SAAqB,EAAE,MAAe;QAUzD,IAAI,CAAC;YACH,MAAM,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC7D,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;gBACvD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;gBACxD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;aACvD,CAAC,CAAC;YAEH,OAAO;gBACL,SAAS,EAAE,UAAU;gBACrB,UAAU,EAAE,WAAW;gBACvB,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE;oBACL,QAAQ,EAAE,UAAU,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;oBAChE,WAAW,EAAE,UAAU,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW;oBACrF,iBAAiB,EAAE,CACjB,UAAU,CAAC,iBAAiB;wBAC5B,WAAW,CAAC,iBAAiB;wBAC7B,SAAS,CAAC,iBAAiB,CAC5B,GAAG,CAAC;iBACN;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,SAAoB,EAAE,MAAc;QAC7D,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB;QAIxB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,EAAE,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB;QAK1B,IAAI,CAAC;YACH,MAAM,CAAC,YAAY,EAAE,aAAa,EAAE,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACnE,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,EAAE;gBACpD,IAAI,CAAC,uBAAuB,CAAC,4BAA4B,EAAE;gBAC3D,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE;aAChD,CAAC,CAAC;YAEH,OAAO;gBACL,SAAS,EAAE,YAAY;gBACvB,UAAU,EAAE,aAAa;gBACzB,QAAQ,EAAE,WAAW;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW;QAIf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG;gBACf,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,cAAc,EAAE,IAAI;gBACpB,aAAa,EAAE,IAAI;gBACnB,SAAS,EAAE,IAAI;aAChB,CAAC;YAGF,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC;YAE9E,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9C,OAAO;gBACL,MAAM,EAAE,WAAW;gBACnB,QAAQ,EAAE;oBACR,eAAe,EAAE,KAAK;oBACtB,gBAAgB,EAAE,KAAK;oBACvB,cAAc,EAAE,KAAK;oBACrB,aAAa,EAAE,KAAK;oBACpB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAhTY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;qCAKgC,kDAAsB;QACrB,oDAAuB;QACzB,gDAAqB;QACtB,yDAA0B;QAC9B,iDAAsB;GARhD,oBAAoB,CAgThC"}