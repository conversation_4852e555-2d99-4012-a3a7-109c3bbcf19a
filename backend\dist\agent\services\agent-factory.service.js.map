{"version": 3, "file": "agent-factory.service.js", "sourceRoot": "", "sources": ["../../../src/agent/services/agent-factory.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,uCAAyC;AAEzC,iEAA4D;AAC5D,+DAA0D;AAC1D,6DAAwD;AAwBjD,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAK9B,YAA6B,SAAoB;QAApB,cAAS,GAAT,SAAS,CAAW;QAJhC,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;QAC9C,iBAAY,GAAkC,IAAI,GAAG,EAAE,CAAC;QACxD,kBAAa,GAA6D,IAAI,GAAG,EAAE,CAAC;QAGnG,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAKO,uBAAuB;QAC7B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,yCAAkB,CAAC,CAAC;QACrD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,uCAAiB,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,qCAAgB,CAAC,CAAC;QAIjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,IAAI,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,CAAC;IACxE,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,OAA6B;QAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QAEjD,IAAI,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC/C,MAAM,IAAI,KAAK,CAAC,gBAAgB,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;YACvD,CAAC;YAGD,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAG9D,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,eAAe,EAAE,CAAC,CAAC;gBACrD,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAChD,CAAC;YAGD,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAGpE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAGrE,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;YACtE,CAAC;YAGD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;YAEtD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,eAAe,EAAE,CAAC,CAAC;YACnD,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,QAAQ,CAAC,SAAoB,EAAE,MAAe,EAAE,SAAkB;QAChE,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC;YACnD,SAAS;YACT,MAAM;YACN,SAAS;SACV,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC;IACxD,CAAC;IAKD,KAAK,CAAC,sBAAsB;QAC1B,MAAM,UAAU,GAAgB,EAAE,CAAC;QAEnC,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7C,IAAI,CAAC;gBAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;gBAC3D,MAAM,OAAO,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;gBAC5C,MAAM,YAAY,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC;gBAEpD,UAAU,CAAC,IAAI,CAAC;oBACd,EAAE,EAAE,SAAS;oBACb,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,OAAO;oBACP,YAAY;oBACZ,MAAM,EAAE,QAAQ;oBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,SAAS,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAKD,mBAAmB;QAMjB,MAAM,MAAM,GAA2B,EAAE,CAAC;QAC1C,IAAI,cAAc,GAAgB,IAAI,CAAC;QACvC,IAAI,cAAc,GAAgB,IAAI,CAAC;QAEvC,KAAK,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpD,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAGjD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,IAAI,GAAG,GAAG,cAAc,EAAE,CAAC;gBAC5C,cAAc,GAAG,GAAG,CAAC;YACvB,CAAC;YACD,IAAI,CAAC,cAAc,IAAI,GAAG,GAAG,cAAc,EAAE,CAAC;gBAC5C,cAAc,GAAG,GAAG,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YACnC,MAAM;YACN,cAAc;YACd,cAAc;SACf,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,OAAa;QAMjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAElC,MAAM,eAAe,GAAG,EAAE,CAAC;QAG3B,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAG3C,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7F,WAAW,IAAI,GAAG,CAAC;QACrB,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/D,WAAW,IAAI,GAAG,CAAC;QACrB,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/D,WAAW,IAAI,GAAG,CAAC;QACrB,CAAC;QAED,eAAe,CAAC,IAAI,CAAC;YACnB,IAAI,EAAE,QAAqB;YAC3B,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;YACvC,SAAS,EAAE,sBAAsB;SAClC,CAAC,CAAC;QAGH,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/F,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9F,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/D,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAED,eAAe,CAAC,IAAI,CAAC;YACnB,IAAI,EAAE,OAAoB;YAC1B,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;YACtC,SAAS,EAAE,sBAAsB;SAClC,CAAC,CAAC;QAGH,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7F,SAAS,IAAI,GAAG,CAAC;QACnB,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9F,SAAS,IAAI,GAAG,CAAC;QACnB,CAAC;QACD,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAChG,SAAS,IAAI,GAAG,CAAC;QACnB,CAAC;QAED,eAAe,CAAC,IAAI,CAAC;YACnB,IAAI,EAAE,MAAmB;YACzB,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;YACrC,SAAS,EAAE,oBAAoB;SAChC,CAAC,CAAC;QAGH,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAG5D,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;YACxC,OAAO;gBACL,WAAW,EAAE,UAAU;gBACvB,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,mCAAmC;gBAC9C,YAAY,EAAE,eAAe;aAC9B,CAAC;QACJ,CAAC;QAED,OAAO;YACL,WAAW,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI;YACpC,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,UAAU;YACzC,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS;YACvC,YAAY,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;SACvC,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,iBAAyB,EAAE;QACrD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,cAAc,gBAAgB,CAAC,CAAC;QAEzD,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAKrE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,YAAY,WAAW,CAAC,CAAC;QAC/C,OAAO,YAAY,CAAC;IACtB,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAAC,SAAoB;QACjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,SAAS,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC;YAEH,MAAM,iBAAiB,GAAG,EAAE,CAAC;YAC7B,KAAK,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpD,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC;oBAC3C,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;YAGD,KAAK,MAAM,UAAU,IAAI,iBAAiB,EAAE,CAAC;gBAC3C,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAEvC,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,iBAAiB,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAOO,uBAAuB,CAAC,OAAyE;QACvG,MAAM,KAAK,GAAa,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE5C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,KAAK,CAAC,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,KAAK,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAKO,KAAK,CAAC,kBAAkB,CAC9B,aAA+B,EAC/B,aAAkB;QAElB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAKhC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;CACF,CAAA;AAxTY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAM6B,gBAAS;GALtC,mBAAmB,CAwT/B"}