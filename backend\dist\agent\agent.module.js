"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentModule = void 0;
const common_1 = require("@nestjs/common");
const database_module_1 = require("../database/database.module");
const agent_runtime_service_1 = require("./services/agent-runtime.service");
const almighty_donny_service_1 = require("./services/almighty-donny.service");
const agent_controller_1 = require("./controllers/agent.controller");
let AgentModule = class AgentModule {
};
exports.AgentModule = AgentModule;
exports.AgentModule = AgentModule = __decorate([
    (0, common_1.Module)({
        imports: [database_module_1.DatabaseModule],
        providers: [
            agent_runtime_service_1.AgentRuntimeService,
            almighty_donny_service_1.AlmightyDonnyService,
        ],
        controllers: [agent_controller_1.AgentController],
        exports: [
            agent_runtime_service_1.AgentRuntimeService,
            almighty_donny_service_1.AlmightyDonnyService,
        ],
    })
], AgentModule);
//# sourceMappingURL=agent.module.js.map