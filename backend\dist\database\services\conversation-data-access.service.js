"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ConversationDataAccessService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationDataAccessService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../database.service");
let ConversationDataAccessService = ConversationDataAccessService_1 = class ConversationDataAccessService {
    constructor(databaseService) {
        this.databaseService = databaseService;
        this.logger = new common_1.Logger(ConversationDataAccessService_1.name);
    }
    async createConversationSession(agentType, userId, sessionType = 'general', metadata) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'conversation_sessions',
            operation: 'insert',
            data: {
                user_id: userId,
                session_type: sessionType,
                status: 'active',
                metadata,
                started_at: new Date().toISOString(),
            },
        });
        if (result.error) {
            return { session: null, error: result.error };
        }
        return { session: result.data[0] };
    }
    async getUserConversationSessions(agentType, userId, status, limit = 20) {
        const filters = { user_id: userId };
        if (status) {
            filters.status = status;
        }
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'conversation_sessions',
            operation: 'select',
            filters,
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data
            .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
            .slice(0, limit);
    }
    async getConversationSessionById(agentType, sessionId) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'conversation_sessions',
            operation: 'select',
            filters: { id: sessionId },
        });
        if (result.error || !result.data || result.data.length === 0) {
            return null;
        }
        return result.data[0];
    }
    async updateConversationSessionStatus(agentType, sessionId, status) {
        const updateData = { status };
        if (status === 'completed' || status === 'abandoned') {
            updateData.ended_at = new Date().toISOString();
        }
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'conversation_sessions',
            operation: 'update',
            data: updateData,
            filters: { id: sessionId },
        });
        return { success: !result.error, error: result.error };
    }
    async addMessageToSession(agentType, sessionId, senderType, senderId, content, messageType = 'text', metadata) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'conversation_messages',
            operation: 'insert',
            data: {
                session_id: sessionId,
                sender_type: senderType,
                sender_id: senderId,
                message_type: messageType,
                content,
                metadata,
            },
        });
        if (result.error) {
            return { message: null, error: result.error };
        }
        return { message: result.data[0] };
    }
    async getSessionMessages(agentType, sessionId, limit = 100, offset = 0) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'conversation_messages',
            operation: 'select',
            filters: { session_id: sessionId },
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data
            .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
            .slice(offset, offset + limit);
    }
    async getRecentMessages(agentType, sessionId, count = 10) {
        const allMessages = await this.getSessionMessages(agentType, sessionId);
        return allMessages.slice(-count);
    }
    async getConversationFullInfo(agentType, sessionId, messageLimit = 100) {
        try {
            const session = await this.getConversationSessionById(agentType, sessionId);
            if (!session) {
                return {
                    session: null,
                    messages: [],
                    error: { message: 'Conversation session not found' }
                };
            }
            const messages = await this.getSessionMessages(agentType, sessionId, messageLimit);
            return { session, messages };
        }
        catch (error) {
            this.logger.error(`获取对话完整信息失败: ${error.message}`);
            return { session: null, messages: [], error };
        }
    }
    async getUserConversationStats(agentType, userId) {
        try {
            const sessions = await this.getUserConversationSessions(agentType, userId, undefined, 1000);
            const totalSessions = sessions.length;
            const activeSessions = sessions.filter(s => s.status === 'active').length;
            const completedSessions = sessions.filter(s => s.status === 'completed').length;
            let totalMessages = 0;
            for (const session of sessions) {
                const messages = await this.getSessionMessages(agentType, session.id);
                totalMessages += messages.length;
            }
            return {
                totalSessions,
                activeSessions,
                completedSessions,
                totalMessages,
            };
        }
        catch (error) {
            this.logger.error(`获取用户对话统计失败: ${error.message}`);
            return {
                totalSessions: 0,
                activeSessions: 0,
                completedSessions: 0,
                totalMessages: 0,
                error,
            };
        }
    }
    async cleanupOldSessions(agentType, daysOld = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysOld);
            const client = this.databaseService.getClient();
            const { data: sessionsToDelete } = await client
                .from('conversation_sessions')
                .select('id')
                .eq('status', 'completed')
                .lt('ended_at', cutoffDate.toISOString());
            if (sessionsToDelete && sessionsToDelete.length > 0) {
                const sessionIds = sessionsToDelete.map(s => s.id);
                await client
                    .from('conversation_messages')
                    .delete()
                    .in('session_id', sessionIds);
            }
            const { data, error } = await client
                .from('conversation_sessions')
                .delete()
                .eq('status', 'completed')
                .lt('ended_at', cutoffDate.toISOString());
            if (error) {
                return { cleanedCount: 0, error };
            }
            let cleanedCount = 0;
            if (data) {
                cleanedCount = Array.isArray(data) ? data.length : 1;
            }
            this.logger.log(`清理了 ${cleanedCount} 个旧的对话会话`);
            return { cleanedCount };
        }
        catch (error) {
            this.logger.error(`清理旧对话会话失败: ${error.message}`);
            return { cleanedCount: 0, error };
        }
    }
    async searchMessages(agentType, userId, searchText, limit = 50) {
        try {
            const client = this.databaseService.getClient();
            const { data, error } = await client
                .from('conversation_messages')
                .select('*, conversation_sessions!inner(user_id)')
                .eq('conversation_sessions.user_id', userId)
                .ilike('content', `%${searchText}%`)
                .order('created_at', { ascending: false })
                .limit(limit);
            if (error) {
                this.logger.warn(`搜索消息失败: ${error.message}`);
                return [];
            }
            return data || [];
        }
        catch (error) {
            this.logger.error(`搜索消息错误: ${error.message}`);
            return [];
        }
    }
    async createBatchMessages(agentType, sessionId, messages) {
        let successCount = 0;
        const errors = [];
        for (const message of messages) {
            const { error } = await this.addMessageToSession(agentType, sessionId, message.senderType, message.senderId, message.content, message.messageType || 'text', message.metadata);
            if (error) {
                errors.push(error);
            }
            else {
                successCount++;
            }
        }
        this.logger.log(`批量创建消息完成: 成功 ${successCount}/${messages.length}`);
        return { successCount, errors };
    }
};
exports.ConversationDataAccessService = ConversationDataAccessService;
exports.ConversationDataAccessService = ConversationDataAccessService = ConversationDataAccessService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], ConversationDataAccessService);
//# sourceMappingURL=conversation-data-access.service.js.map