import { create } from 'zustand';
import { DonnyType, InteractionState, PaymentStatus } from '../types';

interface DonnyState {
  activeDonny: DonnyType | null;
  selectedBusinessDonny: DonnyType | null;
  isSwitchingDonny: boolean;
  paymentStatus: PaymentStatus;
  interactionState: InteractionState;

  // Actions
  selectBusinessDonny: (donny: DonnyType) => void;
  setActiveDonny: (donny: DonnyType | null) => void;
  startDonnySwitching: () => void;
  finishDonnySwitching: () => void;
  setPaymentStatus: (status: PaymentStatus) => void;
  setDreamSubmitted: (submitted: boolean) => void;
  setDreamInterpretationCompleted: (completed: boolean) => void;
  setPendingFeedback: (pending: boolean) => void;
  resetInteractionState: () => void;
}

export const useDonnyStore = create<DonnyState>((set) => ({
  activeDonny: 'almighty',
  selectedBusinessDonny: null,
  isSwitchingDonny: false,
  paymentStatus: 'idle',
  interactionState: {
    hasPaid: false,
    hasDreamSubmitted: false,
    pendingFeedback: false,
    dreamInterpretationCompleted: false,
  },

  // Actions
  selectBusinessDonny: (donny) => set({ selectedBusinessDonny: donny }),

  setActiveDonny: (donny) => set({ activeDonny: donny }),

  startDonnySwitching: () => set({ isSwitchingDonny: true }),

  finishDonnySwitching: () => set((state) => ({
    isSwitchingDonny: false,
    activeDonny: state.selectedBusinessDonny
  })),

  setPaymentStatus: (status) => set((state) => ({
    paymentStatus: status,
    interactionState: status === 'success'
      ? { ...state.interactionState, hasPaid: true }
      : state.interactionState
  })),

  setDreamSubmitted: (submitted) => set((state) => ({
    interactionState: { ...state.interactionState, hasDreamSubmitted: submitted }
  })),

  setDreamInterpretationCompleted: (completed) => set((state) => ({
    interactionState: { ...state.interactionState, dreamInterpretationCompleted: completed }
  })),

  setPendingFeedback: (pending) => set((state) => ({
    interactionState: { ...state.interactionState, pendingFeedback: pending }
  })),

  resetInteractionState: () => set({
    selectedBusinessDonny: null,
    activeDonny: 'almighty',
    paymentStatus: 'idle',
    interactionState: {
      hasPaid: false,
      hasDreamSubmitted: false,
      pendingFeedback: false,
      dreamInterpretationCompleted: false,
    }
  }),
}));
