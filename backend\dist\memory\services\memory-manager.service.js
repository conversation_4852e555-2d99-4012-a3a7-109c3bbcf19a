"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MemoryManagerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryManagerService = void 0;
const common_1 = require("@nestjs/common");
const memory_types_1 = require("../memory.types");
const short_term_memory_service_1 = require("./short-term-memory.service");
const medium_term_memory_service_1 = require("./medium-term-memory.service");
const long_term_memory_service_1 = require("./long-term-memory.service");
const memory_consolidation_service_1 = require("./memory-consolidation.service");
const memory_analytics_service_1 = require("./memory-analytics.service");
let MemoryManagerService = MemoryManagerService_1 = class MemoryManagerService {
    constructor(shortTermMemoryService, mediumTermMemoryService, longTermMemoryService, consolidationService, analyticsService) {
        this.shortTermMemoryService = shortTermMemoryService;
        this.mediumTermMemoryService = mediumTermMemoryService;
        this.longTermMemoryService = longTermMemoryService;
        this.consolidationService = consolidationService;
        this.analyticsService = analyticsService;
        this.logger = new common_1.Logger(MemoryManagerService_1.name);
        this.logger.log('记忆管理服务初始化完成');
    }
    async storeMemory(memoryData) {
        this.logger.debug(`存储记忆 - 类型: ${memoryData.memoryType}`);
        try {
            switch (memoryData.memoryType) {
                case memory_types_1.MemoryType.SHORT_TERM:
                    return await this.shortTermMemoryService.store(memoryData);
                case memory_types_1.MemoryType.MEDIUM_TERM:
                    return await this.mediumTermMemoryService.store(memoryData);
                case memory_types_1.MemoryType.LONG_TERM:
                    return await this.longTermMemoryService.store(memoryData);
                default:
                    return await this.shortTermMemoryService.store(memoryData);
            }
        }
        catch (error) {
            this.logger.error(`存储记忆失败: ${error.message}`);
            throw error;
        }
    }
    async retrieveMemory(query) {
        this.logger.debug(`检索记忆 - 查询: ${JSON.stringify(query)}`);
        try {
            const results = [];
            if (query.memoryType) {
                switch (query.memoryType) {
                    case memory_types_1.MemoryType.SHORT_TERM:
                        results.push(...await this.shortTermMemoryService.retrieve(query));
                        break;
                    case memory_types_1.MemoryType.MEDIUM_TERM:
                        results.push(...await this.mediumTermMemoryService.retrieve(query));
                        break;
                    case memory_types_1.MemoryType.LONG_TERM:
                        results.push(...await this.longTermMemoryService.retrieve(query));
                        break;
                }
            }
            else {
                const [shortResults, mediumResults, longResults] = await Promise.all([
                    this.shortTermMemoryService.retrieve(query),
                    this.mediumTermMemoryService.retrieve(query),
                    this.longTermMemoryService.retrieve(query),
                ]);
                results.push(...longResults);
                results.push(...mediumResults);
                results.push(...shortResults);
            }
            results.sort((a, b) => {
                if (a.importance !== b.importance) {
                    return b.importance - a.importance;
                }
                return b.createdAt.getTime() - a.createdAt.getTime();
            });
            const limited = query.limit
                ? results.slice(query.offset || 0, (query.offset || 0) + query.limit)
                : results;
            return limited;
        }
        catch (error) {
            this.logger.error(`检索记忆失败: ${error.message}`);
            throw error;
        }
    }
    async getContextualMemories(agentType, userId, contextQuery, maxResults = 20) {
        this.logger.debug(`获取上下文记忆 - Agent: ${agentType}, 用户: ${userId}`);
        try {
            const query = {
                agentType,
                userId,
                searchText: contextQuery,
                limit: maxResults,
                similarityThreshold: 0.7,
            };
            const memories = await this.retrieveMemory(query);
            return memories.slice(0, maxResults);
        }
        catch (error) {
            this.logger.error(`获取上下文记忆失败: ${error.message}`);
            throw error;
        }
    }
    async updateMemory(memoryId, updates) {
        this.logger.debug(`更新记忆 - ID: ${memoryId}`);
        try {
            let memory = await this.shortTermMemoryService.findById(memoryId);
            if (memory) {
                return await this.shortTermMemoryService.update(memoryId, updates);
            }
            memory = await this.mediumTermMemoryService.findById(memoryId);
            if (memory) {
                return await this.mediumTermMemoryService.update(memoryId, updates);
            }
            memory = await this.longTermMemoryService.findById(memoryId);
            if (memory) {
                return await this.longTermMemoryService.update(memoryId, updates);
            }
            throw new Error(`记忆未找到: ${memoryId}`);
        }
        catch (error) {
            this.logger.error(`更新记忆失败: ${error.message}`);
            throw error;
        }
    }
    async deleteMemory(memoryId) {
        this.logger.debug(`删除记忆 - ID: ${memoryId}`);
        try {
            let deleted = await this.shortTermMemoryService.delete(memoryId);
            if (deleted)
                return true;
            deleted = await this.mediumTermMemoryService.delete(memoryId);
            if (deleted)
                return true;
            deleted = await this.longTermMemoryService.delete(memoryId);
            return deleted;
        }
        catch (error) {
            this.logger.error(`删除记忆失败: ${error.message}`);
            return false;
        }
    }
    async getMemoryStats(agentType, userId) {
        try {
            const [shortStats, mediumStats, longStats] = await Promise.all([
                this.shortTermMemoryService.getStats(agentType, userId),
                this.mediumTermMemoryService.getStats(agentType, userId),
                this.longTermMemoryService.getStats(agentType, userId),
            ]);
            return {
                shortTerm: shortStats,
                mediumTerm: mediumStats,
                longTerm: longStats,
                total: {
                    memories: shortStats.total + mediumStats.total + longStats.total,
                    storageUsed: shortStats.storageUsed + mediumStats.storageUsed + longStats.storageUsed,
                    averageImportance: (shortStats.averageImportance +
                        mediumStats.averageImportance +
                        longStats.averageImportance) / 3,
                },
            };
        }
        catch (error) {
            this.logger.error(`获取统计信息失败: ${error.message}`);
            throw error;
        }
    }
    async generateUserInsights(agentType, userId) {
        try {
            return await this.analyticsService.generateInsights(agentType, userId);
        }
        catch (error) {
            this.logger.error(`生成用户洞察失败: ${error.message}`);
            throw error;
        }
    }
    async triggerConsolidation() {
        try {
            return await this.consolidationService.performConsolidation();
        }
        catch (error) {
            this.logger.error(`手动巩固失败: ${error.message}`);
            throw error;
        }
    }
    async cleanupExpiredMemories() {
        try {
            const [shortCleaned, mediumCleaned, longCleaned] = await Promise.all([
                this.shortTermMemoryService.cleanupExpiredMemories(),
                this.mediumTermMemoryService.cleanupLowImportanceMemories(),
                this.longTermMemoryService.archiveOldMemories(),
            ]);
            return {
                shortTerm: shortCleaned,
                mediumTerm: mediumCleaned,
                longTerm: longCleaned,
            };
        }
        catch (error) {
            this.logger.error(`清理过期记忆失败: ${error.message}`);
            throw error;
        }
    }
    async healthCheck() {
        try {
            const services = {
                shortTermMemory: true,
                mediumTermMemory: true,
                longTermMemory: true,
                consolidation: true,
                analytics: true,
            };
            const status = Object.values(services).every(s => s) ? 'healthy' : 'degraded';
            return { status, services };
        }
        catch (error) {
            this.logger.error(`健康检查失败: ${error.message}`);
            return {
                status: 'unhealthy',
                services: {
                    shortTermMemory: false,
                    mediumTermMemory: false,
                    longTermMemory: false,
                    consolidation: false,
                    analytics: false,
                },
            };
        }
    }
};
exports.MemoryManagerService = MemoryManagerService;
exports.MemoryManagerService = MemoryManagerService = MemoryManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [short_term_memory_service_1.ShortTermMemoryService,
        medium_term_memory_service_1.MediumTermMemoryService,
        long_term_memory_service_1.LongTermMemoryService,
        memory_consolidation_service_1.MemoryConsolidationService,
        memory_analytics_service_1.MemoryAnalyticsService])
], MemoryManagerService);
//# sourceMappingURL=memory-manager.service.js.map