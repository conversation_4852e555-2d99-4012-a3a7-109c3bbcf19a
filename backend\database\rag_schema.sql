-- RAG知识库服务数据库模式
-- 支持向量存储和相似度检索

-- 创建pgvector扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建知识库表
CREATE TABLE IF NOT EXISTS knowledge_bases (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    agent_type VARCHAR(50) NOT NULL, -- 'almighty', 'taoist', 'freud', 'papa', 'shared'
    is_public BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建知识条目表
CREATE TABLE IF NOT EXISTS knowledge_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    knowledge_base_id UUID REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    title VARCHAR(500),
    content TEXT NOT NULL,
    content_type VARCHAR(50) DEFAULT 'text', -- 'text', 'markdown', 'json'
    source_url VARCHAR(1000),
    metadata JSONB DEFAULT '{}',
    embedding vector(1536), -- OpenAI ada-002 embedding dimension
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建知识库访问权限表
CREATE TABLE IF NOT EXISTS knowledge_base_permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    knowledge_base_id UUID REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    agent_type VARCHAR(50) NOT NULL,
    permission_level VARCHAR(20) DEFAULT 'read' CHECK (permission_level IN ('read', 'write', 'admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建Agent知识库使用记录表
CREATE TABLE IF NOT EXISTS knowledge_usage_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    agent_type VARCHAR(50) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    knowledge_entry_id UUID REFERENCES knowledge_entries(id) ON DELETE CASCADE,
    query_text TEXT,
    similarity_score FLOAT,
    usage_context VARCHAR(100), -- 'conversation', 'dream_analysis', 'guidance'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建向量检索相关索引
CREATE INDEX IF NOT EXISTS idx_knowledge_entries_embedding 
    ON knowledge_entries USING ivfflat (embedding vector_cosine_ops);

-- 创建其他索引
CREATE INDEX IF NOT EXISTS idx_knowledge_bases_agent_type ON knowledge_bases(agent_type);
CREATE INDEX IF NOT EXISTS idx_knowledge_bases_public ON knowledge_bases(is_public);
CREATE INDEX IF NOT EXISTS idx_knowledge_entries_kb_id ON knowledge_entries(knowledge_base_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_entries_content_type ON knowledge_entries(content_type);
CREATE INDEX IF NOT EXISTS idx_knowledge_permissions_kb_id ON knowledge_base_permissions(knowledge_base_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_permissions_agent ON knowledge_base_permissions(agent_type);
CREATE INDEX IF NOT EXISTS idx_knowledge_usage_agent ON knowledge_usage_logs(agent_type);
CREATE INDEX IF NOT EXISTS idx_knowledge_usage_user ON knowledge_usage_logs(user_id);

-- 创建更新时间触发器
CREATE TRIGGER update_knowledge_bases_updated_at 
    BEFORE UPDATE ON knowledge_bases 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_knowledge_entries_updated_at 
    BEFORE UPDATE ON knowledge_entries 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) 策略

-- 启用RLS
ALTER TABLE knowledge_bases ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_base_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_usage_logs ENABLE ROW LEVEL SECURITY;

-- 知识库RLS策略
CREATE POLICY "公共知识库可被所有人查看" ON knowledge_bases
    FOR SELECT USING (is_public = TRUE);

CREATE POLICY "用户可查看自己创建的知识库" ON knowledge_bases
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "用户可管理自己创建的知识库" ON knowledge_bases
    FOR ALL USING (created_by = auth.uid());

-- 知识条目RLS策略
CREATE POLICY "可查看公共知识库的条目" ON knowledge_entries
    FOR SELECT USING (
        knowledge_base_id IN (
            SELECT id FROM knowledge_bases WHERE is_public = TRUE
        )
    );

CREATE POLICY "可查看自己知识库的条目" ON knowledge_entries
    FOR SELECT USING (
        knowledge_base_id IN (
            SELECT id FROM knowledge_bases WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "可管理自己知识库的条目" ON knowledge_entries
    FOR ALL USING (
        knowledge_base_id IN (
            SELECT id FROM knowledge_bases WHERE created_by = auth.uid()
        )
    );

-- 权限表RLS策略
CREATE POLICY "可查看相关知识库权限" ON knowledge_base_permissions
    FOR SELECT USING (
        knowledge_base_id IN (
            SELECT id FROM knowledge_bases 
            WHERE created_by = auth.uid() OR is_public = TRUE
        )
    );

-- 使用记录RLS策略
CREATE POLICY "用户只能查看自己的使用记录" ON knowledge_usage_logs
    FOR SELECT USING (user_id = auth.uid());

-- 创建向量相似度搜索函数
CREATE OR REPLACE FUNCTION search_knowledge_entries(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    filter_agent_type text DEFAULT NULL
)
RETURNS TABLE(
    id uuid,
    title varchar,
    content text,
    metadata jsonb,
    similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ke.id,
        ke.title,
        ke.content,
        ke.metadata,
        1 - (ke.embedding <=> query_embedding) AS similarity
    FROM knowledge_entries ke
    JOIN knowledge_bases kb ON ke.knowledge_base_id = kb.id
    WHERE 
        1 - (ke.embedding <=> query_embedding) > match_threshold
        AND (filter_agent_type IS NULL OR kb.agent_type = filter_agent_type OR kb.agent_type = 'shared')
        AND (kb.is_public = TRUE OR kb.created_by = auth.uid())
    ORDER BY ke.embedding <=> query_embedding
    LIMIT match_count;
END;
$$; 