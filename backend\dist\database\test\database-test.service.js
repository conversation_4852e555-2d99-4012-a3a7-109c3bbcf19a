"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DatabaseTestService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseTestService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../database.service");
const user_data_access_service_1 = require("../services/user-data-access.service");
const dream_data_access_service_1 = require("../services/dream-data-access.service");
const agent_data_access_service_1 = require("../services/agent-data-access.service");
const conversation_data_access_service_1 = require("../services/conversation-data-access.service");
let DatabaseTestService = DatabaseTestService_1 = class DatabaseTestService {
    constructor(databaseService, userDataAccess, dreamDataAccess, agentDataAccess, conversationDataAccess) {
        this.databaseService = databaseService;
        this.userDataAccess = userDataAccess;
        this.dreamDataAccess = dreamDataAccess;
        this.agentDataAccess = agentDataAccess;
        this.conversationDataAccess = conversationDataAccess;
        this.logger = new common_1.Logger(DatabaseTestService_1.name);
    }
    async runDatabaseTests() {
        const results = [];
        const errors = [];
        this.logger.log('🧪 开始数据库功能测试...');
        try {
            const connectionTest = await this.testDatabaseConnection();
            results.push(connectionTest);
            const permissionTest = await this.testAgentPermissions();
            results.push(permissionTest);
            const userTest = await this.testUserOperations();
            results.push(userTest);
            const agentConfigTest = await this.testAgentConfiguration();
            results.push(agentConfigTest);
            const successfulTests = results.filter(r => r.success).length;
            const totalTests = results.length;
            this.logger.log(`✅ 测试完成: ${successfulTests}/${totalTests} 通过`);
            return {
                success: successfulTests === totalTests,
                results,
                errors,
            };
        }
        catch (error) {
            this.logger.error(`❌ 测试运行失败: ${error.message}`);
            errors.push(error);
            return { success: false, results, errors };
        }
    }
    async testDatabaseConnection() {
        try {
            const isConnected = await this.databaseService.healthCheck();
            return {
                name: '数据库连接测试',
                success: isConnected,
                message: isConnected ? '数据库连接成功' : '数据库连接失败',
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            return {
                name: '数据库连接测试',
                success: false,
                message: `连接测试失败: ${error.message}`,
                timestamp: new Date().toISOString(),
            };
        }
    }
    async testAgentPermissions() {
        try {
            const hasUserPermission = await this.databaseService.checkAgentPermission('almighty', 'users', 'read');
            const hasDreamPermission = await this.databaseService.checkAgentPermission('taoist', 'dreams', 'read');
            const hasInvalidPermission = await this.databaseService.checkAgentPermission('taoist', 'users', 'delete');
            const success = hasUserPermission && hasDreamPermission && !hasInvalidPermission;
            return {
                name: 'Agent权限测试',
                success,
                message: success ? 'Agent权限检查正常' : 'Agent权限检查异常',
                details: {
                    almighty_user_read: hasUserPermission,
                    taoist_dream_read: hasDreamPermission,
                    taoist_user_delete: hasInvalidPermission,
                },
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            return {
                name: 'Agent权限测试',
                success: false,
                message: `权限测试失败: ${error.message}`,
                timestamp: new Date().toISOString(),
            };
        }
    }
    async testUserOperations() {
        try {
            const testWallet = `test_wallet_${Date.now()}`;
            const { user, error: createError } = await this.userDataAccess.createUser(testWallet, 'test_user');
            if (createError || !user) {
                return {
                    name: '用户数据操作测试',
                    success: false,
                    message: `创建用户失败: ${createError?.message || '未知错误'}`,
                    timestamp: new Date().toISOString(),
                };
            }
            const foundUser = await this.userDataAccess.findUserByWallet(testWallet);
            const { success: updateSuccess } = await this.userDataAccess.updateLastLogin(user.id);
            const success = foundUser && foundUser.id === user.id && updateSuccess;
            return {
                name: '用户数据操作测试',
                success,
                message: success ? '用户数据操作正常' : '用户数据操作异常',
                details: {
                    user_created: !!user,
                    user_found: !!foundUser,
                    login_updated: updateSuccess,
                    test_wallet: testWallet,
                },
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            return {
                name: '用户数据操作测试',
                success: false,
                message: `用户操作测试失败: ${error.message}`,
                timestamp: new Date().toISOString(),
            };
        }
    }
    async testAgentConfiguration() {
        try {
            const agentTypes = await this.agentDataAccess.getAllAgentTypes();
            const almightyConfig = await this.agentDataAccess.getAgentConfig('almighty');
            const taoistConfig = await this.agentDataAccess.getAgentConfig('taoist');
            const success = agentTypes.length > 0 &&
                almightyConfig.persona &&
                taoistConfig.persona &&
                !almightyConfig.error &&
                !taoistConfig.error;
            return {
                name: 'Agent配置测试',
                success,
                message: success ? 'Agent配置获取正常' : 'Agent配置获取异常',
                details: {
                    total_agent_types: agentTypes.length,
                    agent_names: agentTypes.map(a => a.name),
                    almighty_config_loaded: !!almightyConfig.persona,
                    taoist_config_loaded: !!taoistConfig.persona,
                },
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            return {
                name: 'Agent配置测试',
                success: false,
                message: `Agent配置测试失败: ${error.message}`,
                timestamp: new Date().toISOString(),
            };
        }
    }
    async getDatabaseStats() {
        try {
            const agentTypes = await this.agentDataAccess.getAllAgentTypes();
            return {
                database: {
                    status: 'connected',
                    provider: 'supabase',
                },
                agents: {
                    total_types: agentTypes.length,
                    available_agents: agentTypes.map(a => ({
                        name: a.name,
                        display_name: a.display_name,
                        description: a.description,
                    })),
                },
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error(`获取数据库统计失败: ${error.message}`);
            return {
                database: {
                    status: 'error',
                    error: error.message,
                },
                timestamp: new Date().toISOString(),
            };
        }
    }
};
exports.DatabaseTestService = DatabaseTestService;
exports.DatabaseTestService = DatabaseTestService = DatabaseTestService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService,
        user_data_access_service_1.UserDataAccessService,
        dream_data_access_service_1.DreamDataAccessService,
        agent_data_access_service_1.AgentDataAccessService,
        conversation_data_access_service_1.ConversationDataAccessService])
], DatabaseTestService);
//# sourceMappingURL=database-test.service.js.map