# Donny 技术栈文档

## 1. 概述

本文档详细描述了 Donny Web3 解梦平台的完整技术栈架构，包括前端、后端、区块链和基础设施等各技术层面的选型和版本要求。技术选型基于项目的功能需求、性能指标和可维护性综合考虑，为开发团队提供明确的技术实施指南。

## 2. 前端技术栈

### 2.1 核心框架与库

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| React | 18+ | 用户界面构建 | 组件化开发、虚拟 DOM 高效渲染、丰富的生态系统 |
| TypeScript | 4.9+ | 类型系统 | 提供静态类型检查，增强代码健壮性和可维护性 |
| Redux Toolkit | 1.9+ | 状态管理 | 简化 Redux 样板代码，内置 Immer 简化不可变更新 |
| React Router | 6.8+ | 客户端路由 | 声明式路由管理，支持嵌套路由和代码分割 |
| @supabase/supabase-js | 2.x+ | Supabase客户端 | 提供Supabase实时订阅功能，用于实时交易状态更新 |

### 2.2 UI 与样式

| 技术              | 版本   | 用途                                      | 选择理由                                                 |
|-------------------|--------|-------------------------------------------|----------------------------------------------------------|
| Styled Components | 5.3+   | CSS-in-JS 解决方案                        | 组件级样式隔离，主题支持，动态样式生成                     |
| Ant Design        | 5.x+   | 企业级 UI 设计语言和 React UI 库          | 提供丰富的高质量组件，加速开发效率，统一视觉风格           |
| Framer Motion     | 10+    | 动画效果                                  | 声明式 API，性能优化，手势支持                           |

### 2.3 3D 及可视化

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Three.js | 0.150+ | 3D 渲染引擎 | WebGL 抽象层，广泛应用的 3D 库 |
| React Three Fiber | 8.12+ | React 绑定的 Three.js | 组件化 3D 开发，声明式 API |
| R3F Drei | 9.56+ | Three.js/R3F 工具集 | 提供常用 3D 组件和辅助功能 |

### 2.4 Web3 集成

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| @solana/web3.js | 1.73+ | Solana 区块链交互 | Solana 官方 JavaScript API |
| @solana/wallet-adapter | 0.9+ | 钱包连接适配器 | 标准化钱包接口，支持多种钱包 |
| @metaplex-foundation/js | 0.18+ | NFT 相关操作 | 简化 Solana NFT 创建和管理 |

### 2.5 工具与优化

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Vite | 4.1+ | 构建工具 | 快速的开发服务器，优化的构建性能 |
| ESLint | 8.34+ | 代码规范检查 | 可定制的代码质量保证 |
| Prettier | 2.8+ | 代码格式化 | 统一代码风格 |
| React Query | 3.39+ / TanStack Query 4+ | 数据获取与缓存 | 简化 API 数据获取，自动缓存管理 |

## 3. 后端技术栈

### 3.1 API 服务

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Node.js | 18+ LTS | 运行环境 | 非阻塞 I/O，适合高并发应用 |
| Express | 4.18+ | Web 框架 | 轻量灵活，生态丰富 |
| NestJS | 9.3+ | 企业级框架 | TypeScript 优先，模块化架构，依赖注入 |
| GraphQL | 16+ | API 查询语言 | 按需获取数据，减少网络负载 |
| Apollo Server | 3.11+ | GraphQL 服务器 | 功能全面的 GraphQL 服务实现 |

### 3.2 数据存储

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Supabase | 2.x+ | 后端即服务平台 | 基于PostgreSQL的全栈开发平台，提供数据库、认证、实时订阅、存储等服务 |
| Supabase PostgreSQL | 15+ | 关系型数据库 | 强大的查询能力，事务支持，可靠性高，作为Supabase核心数据库引擎 |
| pgvector | 最新版 | 向量扩展 | 为PostgreSQL提供向量存储和相似性搜索功能，支持RAG实现 |
| Supabase Storage | 最新版 | 文件存储 | 集成的文件存储服务，用于存储用户语音文件 |
| Supabase Auth | 最新版 | 认证服务 | 集成的身份验证系统，支持钱包连接 |
| Redis | 7.0+ | 缓存与会话存储 | 高性能内存数据库，用于短期记忆缓存 |
| PostgreSQL Schemas | - | 数据隔离 | 利用Schema实现不同Agent数据访问隔离 |
| Row Level Security (RLS) | - | 行级安全策略 | 控制不同Agent对数据的访问权限 |

### 3.3 微服务与 API 网关

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| gRPC | 1.52+ | 服务间通信 | 高性能 RPC 框架，支持流式传输 |
| Kong | 3.2+ | API 网关 | 功能丰富的开源 API 网关，插件生态完善 |
| Docker | 23+ | 容器化 | 标准化部署环境，简化微服务部署 |
| Kubernetes | 1.26+ | 容器编排 | 自动扩展，服务发现，负载均衡 |

### 3.4 AI 与语音服务

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| elizaos.ai API | 按合作协议 | AI 对话引擎 | 梦境解析核心能力提供者 |
| WebSpeech API | 浏览器标准 | 前端语音识别 | 内置浏览器 API，无需第三方依赖 |
| DeepSpeech | 0.9+ | 服务端语音识别 | 开源自托管方案，降低 API 依赖 |

### 3.5 消息队列与事件驱动

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| RabbitMQ | 3.11+ | 消息队列 | 可靠的消息传递，多种交换模式 |
| Kafka | 3.4+ | 分布式事件平台 | 高吞吐量，持久化日志，适合事件溯源 |
| Bull | 4.10+ | Node.js 任务队列 | Redis 支持的任务队列，优先级和重试机制 |

## 4. 区块链技术栈

### 4.1 基础设施

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Solana | Mainnet/Testnet/Devnet | 区块链平台 | 高性能、低手续费的区块链 |
| Anchor | 0.27+ | Solana 智能合约框架 | 简化合约开发流程，提供安全保障 |
| SPL Token | 标准 | 代币标准 | Solana 生态的标准代币接口 |
| Metaplex | 标准 | NFT 标准 | Solana 生态的 NFT 接口标准 |

### 4.2 开发工具

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Solana CLI | 1.14+ | Solana 命令行工具 | 网络交互、部署和调试 |
| Rust | 1.68+ | 智能合约开发语言 | Solana 合约的首选语言 |
| Solana Web3.js | 1.73+ | JavaScript 客户端 | 前端与区块链交互 |
| Phantom Wallet | 最新版 | 用户钱包 | Solana 生态流行钱包，用户群体大 |

### 4.3 监控与分析

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Solana Explorer | - | 区块链浏览器 | 监控交易和合约状态 |
| Solscan | - | 区块链浏览器 | 更详细的交易和账户分析 |
| Dune Analytics | - | 数据分析平台 | 自定义区块链数据分析 |

## 5. DevOps 与基础设施

### 5.1 部署与编排

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Docker | 23+ | 容器化 | 环境一致性，隔离性 |
| Docker Compose | 2.16+ | 本地容器编排 | 简化多容器应用的开发和测试 |
| Kubernetes | 1.26+ | 生产容器编排 | 自动扩展，故障恢复，滚动更新 |
| Helm | 3.11+ | K8s 包管理器 | 简化 K8s 应用部署和管理 |

### 5.2 CI/CD

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| GitHub Actions | - | CI/CD 流水线 | 与 GitHub 深度集成，配置简单 |
| Jenkins | 2.387+ | CI/CD 服务器 | 成熟稳定，插件丰富 |
| ArgoCD | 2.6+ | GitOps 工具 | 声明式 K8s 应用部署和管理 |

### 5.3 监控与日志

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Prometheus | 2.42+ | 监控系统 | 时序数据库，强大的查询语言 |
| Grafana | 9.4+ | 监控可视化 | 丰富的图表和仪表盘 |
| ELK Stack | 8.6+ | 日志管理 | 全功能日志收集、搜索和分析 |
| Sentry | 最新版 | 错误跟踪 | 实时错误监控，详细错误上下文 |

### 5.4 云服务提供商

| 技术 | 用途 | 选择理由 |
|------|------|---------|
| AWS | 主要云服务提供商 | 全球覆盖，服务丰富 |
| Vercel | 前端部署 | 针对 React 优化的部署平台 |
| Cloudflare | CDN 和安全 | 全球 CDN，DDoS 防护 |

## 6. 测试技术栈

### 6.1 前端测试

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Jest | 29+ | 单元测试框架 | 零配置，快照测试，模拟功能 |
| React Testing Library | 14+ | 组件测试 | 以用户行为为中心的测试方法 |
| Cypress | 12+ | 端到端测试 | 浏览器中运行，可视化调试 |
| Mock Service Worker | 1.2+ | API 模拟 | 拦截网络请求，模拟 API 响应 |

### 6.2 后端测试

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Mocha | 10+ | 测试框架 | 灵活的测试框架 |
| Chai | 4.3+ | 断言库 | 表达性强的断言语法 |
| Supertest | 6.3+ | API 测试 | HTTP 断言测试 |
| Sinon | 15+ | 测试替身 | Spy, Stub, Mock 功能 |

### 6.3 区块链测试

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Anchor Test | 0.27+ | Anchor 程序测试 | 随 Anchor 框架提供的测试工具 |
| Solana Test Validator | - | 本地测试网络 | 测试区块链交互 |

### 6.4 性能测试

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|---------|
| k6 | 0.43+ | 负载测试 | 可编程负载测试工具 |
| Lighthouse | 最新版 | 前端性能测试 | 网页性能评分和优化建议 |
| Artillery | 2.0+ | API 负载测试 | 易用的分布式负载测试 |

## 7. 开发工具

### 7.1 IDE 与编辑器

| 工具 | 推荐版本 | 用途 | 选择理由 |
|------|------|------|---------|
| Visual Studio Code | 最新版 | 主要代码编辑器 | 轻量、扩展丰富、多语言支持 |
| WebStorm | 最新版 | JavaScript/TypeScript IDE | 强大的重构和导航功能 |
| Rust Analyzer | 最新版 | Rust 语言支持 | 智能代码补全和分析 |

### 7.2 协作与文档

| 工具 | 用途 | 选择理由 |
|------|------|---------|
| GitHub | 代码仓库 | 开发者熟悉度高，工作流工具丰富 |
| Confluence | 文档管理 | 结构化文档，版本控制 |
| Jira | 项目管理 | 任务分配，进度跟踪 |
| Figma | UI/UX 设计 | 协作设计，原型制作 |

### 7.3 API 开发

| 工具 | 用途 | 选择理由 |
|------|------|---------|
| Postman | API 测试 | 功能丰富的 API 开发环境 |
| Swagger | API 文档 | 标准化 API 文档生成与测试 |
| Insomnia | API 客户端 | 轻量级 API 测试工具 |

## 8. 安全工具

| 工具 | 用途 | 选择理由 |
|------|------|---------|
| OWASP ZAP | 安全扫描 | 开源 Web 应用安全扫描器 |
| SonarQube | 代码质量与安全 | 静态代码分析，安全规则检查 |
| npm audit | 依赖安全检查 | Node.js 包安全分析 |
| Anchor Security | Solana 合约安全 | Anchor 程序安全检查工具 |

## 9. 技术栈组合与应用

以下是项目各主要模块的技术栈组合推荐：

### 9.1 前端应用

- **主框架**: React + TypeScript + Vite
- **状态管理**: Redux Toolkit + React Query
- **UI 层**: Styled Components + Ant Design
- **3D 渲染**: Three.js + React Three Fiber
- **Web3 集成**: @solana/web3.js + @solana/wallet-adapter
- **测试**: Jest + RTL + Cypress

### 9.2 后端服务群

- **API 网关**: Kong + Express
- **用户服务**: NestJS + Supabase (PostgreSQL)
- **AI 服务**: Node.js + elizaos.ai API
- **STT 服务**: Node.js + DeepSpeech
- **Solana 交互服务**: Node.js + @solana/web3.js + Supabase实时订阅
- **记忆管理服务**: Node.js + Supabase (pgvector) + Redis
- **无服务器函数**: Supabase Edge Functions
- **消息处理**: Kafka + Bull
- **对话数据服务**: NestJS + Supabase (PostgreSQL JSONB)

### 9.3 智能合约

- **开发框架**: Rust + Anchor
- **标准**: SPL Token + Metaplex
- **测试**: Anchor Test + Solana Test Validator
- **监控**: Solana Explorer + Solscan

### 9.4 DevOps 流水线

- **构建**: GitHub Actions
- **容器化**: Docker + Kubernetes
- **部署**: ArgoCD + Helm
- **监控**: Prometheus + Grafana + ELK
- **安全检查**: SonarQube + OWASP ZAP

## 10. 版本兼容性与升级计划

### 10.1 版本锁定策略

- 开发依赖使用精确版本 (exact version)
- 核心库使用兼容版本范围 (^)
- 使用 package-lock.json 或 yarn.lock 锁定依赖树

### 10.2 升级评估流程

- 每季度评估核心依赖的升级需求
- 主要版本升级前进行全面兼容性测试
- 建立依赖升级检查清单和回滚计划

### 10.3 长期技术规划

- 评估 React Server Components 在项目中的应用
- 关注 Solana 生态系统更新和新标准
- 持续优化前端性能和移动设备兼容性

## 11. 技术决策说明

### 11.1 React vs Vue vs Angular

React 被选为前端框架，主要考虑因素：
- 社区规模和活跃度高，解决问题资源丰富
- 与 Web3 生态系统的集成库更完善
- 团队已有 React 技术积累，降低学习成本
- 组件抽象和代码复用模型符合项目需求

### 11.2 PostgreSQL (Supabase) 选择理由

数据存储选择Supabase PostgreSQL，考虑因素：
- 用户数据和交易记录需要事务和关系完整性保障
- 使用PostgreSQL的JSONB类型高效存储和查询半结构化数据，如对话历史和解梦内容
- 通过Supabase实时订阅功能，实现区块链交易状态的实时更新
- pgvector扩展提供强大的向量搜索能力，支持知识检索和用户认知分析
- Schema隔离和Row Level Security实现不同Agent数据访问的安全隔离
- 单一数据库简化架构，减少跨数据库同步复杂性
- Supabase提供完整的后端即服务方案，包括数据库、存储、认证等服务

### 11.3 Solana vs Ethereum

区块链平台选择 Solana，考虑因素：
- 低交易费用，提高用户接受度
- 高吞吐量，支持更流畅的用户体验
- NFT 标准成熟且操作成本低
- 用户群体年轻化，符合目标受众画像

### 11.4 微服务架构 vs 单体应用

后端采用微服务架构，考虑因素：
- 各功能模块（AI、STT、区块链交互）技术栈差异大
- 服务可独立扩展，优化资源利用
- 团队可并行开发，提高开发效率
- 容错性更高，单个服务故障不影响整体系统