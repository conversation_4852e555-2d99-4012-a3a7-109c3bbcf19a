import { BaseMemory, MemoryQuery } from '../memory.types';
import { AgentType } from '../../database/types/rag.types';
import { ShortTermMemoryService } from './short-term-memory.service';
import { MediumTermMemoryService } from './medium-term-memory.service';
import { LongTermMemoryService } from './long-term-memory.service';
import { MemoryConsolidationService } from './memory-consolidation.service';
import { MemoryAnalyticsService } from './memory-analytics.service';
export declare class MemoryManagerService {
    private readonly shortTermMemoryService;
    private readonly mediumTermMemoryService;
    private readonly longTermMemoryService;
    private readonly consolidationService;
    private readonly analyticsService;
    private readonly logger;
    constructor(shortTermMemoryService: ShortTermMemoryService, mediumTermMemoryService: MediumTermMemoryService, longTermMemoryService: LongTermMemoryService, consolidationService: MemoryConsolidationService, analyticsService: MemoryAnalyticsService);
    storeMemory(memoryData: Partial<BaseMemory>): Promise<BaseMemory>;
    retrieveMemory(query: MemoryQuery): Promise<BaseMemory[]>;
    getContextualMemories(agentType: AgentType, userId: string, contextQuery: string, maxResults?: number): Promise<BaseMemory[]>;
    updateMemory(memoryId: string, updates: Partial<BaseMemory>): Promise<BaseMemory>;
    deleteMemory(memoryId: string): Promise<boolean>;
    getMemoryStats(agentType?: AgentType, userId?: string): Promise<{
        shortTerm: any;
        mediumTerm: any;
        longTerm: any;
        total: {
            memories: number;
            storageUsed: number;
            averageImportance: number;
        };
    }>;
    generateUserInsights(agentType: AgentType, userId: string): Promise<any>;
    triggerConsolidation(): Promise<{
        shortToMedium: number;
        mediumToLong: number;
    }>;
    cleanupExpiredMemories(): Promise<{
        shortTerm: number;
        mediumTerm: number;
        longTerm: number;
    }>;
    healthCheck(): Promise<{
        status: string;
        services: Record<string, boolean>;
    }>;
}
