const { createClient } = require('@supabase/supabase-js');

// 从环境变量获取Supabase配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ 缺少Supabase配置：SUPABASE_URL 或 SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
}

console.log('🔗 连接配置:');
console.log('URL:', supabaseUrl);
console.log('Service Key:', supabaseServiceKey.substring(0, 20) + '...');

// 创建Supabase客户端
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testConnection() {
    try {
        console.log('🧪 测试基础连接...');
        
        // 测试基础RPC调用
        const { data, error } = await supabase.rpc('get_schema_version');
        
        if (error && error.code !== 'PGRST202') {
            console.log('⚠️ RPC函数不存在，这是正常的。尝试其他方式...');
        }
        
        // 尝试获取版本信息
        const { data: versionData, error: versionError } = await supabase
            .rpc('version');
            
        if (versionError) {
            console.log('⚠️ 版本查询失败，尝试直接创建表...');
        }
        
        console.log('✅ Supabase连接成功！');
        console.log('📊 现在可以创建数据库结构');
        
        return true;
        
    } catch (error) {
        console.error('❌ 连接测试失败:', error.message);
        return false;
    }
}

async function createSimpleTable() {
    try {
        console.log('📝 尝试创建测试表...');
        
        // 执行简单的SQL创建测试表
        const { data, error } = await supabase.rpc('create_test_table');
        
        if (error) {
            console.log('ℹ️ 无法通过RPC创建表，需要手动在Supabase控制台执行SQL');
            console.log('🔗 请访问: https://supabase.com/dashboard/project/fwvtrliiyzvpaxqkyjef/sql');
            console.log('📄 执行 complete_schema.sql 和 initial_data.sql 文件内容');
        } else {
            console.log('✅ 测试表创建成功');
        }
        
    } catch (error) {
        console.log('ℹ️ 预期的错误，需要手动执行SQL');
    }
}

testConnection().then(() => createSimpleTable()); 