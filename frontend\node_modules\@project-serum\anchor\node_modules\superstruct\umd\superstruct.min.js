!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).Superstruct={})}(this,(function(e){"use strict";class t extends TypeError{constructor(e,t){let n;const{message:r,...i}=e,{path:o}=e;super(0===o.length?r:"At path: "+o.join(".")+" -- "+r),this.value=void 0,this.key=void 0,this.type=void 0,this.refinement=void 0,this.path=void 0,this.branch=void 0,this.failures=void 0,Object.assign(this,i),this.name=this.constructor.name,this.failures=()=>{var r;return null!=(r=n)?r:n=[e,...t()]}}}function n(e){return"object"==typeof e&&null!=e}function r(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function i(e){return"string"==typeof e?JSON.stringify(e):""+e}function o(e,t,n,r){if(!0===e)return;!1===e?e={}:"string"==typeof e&&(e={message:e});const{path:o,branch:c}=t,{type:a}=n,{refinement:s,message:u="Expected a value of type `"+a+"`"+(s?" with refinement `"+s+"`":"")+", but received: `"+i(r)+"`"}=e;return{value:r,type:a,refinement:s,key:o[o.length-1],path:o,branch:c,...e,message:u}}function*c(e,t,r,i){var c;n(c=e)&&"function"==typeof c[Symbol.iterator]||(e=[e]);for(const n of e){const e=o(n,t,r,i);e&&(yield e)}}function*a(e,t,r){void 0===r&&(r={});const{path:i=[],branch:o=[e],coerce:c=!1,mask:s=!1}=r,u={path:i,branch:o};if(c&&(e=t.coercer(e,u),s&&"type"!==t.type&&n(t.schema)&&n(e)&&!Array.isArray(e)))for(const n in e)void 0===t.schema[n]&&delete e[n];let f=!0;for(const n of t.validator(e,u))f=!1,yield[n,void 0];for(let[r,d,l]of t.entries(e,u)){const t=a(d,l,{path:void 0===r?i:[...i,r],branch:void 0===r?o:[...o,d],coerce:c,mask:s});for(const i of t)i[0]?(f=!1,yield[i[0],void 0]):c&&(d=i[1],void 0===r?e=d:e instanceof Map?e.set(r,d):e instanceof Set?e.add(d):n(e)&&(e[r]=d))}if(f)for(const n of t.refiner(e,u))f=!1,yield[n,void 0];f&&(yield[void 0,e])}class s{constructor(e){this.TYPE=void 0,this.type=void 0,this.schema=void 0,this.coercer=void 0,this.validator=void 0,this.refiner=void 0,this.entries=void 0;const{type:t,schema:n,validator:r,refiner:i,coercer:o=(e=>e),entries:a=function*(){}}=e;this.type=t,this.schema=n,this.entries=a,this.coercer=o,this.validator=r?(e,t)=>c(r(e,t),t,this,e):()=>[],this.refiner=i?(e,t)=>c(i(e,t),t,this,e):()=>[]}assert(e){return u(e,this)}create(e){return f(e,this)}is(e){return l(e,this)}mask(e){return d(e,this)}validate(e,t){return void 0===t&&(t={}),p(e,this,t)}}function u(e,t){const n=p(e,t);if(n[0])throw n[0]}function f(e,t){const n=p(e,t,{coerce:!0});if(n[0])throw n[0];return n[1]}function d(e,t){const n=p(e,t,{coerce:!0,mask:!0});if(n[0])throw n[0];return n[1]}function l(e,t){return!p(e,t)[0]}function p(e,n,r){void 0===r&&(r={});const i=a(e,n,r),o=function(e){const{done:t,value:n}=e.next();return t?void 0:n}(i);if(o[0]){return[new t(o[0],(function*(){for(const e of i)e[0]&&(yield e[0])})),void 0]}return[void 0,o[1]]}function y(e,t){return new s({type:e,schema:null,validator:t})}function v(){return y("never",(()=>!1))}function h(e){const t=e?Object.keys(e):[],r=v();return new s({type:"object",schema:e||null,*entries(i){if(e&&n(i)){const n=new Set(Object.keys(i));for(const r of t)n.delete(r),yield[r,i[r],e[r]];for(const e of n)yield[e,i[e],r]}},validator:e=>n(e)||"Expected an object, but received: "+i(e),coercer:e=>n(e)?{...e}:e})}function m(e){return new s({...e,validator:(t,n)=>void 0===t||e.validator(t,n),refiner:(t,n)=>void 0===t||e.refiner(t,n)})}function b(){return y("string",(e=>"string"==typeof e||"Expected a string, but received: "+i(e)))}function g(e){const t=Object.keys(e);return new s({type:"type",schema:e,*entries(r){if(n(r))for(const n of t)yield[n,r[n],e[n]]},validator:e=>n(e)||"Expected an object, but received: "+i(e)})}function w(){return y("unknown",(()=>!0))}function x(e,t,n){return new s({...e,coercer:(r,i)=>l(r,t)?e.coercer(n(r,i),i):e.coercer(r,i)})}function E(e){return e instanceof Map||e instanceof Set?e.size:e.length}function j(e,t,n){return new s({...e,*refiner(r,i){yield*e.refiner(r,i);const o=c(n(r,i),i,e,r);for(const e of o)yield{...e,refinement:t}}})}e.Struct=s,e.StructError=t,e.any=function(){return y("any",(()=>!0))},e.array=function(e){return new s({type:"array",schema:e,*entries(t){if(e&&Array.isArray(t))for(const[n,r]of t.entries())yield[n,r,e]},coercer:e=>Array.isArray(e)?e.slice():e,validator:e=>Array.isArray(e)||"Expected an array value, but received: "+i(e)})},e.assert=u,e.assign=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r="type"===t[0].type,i=t.map((e=>e.schema)),o=Object.assign({},...i);return r?g(o):h(o)},e.bigint=function(){return y("bigint",(e=>"bigint"==typeof e))},e.boolean=function(){return y("boolean",(e=>"boolean"==typeof e))},e.coerce=x,e.create=f,e.date=function(){return y("date",(e=>e instanceof Date&&!isNaN(e.getTime())||"Expected a valid `Date` object, but received: "+i(e)))},e.defaulted=function(e,t,n){return void 0===n&&(n={}),x(e,w(),(e=>{const i="function"==typeof t?t():t;if(void 0===e)return i;if(!n.strict&&r(e)&&r(i)){const t={...e};let n=!1;for(const e in i)void 0===t[e]&&(t[e]=i[e],n=!0);if(n)return t}return e}))},e.define=y,e.deprecated=function(e,t){return new s({...e,refiner:(t,n)=>void 0===t||e.refiner(t,n),validator:(n,r)=>void 0===n||(t(n,r),e.validator(n,r))})},e.dynamic=function(e){return new s({type:"dynamic",schema:null,*entries(t,n){const r=e(t,n);yield*r.entries(t,n)},validator:(t,n)=>e(t,n).validator(t,n),coercer:(t,n)=>e(t,n).coercer(t,n),refiner:(t,n)=>e(t,n).refiner(t,n)})},e.empty=function(e){return j(e,"empty",(t=>{const n=E(t);return 0===n||"Expected an empty "+e.type+" but received one with a size of `"+n+"`"}))},e.enums=function(e){const t={},n=e.map((e=>i(e))).join();for(const n of e)t[n]=n;return new s({type:"enums",schema:t,validator:t=>e.includes(t)||"Expected one of `"+n+"`, but received: "+i(t)})},e.func=function(){return y("func",(e=>"function"==typeof e||"Expected a function, but received: "+i(e)))},e.instance=function(e){return y("instance",(t=>t instanceof e||"Expected a `"+e.name+"` instance, but received: "+i(t)))},e.integer=function(){return y("integer",(e=>"number"==typeof e&&!isNaN(e)&&Number.isInteger(e)||"Expected an integer, but received: "+i(e)))},e.intersection=function(e){return new s({type:"intersection",schema:null,*entries(t,n){for(const r of e)yield*r.entries(t,n)},*validator(t,n){for(const r of e)yield*r.validator(t,n)},*refiner(t,n){for(const r of e)yield*r.refiner(t,n)}})},e.is=l,e.lazy=function(e){let t;return new s({type:"lazy",schema:null,*entries(n,r){null!=t||(t=e()),yield*t.entries(n,r)},validator:(n,r)=>(null!=t||(t=e()),t.validator(n,r)),coercer:(n,r)=>(null!=t||(t=e()),t.coercer(n,r)),refiner:(n,r)=>(null!=t||(t=e()),t.refiner(n,r))})},e.literal=function(e){const t=i(e),n=typeof e;return new s({type:"literal",schema:"string"===n||"number"===n||"boolean"===n?e:null,validator:n=>n===e||"Expected the literal `"+t+"`, but received: "+i(n)})},e.map=function(e,t){return new s({type:"map",schema:null,*entries(n){if(e&&t&&n instanceof Map)for(const[r,i]of n.entries())yield[r,r,e],yield[r,i,t]},coercer:e=>e instanceof Map?new Map(e):e,validator:e=>e instanceof Map||"Expected a `Map` object, but received: "+i(e)})},e.mask=d,e.max=function(e,t,n){void 0===n&&(n={});const{exclusive:r}=n;return j(e,"max",(n=>r?n<t:n<=t||"Expected a "+e.type+" less than "+(r?"":"or equal to ")+t+" but received `"+n+"`"))},e.min=function(e,t,n){void 0===n&&(n={});const{exclusive:r}=n;return j(e,"min",(n=>r?n>t:n>=t||"Expected a "+e.type+" greater than "+(r?"":"or equal to ")+t+" but received `"+n+"`"))},e.never=v,e.nonempty=function(e){return j(e,"nonempty",(t=>E(t)>0||"Expected a nonempty "+e.type+" but received an empty one"))},e.nullable=function(e){return new s({...e,validator:(t,n)=>null===t||e.validator(t,n),refiner:(t,n)=>null===t||e.refiner(t,n)})},e.number=function(){return y("number",(e=>"number"==typeof e&&!isNaN(e)||"Expected a number, but received: "+i(e)))},e.object=h,e.omit=function(e,t){const{schema:n}=e,r={...n};for(const e of t)delete r[e];switch(e.type){case"type":return g(r);default:return h(r)}},e.optional=m,e.partial=function(e){const t=e instanceof s?{...e.schema}:{...e};for(const e in t)t[e]=m(t[e]);return h(t)},e.pattern=function(e,t){return j(e,"pattern",(n=>t.test(n)||"Expected a "+e.type+" matching `/"+t.source+'/` but received "'+n+'"'))},e.pick=function(e,t){const{schema:n}=e,r={};for(const e of t)r[e]=n[e];return h(r)},e.record=function(e,t){return new s({type:"record",schema:null,*entries(r){if(n(r))for(const n in r){const i=r[n];yield[n,n,e],yield[n,i,t]}},validator:e=>n(e)||"Expected an object, but received: "+i(e)})},e.refine=j,e.regexp=function(){return y("regexp",(e=>e instanceof RegExp))},e.set=function(e){return new s({type:"set",schema:null,*entries(t){if(e&&t instanceof Set)for(const n of t)yield[n,n,e]},coercer:e=>e instanceof Set?new Set(e):e,validator:e=>e instanceof Set||"Expected a `Set` object, but received: "+i(e)})},e.size=function(e,t,n){void 0===n&&(n=t);const r="Expected a "+e.type,i=t===n?"of `"+t+"`":"between `"+t+"` and `"+n+"`";return j(e,"size",(e=>{if("number"==typeof e||e instanceof Date)return t<=e&&e<=n||r+" "+i+" but received `"+e+"`";if(e instanceof Map||e instanceof Set){const{size:o}=e;return t<=o&&o<=n||r+" with a size "+i+" but received one with a size of `"+o+"`"}{const{length:o}=e;return t<=o&&o<=n||r+" with a length "+i+" but received one with a length of `"+o+"`"}}))},e.string=b,e.struct=function(e,t){return console.warn("superstruct@0.11 - The `struct` helper has been renamed to `define`."),y(e,t)},e.trimmed=function(e){return x(e,b(),(e=>e.trim()))},e.tuple=function(e){const t=v();return new s({type:"tuple",schema:null,*entries(n){if(Array.isArray(n)){const r=Math.max(e.length,n.length);for(let i=0;i<r;i++)yield[i,n[i],e[i]||t]}},validator:e=>Array.isArray(e)||"Expected an array, but received: "+i(e)})},e.type=g,e.union=function(e){const t=e.map((e=>e.type)).join(" | ");return new s({type:"union",schema:null,coercer:(t,n)=>(e.find((e=>{const[n]=e.validate(t,{coerce:!0});return!n}))||w()).coercer(t,n),validator(n,r){const o=[];for(const t of e){const[...e]=a(n,t,r),[i]=e;if(!i[0])return[];for(const[t]of e)t&&o.push(t)}return["Expected the value to satisfy a union of `"+t+"`, but received: "+i(n),...o]}})},e.unknown=w,e.validate=p,Object.defineProperty(e,"__esModule",{value:!0})}));
