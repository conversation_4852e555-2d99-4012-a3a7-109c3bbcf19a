"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MemoryConsolidationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryConsolidationService = void 0;
const common_1 = require("@nestjs/common");
const memory_types_1 = require("../memory.types");
const short_term_memory_service_1 = require("./short-term-memory.service");
const medium_term_memory_service_1 = require("./medium-term-memory.service");
const long_term_memory_service_1 = require("./long-term-memory.service");
let MemoryConsolidationService = MemoryConsolidationService_1 = class MemoryConsolidationService {
    constructor(shortTermService, mediumTermService, longTermService) {
        this.shortTermService = shortTermService;
        this.mediumTermService = mediumTermService;
        this.longTermService = longTermService;
        this.logger = new common_1.Logger(MemoryConsolidationService_1.name);
        this.consolidationRules = [];
        this.shortToMediumThreshold = {
            accessCount: 3,
            activationLevel: 1.5,
            ageHours: 6,
        };
        this.mediumToLongThreshold = {
            reinforcementCount: 5,
            consolidationScore: 0.8,
            ageDays: 7,
            importance: memory_types_1.MemoryImportance.HIGH,
        };
        this.logger.log('记忆巩固服务初始化完成');
        this.initializeConsolidationRules();
    }
    async performConsolidation() {
        this.logger.log('开始执行记忆巩固任务');
        try {
            const results = {
                shortToMedium: 0,
                mediumToLong: 0,
                totalProcessed: 0,
            };
            const shortTermCandidates = await this.getShortTermConsolidationCandidates();
            this.logger.log(`找到${shortTermCandidates.length}个短期记忆巩固候选`);
            for (const candidate of shortTermCandidates) {
                try {
                    const success = await this.consolidateShortToMedium(candidate);
                    if (success) {
                        results.shortToMedium++;
                    }
                }
                catch (error) {
                    this.logger.warn(`短期记忆巩固失败 - ID: ${candidate.id}, 错误: ${error.message}`);
                }
            }
            const mediumTermCandidates = await this.getMediumTermConsolidationCandidates();
            this.logger.log(`找到${mediumTermCandidates.length}个中期记忆巩固候选`);
            for (const candidate of mediumTermCandidates) {
                try {
                    const success = await this.consolidateMediumToLong(candidate);
                    if (success) {
                        results.mediumToLong++;
                    }
                }
                catch (error) {
                    this.logger.warn(`中期记忆巩固失败 - ID: ${candidate.id}, 错误: ${error.message}`);
                }
            }
            results.totalProcessed = results.shortToMedium + results.mediumToLong;
            this.logger.log(`记忆巩固完成 - 短期到中期: ${results.shortToMedium}, 中期到长期: ${results.mediumToLong}`);
            return results;
        }
        catch (error) {
            this.logger.error(`记忆巩固失败: ${error.message}`);
            throw error;
        }
    }
    async checkMemoryForConsolidation(memory) {
        try {
            if (memory.memoryType === memory_types_1.MemoryType.SHORT_TERM) {
                const shortMemory = memory;
                if (this.shouldConsolidateShortToMedium(shortMemory)) {
                    return await this.consolidateShortToMedium(shortMemory);
                }
            }
            else if (memory.memoryType === memory_types_1.MemoryType.MEDIUM_TERM) {
                const mediumMemory = memory;
                if (this.shouldConsolidateMediumToLong(mediumMemory)) {
                    return await this.consolidateMediumToLong(mediumMemory);
                }
            }
            return false;
        }
        catch (error) {
            this.logger.error(`检查记忆巩固失败: ${error.message}`);
            return false;
        }
    }
    async getPendingCount() {
        try {
            const [shortCandidates, mediumCandidates] = await Promise.all([
                this.getShortTermConsolidationCandidates(),
                this.getMediumTermConsolidationCandidates(),
            ]);
            return shortCandidates.length + mediumCandidates.length;
        }
        catch (error) {
            this.logger.error(`获取待巩固数量失败: ${error.message}`);
            return 0;
        }
    }
    initializeConsolidationRules() {
        this.consolidationRules = [
            {
                id: 'high_access_promotion',
                name: '高访问频率提升',
                description: '访问频率高的记忆优先巩固',
                triggerConditions: {
                    accessCount: { $gte: this.shortToMediumThreshold.accessCount },
                },
                actions: ['promote_to_medium_term'],
                priority: 1,
                active: true,
            },
            {
                id: 'emotional_significance_promotion',
                name: '情感重要性提升',
                description: '情感重要性高的记忆优先巩固',
                triggerConditions: {
                    'metadata.sentiment': { $exists: true },
                    importance: { $gte: memory_types_1.MemoryImportance.HIGH },
                },
                actions: ['promote_to_long_term'],
                priority: 2,
                active: true,
            },
            {
                id: 'time_based_promotion',
                name: '基于时间的提升',
                description: '存在时间达到阈值的记忆自动巩固',
                triggerConditions: {
                    age: { $gte: 'threshold' },
                },
                actions: ['promote_to_next_level'],
                priority: 3,
                active: true,
            },
            {
                id: 'importance_based_promotion',
                name: '基于重要性的提升',
                description: '高重要性记忆快速巩固',
                triggerConditions: {
                    importance: { $gte: memory_types_1.MemoryImportance.CRITICAL },
                },
                actions: ['promote_to_long_term'],
                priority: 0,
                active: true,
            },
        ];
        this.logger.log(`初始化了${this.consolidationRules.length}个巩固规则`);
    }
    async getShortTermConsolidationCandidates() {
        try {
            const candidates = [];
            const queries = [
                { accessCount: { $gte: this.shortToMediumThreshold.accessCount } },
                { 'metadata.activationLevel': { $gte: this.shortToMediumThreshold.activationLevel } },
                { importance: { $gte: memory_types_1.MemoryImportance.HIGH } },
                {
                    created_at: {
                        $lte: new Date(Date.now() - this.shortToMediumThreshold.ageHours * 60 * 60 * 1000).toISOString()
                    }
                },
            ];
            for (const queryFilter of queries) {
                const memories = await this.shortTermService.retrieve({
                    memoryType: memory_types_1.MemoryType.SHORT_TERM,
                    limit: 100,
                });
                const filtered = memories.filter(memory => this.shouldConsolidateShortToMedium(memory));
                candidates.push(...filtered);
            }
            const uniqueCandidates = candidates.filter((memory, index, self) => self.findIndex(m => m.id === memory.id) === index);
            return uniqueCandidates;
        }
        catch (error) {
            this.logger.error(`获取短期记忆巩固候选失败: ${error.message}`);
            return [];
        }
    }
    async getMediumTermConsolidationCandidates() {
        try {
            const candidates = await this.mediumTermService.getConsolidationCandidates(100);
            return candidates.filter(memory => this.shouldConsolidateMediumToLong(memory));
        }
        catch (error) {
            this.logger.error(`获取中期记忆巩固候选失败: ${error.message}`);
            return [];
        }
    }
    shouldConsolidateShortToMedium(memory) {
        const now = new Date();
        const ageHours = (now.getTime() - memory.createdAt.getTime()) / (60 * 60 * 1000);
        const conditions = [
            memory.accessCount >= this.shortToMediumThreshold.accessCount,
            memory.activationLevel >= this.shortToMediumThreshold.activationLevel,
            ageHours >= this.shortToMediumThreshold.ageHours,
            memory.importance >= memory_types_1.MemoryImportance.NORMAL,
        ];
        const satisfiedConditions = conditions.filter(Boolean).length;
        return satisfiedConditions >= 2 || memory.importance >= memory_types_1.MemoryImportance.HIGH;
    }
    shouldConsolidateMediumToLong(memory) {
        const now = new Date();
        const ageDays = (now.getTime() - memory.createdAt.getTime()) / (24 * 60 * 60 * 1000);
        const conditions = [
            memory.reinforcementCount >= this.mediumToLongThreshold.reinforcementCount,
            memory.consolidationScore >= this.mediumToLongThreshold.consolidationScore,
            ageDays >= this.mediumToLongThreshold.ageDays,
            memory.importance >= this.mediumToLongThreshold.importance,
        ];
        const satisfiedConditions = conditions.filter(Boolean).length;
        return satisfiedConditions >= 2 || memory.importance >= memory_types_1.MemoryImportance.CRITICAL;
    }
    async consolidateShortToMedium(shortMemory) {
        this.logger.debug(`巩固短期记忆到中期 - ID: ${shortMemory.id}`);
        try {
            const mediumMemory = {
                id: `mtm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                agentType: shortMemory.agentType,
                userId: shortMemory.userId,
                sessionId: shortMemory.sessionId,
                memoryType: memory_types_1.MemoryType.MEDIUM_TERM,
                importance: Math.max(shortMemory.importance, memory_types_1.MemoryImportance.NORMAL),
                content: shortMemory.content,
                metadata: {
                    ...shortMemory.metadata,
                    consolidatedFrom: shortMemory.id,
                    consolidationReason: '短期记忆自动巩固',
                    originalCreatedAt: shortMemory.createdAt.toISOString(),
                },
                embedding: shortMemory.embedding,
                reinforcementCount: 1,
                lastReinforcedAt: new Date(),
                decayRate: 0.95,
                consolidationScore: this.calculateInitialConsolidationScore(shortMemory),
            };
            const storedMediumMemory = await this.mediumTermService.store(mediumMemory);
            await this.shortTermService.delete(shortMemory.id);
            await this.recordConsolidationEvent({
                id: `consolidation_${Date.now()}`,
                memoryId: shortMemory.id,
                eventType: 'consolidated',
                oldValue: shortMemory,
                newValue: storedMediumMemory,
                reason: '短期记忆巩固到中期记忆',
                confidence: 0.9,
                timestamp: new Date(),
                agentType: shortMemory.agentType,
                userId: shortMemory.userId,
            });
            this.logger.debug(`短期记忆巩固成功 - 原ID: ${shortMemory.id}, 新ID: ${storedMediumMemory.id}`);
            return true;
        }
        catch (error) {
            this.logger.error(`短期记忆巩固失败: ${error.message}`);
            return false;
        }
    }
    async consolidateMediumToLong(mediumMemory) {
        this.logger.debug(`巩固中期记忆到长期 - ID: ${mediumMemory.id}`);
        try {
            const longMemory = {
                id: `ltm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                agentType: mediumMemory.agentType,
                userId: mediumMemory.userId,
                sessionId: mediumMemory.sessionId,
                memoryType: memory_types_1.MemoryType.LONG_TERM,
                importance: Math.max(mediumMemory.importance, memory_types_1.MemoryImportance.HIGH),
                content: mediumMemory.content,
                metadata: {
                    ...mediumMemory.metadata,
                    consolidatedFrom: mediumMemory.id,
                    consolidationReason: '中期记忆自动巩固',
                    reinforcementHistory: {
                        count: mediumMemory.reinforcementCount,
                        score: mediumMemory.consolidationScore,
                    },
                },
                embedding: mediumMemory.embedding,
                consolidationDate: new Date(),
                archivalStatus: 'active',
                emotionalSignificance: this.calculateEmotionalSignificance(mediumMemory),
                narrativeContext: this.generateNarrativeContext(mediumMemory),
            };
            const storedLongMemory = await this.longTermService.store(longMemory);
            await this.mediumTermService.delete(mediumMemory.id);
            await this.recordConsolidationEvent({
                id: `consolidation_${Date.now()}`,
                memoryId: mediumMemory.id,
                eventType: 'consolidated',
                oldValue: mediumMemory,
                newValue: storedLongMemory,
                reason: '中期记忆巩固到长期记忆',
                confidence: 0.95,
                timestamp: new Date(),
                agentType: mediumMemory.agentType,
                userId: mediumMemory.userId,
            });
            this.logger.debug(`中期记忆巩固成功 - 原ID: ${mediumMemory.id}, 新ID: ${storedLongMemory.id}`);
            return true;
        }
        catch (error) {
            this.logger.error(`中期记忆巩固失败: ${error.message}`);
            return false;
        }
    }
    calculateInitialConsolidationScore(memory) {
        let score = 0.5;
        score += Math.min(memory.accessCount * 0.05, 0.2);
        score += memory.importance * 0.1;
        if (memory.metadata?.sentiment) {
            score += Math.abs(memory.metadata.sentiment) * 0.15;
        }
        if (memory.metadata?.confidence) {
            score += memory.metadata.confidence * 0.1;
        }
        return Math.min(score, 1.0);
    }
    calculateEmotionalSignificance(memory) {
        let significance = 0.5;
        if (memory.metadata?.sentiment) {
            significance += Math.abs(memory.metadata.sentiment) * 0.3;
        }
        if (memory.importance >= memory_types_1.MemoryImportance.HIGH) {
            significance += 0.2;
        }
        const emotionalKeywords = ['感动', '愤怒', '恐惧', '快乐', '悲伤', '惊讶', '厌恶'];
        const content = JSON.stringify(memory.content).toLowerCase();
        const hasEmotionalContent = emotionalKeywords.some(keyword => content.includes(keyword));
        if (hasEmotionalContent) {
            significance += 0.1;
        }
        return Math.min(significance, 1.0);
    }
    generateNarrativeContext(memory) {
        const contexts = [];
        const age = Date.now() - memory.createdAt.getTime();
        const days = Math.floor(age / (24 * 60 * 60 * 1000));
        contexts.push(`存在${days}天后巩固`);
        if (memory.accessCount > 5) {
            contexts.push('高频访问记忆');
        }
        else if (memory.accessCount > 2) {
            contexts.push('中频访问记忆');
        }
        const importanceMap = {
            [memory_types_1.MemoryImportance.LOW]: '低重要性',
            [memory_types_1.MemoryImportance.NORMAL]: '普通重要性',
            [memory_types_1.MemoryImportance.HIGH]: '高重要性',
            [memory_types_1.MemoryImportance.CRITICAL]: '关键重要性',
            [memory_types_1.MemoryImportance.PERMANENT]: '永久重要性',
        };
        contexts.push(importanceMap[memory.importance]);
        if (memory.metadata?.category) {
            contexts.push(`分类: ${memory.metadata.category}`);
        }
        return contexts.join(' | ');
    }
    async recordConsolidationEvent(event) {
        try {
            this.logger.debug(`记录巩固事件 - 类型: ${event.eventType}, 记忆ID: ${event.memoryId}`);
        }
        catch (error) {
            this.logger.warn(`记录巩固事件失败: ${error.message}`);
        }
    }
};
exports.MemoryConsolidationService = MemoryConsolidationService;
exports.MemoryConsolidationService = MemoryConsolidationService = MemoryConsolidationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [short_term_memory_service_1.ShortTermMemoryService,
        medium_term_memory_service_1.MediumTermMemoryService,
        long_term_memory_service_1.LongTermMemoryService])
], MemoryConsolidationService);
//# sourceMappingURL=memory-consolidation.service.js.map