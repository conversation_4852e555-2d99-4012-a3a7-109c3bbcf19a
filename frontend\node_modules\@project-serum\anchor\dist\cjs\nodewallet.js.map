{"version": 3, "file": "nodewallet.js", "sourceRoot": "", "sources": ["../../src/nodewallet.ts"], "names": [], "mappings": ";;AAAA,mCAAgC;AAChC,6CAAkE;AAGlE;;GAEG;AACH,MAAqB,UAAU;IAC7B,YAAqB,KAAc;QAAd,UAAK,GAAL,KAAK,CAAS;IAAG,CAAC;IAEvC,MAAM,CAAC,KAAK;QACV,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAEnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,EAAE,EAAE;YAClE,MAAM,IAAI,KAAK,CACb,2DAA2D,CAC5D,CAAC;SACH;QAED,MAAM,KAAK,GAAG,iBAAO,CAAC,aAAa,CACjC,eAAM,CAAC,IAAI,CACT,IAAI,CAAC,KAAK,CACR,OAAO,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE;YACpD,QAAQ,EAAE,OAAO;SAClB,CAAC,CACH,CACF,CACF,CAAC;QAEF,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAe;QACnC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,GAAkB;QAC1C,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACnB,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1B,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;CACF;AAxCD,6BAwCC"}