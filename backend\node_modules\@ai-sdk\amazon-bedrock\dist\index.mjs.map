{"version": 3, "sources": ["../src/bedrock-provider.ts", "../src/bedrock-chat-language-model.ts", "../src/bedrock-prepare-tools.ts", "../src/convert-to-bedrock-chat-messages.ts", "../src/map-bedrock-finish-reason.ts", "../src/bedrock-embedding-model.ts"], "sourcesContent": ["import {\n  EmbeddingModelV1,\n  LanguageModelV1,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport {\n  generateId,\n  loadOptionalSetting,\n  loadSetting,\n} from '@ai-sdk/provider-utils';\nimport {\n  BedrockRuntimeClient,\n  BedrockRuntimeClientConfig,\n} from '@aws-sdk/client-bedrock-runtime';\nimport { BedrockChatLanguageModel } from './bedrock-chat-language-model';\nimport {\n  BedrockChatModelId,\n  BedrockChatSettings,\n} from './bedrock-chat-settings';\nimport { BedrockEmbeddingModel } from './bedrock-embedding-model';\nimport {\n  BedrockEmbeddingModelId,\n  BedrockEmbeddingSettings,\n} from './bedrock-embedding-settings';\n\nexport interface AmazonBedrockProviderSettings {\n  region?: string;\n  accessKeyId?: string;\n  secretAccessKey?: string;\n  sessionToken?: string;\n\n  /**\n   * Complete Bedrock configuration for setting advanced authentication and\n   * other options. When this is provided, the region, accessKeyId, and\n   * secretAccessKey settings are ignored.\n   */\n  bedrockOptions?: BedrockRuntimeClientConfig;\n\n  // for testing\n  generateId?: () => string;\n}\n\nexport interface AmazonBedrockProvider extends ProviderV1 {\n  (\n    modelId: BedrockChatModelId,\n    settings?: BedrockChatSettings,\n  ): LanguageModelV1;\n\n  languageModel(\n    modelId: BedrockChatModelId,\n    settings?: BedrockChatSettings,\n  ): LanguageModelV1;\n\n  embedding(\n    modelId: BedrockEmbeddingModelId,\n    settings?: BedrockEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n}\n\n/**\nCreate an Amazon Bedrock provider instance.\n */\nexport function createAmazonBedrock(\n  options: AmazonBedrockProviderSettings = {},\n): AmazonBedrockProvider {\n  const createBedrockRuntimeClient = () =>\n    new BedrockRuntimeClient(\n      options.bedrockOptions ?? {\n        region: loadSetting({\n          settingValue: options.region,\n          settingName: 'region',\n          environmentVariableName: 'AWS_REGION',\n          description: 'AWS region',\n        }),\n        credentials: {\n          accessKeyId: loadSetting({\n            settingValue: options.accessKeyId,\n            settingName: 'accessKeyId',\n            environmentVariableName: 'AWS_ACCESS_KEY_ID',\n            description: 'AWS access key ID',\n          }),\n          secretAccessKey: loadSetting({\n            settingValue: options.secretAccessKey,\n            settingName: 'secretAccessKey',\n            environmentVariableName: 'AWS_SECRET_ACCESS_KEY',\n            description: 'AWS secret access key',\n          }),\n          sessionToken: loadOptionalSetting({\n            settingValue: options.sessionToken,\n            environmentVariableName: 'AWS_SESSION_TOKEN',\n          }),\n        },\n      },\n    );\n\n  const createChatModel = (\n    modelId: BedrockChatModelId,\n    settings: BedrockChatSettings = {},\n  ) =>\n    new BedrockChatLanguageModel(modelId, settings, {\n      client: createBedrockRuntimeClient(),\n      generateId,\n    });\n\n  const provider = function (\n    modelId: BedrockChatModelId,\n    settings?: BedrockChatSettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Amazon Bedrock model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  const createEmbeddingModel = (\n    modelId: BedrockEmbeddingModelId,\n    settings: BedrockEmbeddingSettings = {},\n  ) =>\n    new BedrockEmbeddingModel(modelId, settings, {\n      client: createBedrockRuntimeClient(),\n    });\n\n  provider.languageModel = createChatModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n\n  return provider as AmazonBedrockProvider;\n}\n\n/**\nDefault Bedrock provider instance.\n */\nexport const bedrock = createAmazonBedrock();\n", "import {\n  J<PERSON>NObject,\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1ProviderMetadata,\n  LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { ParseResult } from '@ai-sdk/provider-utils';\nimport {\n  BedrockRuntimeClient,\n  ConverseCommand,\n  ConverseCommandInput,\n  ConverseStreamCommand,\n  ConverseStreamOutput,\n  GuardrailConfiguration,\n  GuardrailStreamConfiguration,\n  ToolInputSchema,\n} from '@aws-sdk/client-bedrock-runtime';\nimport {\n  BedrockChatModelId,\n  BedrockChatSettings,\n} from './bedrock-chat-settings';\nimport { prepareTools } from './bedrock-prepare-tools';\nimport { convertToBedrockChatMessages } from './convert-to-bedrock-chat-messages';\nimport { mapBedrockFinishReason } from './map-bedrock-finish-reason';\n\ntype BedrockChatConfig = {\n  client: BedrockRuntimeClient;\n  generateId: () => string;\n};\n\nexport class BedrockChatLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly provider = 'amazon-bedrock';\n  readonly defaultObjectGenerationMode = 'tool';\n  readonly supportsImageUrls = false;\n\n  readonly modelId: BedrockChatModelId;\n  readonly settings: BedrockChatSettings;\n\n  private readonly config: BedrockChatConfig;\n\n  constructor(\n    modelId: BedrockChatModelId,\n    settings: BedrockChatSettings,\n    config: BedrockChatConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  private getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata,\n    headers,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]): {\n    command: ConverseCommandInput;\n    warnings: LanguageModelV1CallWarning[];\n  } {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (frequencyPenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'frequencyPenalty',\n      });\n    }\n\n    if (presencePenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'presencePenalty',\n      });\n    }\n\n    if (seed != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'seed',\n      });\n    }\n\n    if (headers != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'headers',\n      });\n    }\n\n    if (topK != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topK',\n      });\n    }\n\n    if (responseFormat != null && responseFormat.type !== 'text') {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'responseFormat',\n        details: 'JSON response format is not supported.',\n      });\n    }\n\n    const { system, messages } = convertToBedrockChatMessages(prompt);\n\n    const baseArgs: ConverseCommandInput = {\n      modelId: this.modelId,\n      system: system ? [{ text: system }] : undefined,\n      additionalModelRequestFields: this.settings.additionalModelRequestFields,\n      inferenceConfig: {\n        maxTokens,\n        temperature,\n        topP,\n        stopSequences,\n      },\n      messages,\n      guardrailConfig: providerMetadata?.bedrock?.guardrailConfig as\n        | GuardrailConfiguration\n        | GuardrailStreamConfiguration\n        | undefined,\n    };\n\n    switch (type) {\n      case 'regular': {\n        const { toolConfig, toolWarnings } = prepareTools(mode);\n        return {\n          command: {\n            ...baseArgs,\n            ...(toolConfig.tools?.length ? { toolConfig } : {}),\n          },\n          warnings: [...warnings, ...toolWarnings],\n        };\n      }\n\n      case 'object-json': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'json-mode object generation',\n        });\n      }\n\n      case 'object-tool': {\n        return {\n          command: {\n            ...baseArgs,\n            toolConfig: {\n              tools: [\n                {\n                  toolSpec: {\n                    name: mode.tool.name,\n                    description: mode.tool.description,\n                    inputSchema: {\n                      json: mode.tool.parameters,\n                    } as ToolInputSchema,\n                  },\n                },\n              ],\n              toolChoice: { tool: { name: mode.tool.name } },\n            },\n          },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { command, warnings } = this.getArgs(options);\n\n    const response = await this.config.client.send(\n      new ConverseCommand(command),\n    );\n\n    const { messages: rawPrompt, ...rawSettings } = command;\n\n    const providerMetadata = response.trace\n      ? { bedrock: { trace: response.trace as JSONObject } }\n      : undefined;\n\n    return {\n      text:\n        response.output?.message?.content\n          ?.map(part => part.text ?? '')\n          .join('') ?? undefined,\n      toolCalls: response.output?.message?.content\n        ?.filter(part => !!part.toolUse)\n        ?.map(part => ({\n          toolCallType: 'function',\n          toolCallId: part.toolUse?.toolUseId ?? this.config.generateId(),\n          toolName: part.toolUse?.name ?? `tool-${this.config.generateId()}`,\n          args: JSON.stringify(part.toolUse?.input ?? ''),\n        })),\n      finishReason: mapBedrockFinishReason(response.stopReason),\n      usage: {\n        promptTokens: response.usage?.inputTokens ?? Number.NaN,\n        completionTokens: response.usage?.outputTokens ?? Number.NaN,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      warnings,\n      providerMetadata,\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { command, warnings } = this.getArgs(options);\n\n    const response = await this.config.client.send(\n      new ConverseStreamCommand(command),\n    );\n\n    const { messages: rawPrompt, ...rawSettings } = command;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n    let providerMetadata: LanguageModelV1ProviderMetadata | undefined =\n      undefined;\n\n    if (!response.stream) {\n      throw new Error('No stream found');\n    }\n\n    const stream = new ReadableStream<any>({\n      async start(controller) {\n        for await (const chunk of response.stream!) {\n          controller.enqueue({ success: true, value: chunk });\n        }\n        controller.close();\n      },\n    });\n\n    const toolCallContentBlocks: Record<\n      number,\n      {\n        toolCallId: string;\n        toolName: string;\n        jsonText: string;\n      }\n    > = {};\n\n    return {\n      stream: stream.pipeThrough(\n        new TransformStream<\n          ParseResult<ConverseStreamOutput>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            function enqueueError(error: Error) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error });\n            }\n\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              enqueueError(chunk.error);\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle errors:\n            if (value.internalServerException) {\n              enqueueError(value.internalServerException);\n              return;\n            }\n            if (value.modelStreamErrorException) {\n              enqueueError(value.modelStreamErrorException);\n              return;\n            }\n            if (value.throttlingException) {\n              enqueueError(value.throttlingException);\n              return;\n            }\n            if (value.validationException) {\n              enqueueError(value.validationException);\n              return;\n            }\n\n            if (value.messageStop) {\n              finishReason = mapBedrockFinishReason(\n                value.messageStop.stopReason,\n              );\n            }\n\n            if (value.metadata) {\n              usage = {\n                promptTokens: value.metadata.usage?.inputTokens ?? Number.NaN,\n                completionTokens:\n                  value.metadata.usage?.outputTokens ?? Number.NaN,\n              };\n\n              if (value.metadata.trace) {\n                providerMetadata = {\n                  bedrock: {\n                    trace: value.metadata.trace as JSONObject,\n                  },\n                };\n              }\n            }\n\n            if (value.contentBlockDelta?.delta?.text) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: value.contentBlockDelta.delta.text,\n              });\n            }\n\n            const contentBlockStart = value.contentBlockStart;\n            if (contentBlockStart?.start?.toolUse != null) {\n              const toolUse = contentBlockStart.start.toolUse;\n              toolCallContentBlocks[contentBlockStart.contentBlockIndex!] = {\n                toolCallId: toolUse.toolUseId!,\n                toolName: toolUse.name!,\n                jsonText: '',\n              };\n            }\n\n            const contentBlockDelta = value.contentBlockDelta;\n            if (contentBlockDelta?.delta?.toolUse) {\n              const contentBlock =\n                toolCallContentBlocks[contentBlockDelta.contentBlockIndex!];\n              const delta = contentBlockDelta.delta.toolUse.input ?? '';\n\n              controller.enqueue({\n                type: 'tool-call-delta',\n                toolCallType: 'function',\n                toolCallId: contentBlock.toolCallId,\n                toolName: contentBlock.toolName,\n                argsTextDelta: delta,\n              });\n\n              contentBlock.jsonText += delta;\n            }\n\n            const contentBlockStop = value.contentBlockStop;\n            if (contentBlockStop != null) {\n              const index = contentBlockStop.contentBlockIndex!;\n              const contentBlock = toolCallContentBlocks[index];\n\n              // when finishing a tool call block, send the full tool call:\n              if (contentBlock != null) {\n                controller.enqueue({\n                  type: 'tool-call',\n                  toolCallType: 'function',\n                  toolCallId: contentBlock.toolCallId,\n                  toolName: contentBlock.toolName,\n                  args: contentBlock.jsonText,\n                });\n\n                delete toolCallContentBlocks[index];\n              }\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              usage,\n              providerMetadata,\n            });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      warnings,\n    };\n  }\n}\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  Tool,\n  ToolConfiguration,\n  ToolInputSchema,\n} from '@aws-sdk/client-bedrock-runtime';\n\nexport function prepareTools(\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  },\n): {\n  toolConfig: ToolConfiguration; // note: do not rename, name required by Bedrock\n  toolWarnings: LanguageModelV1CallWarning[];\n} {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  if (tools == null) {\n    return {\n      toolConfig: { tools: undefined, toolChoice: undefined },\n      toolWarnings: [],\n    };\n  }\n\n  const toolWarnings: LanguageModelV1CallWarning[] = [];\n  const bedrockTools: Tool[] = [];\n\n  for (const tool of tools) {\n    if (tool.type === 'provider-defined') {\n      toolWarnings.push({ type: 'unsupported-tool', tool });\n    } else {\n      bedrockTools.push({\n        toolSpec: {\n          name: tool.name,\n          description: tool.description,\n          inputSchema: {\n            json: tool.parameters,\n          } as ToolInputSchema,\n        },\n      });\n    }\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  if (toolChoice == null) {\n    return {\n      toolConfig: { tools: bedrockTools, toolChoice: undefined },\n      toolWarnings,\n    };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n      return {\n        toolConfig: { tools: bedrockTools, toolChoice: { auto: {} } },\n        toolWarnings,\n      };\n    case 'required':\n      return {\n        toolConfig: { tools: bedrockTools, toolChoice: { any: {} } },\n        toolWarnings,\n      };\n    case 'none':\n      // Bedrock does not support 'none' tool choice, so we remove the tools:\n      return {\n        toolConfig: { tools: undefined, toolChoice: undefined },\n        toolWarnings,\n      };\n    case 'tool':\n      return {\n        toolConfig: {\n          tools: bedrockTools,\n          toolChoice: { tool: { name: toolChoice.toolName } },\n        },\n        toolWarnings,\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import {\n  LanguageModelV1Message,\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { createIdGenerator } from '@ai-sdk/provider-utils';\nimport { DocumentFormat, ImageFormat } from '@aws-sdk/client-bedrock-runtime';\nimport {\n  BedrockAssistantMessage,\n  BedrockMessagesPrompt,\n  BedrockUserMessage,\n} from './bedrock-chat-prompt';\n\nconst generateFileId = createIdGenerator({ prefix: 'file', size: 16 });\n\nexport function convertToBedrockChatMessages(\n  prompt: LanguageModelV1Prompt,\n): BedrockMessagesPrompt {\n  const blocks = groupIntoBlocks(prompt);\n\n  let system: string | undefined = undefined;\n  const messages: BedrockMessagesPrompt['messages'] = [];\n\n  for (let i = 0; i < blocks.length; i++) {\n    const block = blocks[i];\n    const isLastBlock = i === blocks.length - 1;\n    const type = block.type;\n\n    switch (type) {\n      case 'system': {\n        if (messages.length > 0) {\n          throw new UnsupportedFunctionalityError({\n            functionality:\n              'Multiple system messages that are separated by user/assistant messages',\n          });\n        }\n\n        system = block.messages.map(({ content }) => content).join('\\n');\n        break;\n      }\n\n      case 'user': {\n        // combines all user and tool messages in this block into a single message:\n        const bedrockContent: BedrockUserMessage['content'] = [];\n\n        for (const message of block.messages) {\n          const { role, content } = message;\n          switch (role) {\n            case 'user': {\n              for (let j = 0; j < content.length; j++) {\n                const part = content[j];\n\n                switch (part.type) {\n                  case 'text': {\n                    bedrockContent.push({\n                      text: part.text,\n                    });\n                    break;\n                  }\n                  case 'image': {\n                    if (part.image instanceof URL) {\n                      // The AI SDK automatically downloads images for user image parts with URLs\n                      throw new UnsupportedFunctionalityError({\n                        functionality: 'Image URLs in user messages',\n                      });\n                    }\n\n                    bedrockContent.push({\n                      image: {\n                        format: part.mimeType?.split('/')?.[1] as ImageFormat,\n                        source: {\n                          bytes: part.image ?? (part.image as Uint8Array),\n                        },\n                      },\n                    });\n\n                    break;\n                  }\n                  case 'file': {\n                    if (part.data instanceof URL) {\n                      // The AI SDK automatically downloads files for user file parts with URLs\n                      throw new UnsupportedFunctionalityError({\n                        functionality: 'File URLs in user messages',\n                      });\n                    }\n\n                    bedrockContent.push({\n                      document: {\n                        format: part.mimeType?.split(\n                          '/',\n                        )?.[1] as DocumentFormat,\n                        name: generateFileId(),\n                        source: {\n                          bytes: Buffer.from(part.data, 'base64'),\n                        },\n                      },\n                    });\n\n                    break;\n                  }\n                }\n              }\n\n              break;\n            }\n            case 'tool': {\n              for (let i = 0; i < content.length; i++) {\n                const part = content[i];\n\n                bedrockContent.push({\n                  toolResult: {\n                    toolUseId: part.toolCallId,\n                    content: [{ text: JSON.stringify(part.result) }],\n                  },\n                });\n              }\n\n              break;\n            }\n            default: {\n              const _exhaustiveCheck: never = role;\n              throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n            }\n          }\n        }\n\n        messages.push({ role: 'user', content: bedrockContent });\n\n        break;\n      }\n\n      case 'assistant': {\n        // combines multiple assistant messages in this block into a single message:\n        const bedrockContent: BedrockAssistantMessage['content'] = [];\n\n        for (let j = 0; j < block.messages.length; j++) {\n          const message = block.messages[j];\n          const isLastMessage = j === block.messages.length - 1;\n          const { content } = message;\n\n          for (let k = 0; k < content.length; k++) {\n            const part = content[k];\n            const isLastContentPart = k === content.length - 1;\n\n            switch (part.type) {\n              case 'text': {\n                bedrockContent.push({\n                  text:\n                    // trim the last text part if it's the last message in the block\n                    // because Bedrock does not allow trailing whitespace\n                    // in pre-filled assistant responses\n                    isLastBlock && isLastMessage && isLastContentPart\n                      ? part.text.trim()\n                      : part.text,\n                });\n                break;\n              }\n\n              case 'tool-call': {\n                bedrockContent.push({\n                  toolUse: {\n                    toolUseId: part.toolCallId,\n                    name: part.toolName,\n                    input: part.args as any,\n                  },\n                });\n                break;\n              }\n            }\n          }\n        }\n\n        messages.push({ role: 'assistant', content: bedrockContent });\n\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return {\n    system,\n    messages,\n  };\n}\n\ntype SystemBlock = {\n  type: 'system';\n  messages: Array<LanguageModelV1Message & { role: 'system' }>;\n};\ntype AssistantBlock = {\n  type: 'assistant';\n  messages: Array<LanguageModelV1Message & { role: 'assistant' }>;\n};\ntype UserBlock = {\n  type: 'user';\n  messages: Array<LanguageModelV1Message & { role: 'user' | 'tool' }>;\n};\n\nfunction groupIntoBlocks(\n  prompt: LanguageModelV1Prompt,\n): Array<SystemBlock | AssistantBlock | UserBlock> {\n  const blocks: Array<SystemBlock | AssistantBlock | UserBlock> = [];\n  let currentBlock: SystemBlock | AssistantBlock | UserBlock | undefined =\n    undefined;\n\n  for (const message of prompt) {\n    const { role } = message;\n    switch (role) {\n      case 'system': {\n        if (currentBlock?.type !== 'system') {\n          currentBlock = { type: 'system', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'assistant': {\n        if (currentBlock?.type !== 'assistant') {\n          currentBlock = { type: 'assistant', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'user': {\n        if (currentBlock?.type !== 'user') {\n          currentBlock = { type: 'user', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'tool': {\n        if (currentBlock?.type !== 'user') {\n          currentBlock = { type: 'user', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return blocks;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\nimport { StopReason } from '@aws-sdk/client-bedrock-runtime';\n\nexport function mapBedrockFinishReason(\n  finishReason?: StopReason,\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'stop_sequence':\n    case 'end_turn':\n      return 'stop';\n    case 'max_tokens':\n      return 'length';\n    case 'content_filtered':\n    case 'guardrail_intervened':\n      return 'content-filter';\n    case 'tool_use':\n      return 'tool-calls';\n    default:\n      return 'unknown';\n  }\n}\n", "import { EmbeddingModelV1 } from '@ai-sdk/provider';\nimport {\n  BedrockEmbeddingModelId,\n  BedrockEmbeddingSettings,\n} from './bedrock-embedding-settings';\nimport {\n  BedrockRuntimeClient,\n  InvokeModelCommand,\n} from '@aws-sdk/client-bedrock-runtime';\n\ntype BedrockEmbeddingConfig = {\n  client: BedrockRuntimeClient;\n};\n\ntype DoEmbedResponse = Awaited<ReturnType<EmbeddingModelV1<string>['doEmbed']>>;\n\nexport class BedrockEmbeddingModel implements EmbeddingModelV1<string> {\n  readonly specificationVersion = 'v1';\n  readonly modelId: BedrockEmbeddingModelId;\n  readonly provider = 'amazon-bedrock';\n  readonly maxEmbeddingsPerCall = undefined;\n  readonly supportsParallelCalls = true;\n  private readonly config: BedrockEmbeddingConfig;\n  private readonly settings: BedrockEmbeddingSettings;\n\n  constructor(\n    modelId: BedrockEmbeddingModelId,\n    settings: BedrockEmbeddingSettings,\n    config: BedrockEmbeddingConfig,\n  ) {\n    this.modelId = modelId;\n    this.config = config;\n    this.settings = settings;\n  }\n\n  async doEmbed({\n    values,\n  }: Parameters<\n    EmbeddingModelV1<string>['doEmbed']\n  >[0]): Promise<DoEmbedResponse> {\n    const fn = async (inputText: string) => {\n      const payload = {\n        inputText,\n        dimensions: this.settings.dimensions,\n        normalize: this.settings.normalize,\n      };\n\n      const command = new InvokeModelCommand({\n        contentType: 'application/json',\n        body: JSON.stringify(payload),\n        modelId: this.modelId,\n      });\n      const rawResponse = await this.config.client.send(command);\n\n      const parsed = JSON.parse(new TextDecoder().decode(rawResponse.body));\n\n      return parsed;\n    };\n\n    const responses = await Promise.all(values.map(fn));\n\n    const response = responses.reduce(\n      (acc, r) => {\n        acc.embeddings.push(r.embedding);\n        acc.usage.tokens += r.inputTextTokenCount;\n        return acc;\n      },\n      { embeddings: [], usage: { tokens: 0 } },\n    );\n\n    return response;\n  }\n}\n"], "mappings": ";AAKA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE,wBAAAA;AAAA,OAEK;;;ACbP;AAAA,EAOE,iCAAAC;AAAA,OACK;AAEP;AAAA,EAEE;AAAA,EAEA;AAAA,OAKK;;;ACnBP;AAAA,EAGE;AAAA,OACK;AAOA,SAAS,aACd,MAMA;AAlBF;AAoBE,QAAM,UAAQ,UAAK,UAAL,mBAAY,UAAS,KAAK,QAAQ;AAEhD,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,MACL,YAAY,EAAE,OAAO,QAAW,YAAY,OAAU;AAAA,MACtD,cAAc,CAAC;AAAA,IACjB;AAAA,EACF;AAEA,QAAM,eAA6C,CAAC;AACpD,QAAM,eAAuB,CAAC;AAE9B,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,SAAS,oBAAoB;AACpC,mBAAa,KAAK,EAAE,MAAM,oBAAoB,KAAK,CAAC;AAAA,IACtD,OAAO;AACL,mBAAa,KAAK;AAAA,QAChB,UAAU;AAAA,UACR,MAAM,KAAK;AAAA,UACX,aAAa,KAAK;AAAA,UAClB,aAAa;AAAA,YACX,MAAM,KAAK;AAAA,UACb;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,aAAa,KAAK;AAExB,MAAI,cAAc,MAAM;AACtB,WAAO;AAAA,MACL,YAAY,EAAE,OAAO,cAAc,YAAY,OAAU;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,QACL,YAAY,EAAE,OAAO,cAAc,YAAY,EAAE,MAAM,CAAC,EAAE,EAAE;AAAA,QAC5D;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,YAAY,EAAE,OAAO,cAAc,YAAY,EAAE,KAAK,CAAC,EAAE,EAAE;AAAA,QAC3D;AAAA,MACF;AAAA,IACF,KAAK;AAEH,aAAO;AAAA,QACL,YAAY,EAAE,OAAO,QAAW,YAAY,OAAU;AAAA,QACtD;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,YAAY;AAAA,UACV,OAAO;AAAA,UACP,YAAY,EAAE,MAAM,EAAE,MAAM,WAAW,SAAS,EAAE;AAAA,QACpD;AAAA,QACA;AAAA,MACF;AAAA,IACF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,8BAA8B;AAAA,QACtC,eAAe,iCAAiC,gBAAgB;AAAA,MAClE,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;AC3FA;AAAA,EAGE,iCAAAC;AAAA,OACK;AACP,SAAS,yBAAyB;AAQlC,IAAM,iBAAiB,kBAAkB,EAAE,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAE9D,SAAS,6BACd,QACuB;AAjBzB;AAkBE,QAAM,SAAS,gBAAgB,MAAM;AAErC,MAAI,SAA6B;AACjC,QAAM,WAA8C,CAAC;AAErD,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,UAAM,cAAc,MAAM,OAAO,SAAS;AAC1C,UAAM,OAAO,MAAM;AAEnB,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,YAAI,SAAS,SAAS,GAAG;AACvB,gBAAM,IAAIA,+BAA8B;AAAA,YACtC,eACE;AAAA,UACJ,CAAC;AAAA,QACH;AAEA,iBAAS,MAAM,SAAS,IAAI,CAAC,EAAE,QAAQ,MAAM,OAAO,EAAE,KAAK,IAAI;AAC/D;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AAEX,cAAM,iBAAgD,CAAC;AAEvD,mBAAW,WAAW,MAAM,UAAU;AACpC,gBAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,kBAAQ,MAAM;AAAA,YACZ,KAAK,QAAQ;AACX,uBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,sBAAM,OAAO,QAAQ,CAAC;AAEtB,wBAAQ,KAAK,MAAM;AAAA,kBACjB,KAAK,QAAQ;AACX,mCAAe,KAAK;AAAA,sBAClB,MAAM,KAAK;AAAA,oBACb,CAAC;AACD;AAAA,kBACF;AAAA,kBACA,KAAK,SAAS;AACZ,wBAAI,KAAK,iBAAiB,KAAK;AAE7B,4BAAM,IAAIA,+BAA8B;AAAA,wBACtC,eAAe;AAAA,sBACjB,CAAC;AAAA,oBACH;AAEA,mCAAe,KAAK;AAAA,sBAClB,OAAO;AAAA,wBACL,SAAQ,gBAAK,aAAL,mBAAe,MAAM,SAArB,mBAA4B;AAAA,wBACpC,QAAQ;AAAA,0BACN,QAAO,UAAK,UAAL,YAAe,KAAK;AAAA,wBAC7B;AAAA,sBACF;AAAA,oBACF,CAAC;AAED;AAAA,kBACF;AAAA,kBACA,KAAK,QAAQ;AACX,wBAAI,KAAK,gBAAgB,KAAK;AAE5B,4BAAM,IAAIA,+BAA8B;AAAA,wBACtC,eAAe;AAAA,sBACjB,CAAC;AAAA,oBACH;AAEA,mCAAe,KAAK;AAAA,sBAClB,UAAU;AAAA,wBACR,SAAQ,gBAAK,aAAL,mBAAe;AAAA,0BACrB;AAAA,8BADM,mBAEJ;AAAA,wBACJ,MAAM,eAAe;AAAA,wBACrB,QAAQ;AAAA,0BACN,OAAO,OAAO,KAAK,KAAK,MAAM,QAAQ;AAAA,wBACxC;AAAA,sBACF;AAAA,oBACF,CAAC;AAED;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAEA;AAAA,YACF;AAAA,YACA,KAAK,QAAQ;AACX,uBAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACvC,sBAAM,OAAO,QAAQA,EAAC;AAEtB,+BAAe,KAAK;AAAA,kBAClB,YAAY;AAAA,oBACV,WAAW,KAAK;AAAA,oBAChB,SAAS,CAAC,EAAE,MAAM,KAAK,UAAU,KAAK,MAAM,EAAE,CAAC;AAAA,kBACjD;AAAA,gBACF,CAAC;AAAA,cACH;AAEA;AAAA,YACF;AAAA,YACA,SAAS;AACP,oBAAM,mBAA0B;AAChC,oBAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,YACzD;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,QAAQ,SAAS,eAAe,CAAC;AAEvD;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAEhB,cAAM,iBAAqD,CAAC;AAE5D,iBAAS,IAAI,GAAG,IAAI,MAAM,SAAS,QAAQ,KAAK;AAC9C,gBAAM,UAAU,MAAM,SAAS,CAAC;AAChC,gBAAM,gBAAgB,MAAM,MAAM,SAAS,SAAS;AACpD,gBAAM,EAAE,QAAQ,IAAI;AAEpB,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,kBAAM,OAAO,QAAQ,CAAC;AACtB,kBAAM,oBAAoB,MAAM,QAAQ,SAAS;AAEjD,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,+BAAe,KAAK;AAAA,kBAClB;AAAA;AAAA;AAAA;AAAA,oBAIE,eAAe,iBAAiB,oBAC5B,KAAK,KAAK,KAAK,IACf,KAAK;AAAA;AAAA,gBACb,CAAC;AACD;AAAA,cACF;AAAA,cAEA,KAAK,aAAa;AAChB,+BAAe,KAAK;AAAA,kBAClB,SAAS;AAAA,oBACP,WAAW,KAAK;AAAA,oBAChB,MAAM,KAAK;AAAA,oBACX,OAAO,KAAK;AAAA,kBACd;AAAA,gBACF,CAAC;AACD;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,aAAa,SAAS,eAAe,CAAC;AAE5D;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAeA,SAAS,gBACP,QACiD;AACjD,QAAM,SAA0D,CAAC;AACjE,MAAI,eACF;AAEF,aAAW,WAAW,QAAQ;AAC5B,UAAM,EAAE,KAAK,IAAI;AACjB,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,aAAI,6CAAc,UAAS,UAAU;AACnC,yBAAe,EAAE,MAAM,UAAU,UAAU,CAAC,EAAE;AAC9C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,KAAK,aAAa;AAChB,aAAI,6CAAc,UAAS,aAAa;AACtC,yBAAe,EAAE,MAAM,aAAa,UAAU,CAAC,EAAE;AACjD,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,aAAI,6CAAc,UAAS,QAAQ;AACjC,yBAAe,EAAE,MAAM,QAAQ,UAAU,CAAC,EAAE;AAC5C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,aAAI,6CAAc,UAAS,QAAQ;AACjC,yBAAe,EAAE,MAAM,QAAQ,UAAU,CAAC,EAAE;AAC5C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AC9PO,SAAS,uBACd,cAC6B;AAC7B,UAAQ,cAAc;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;AHaO,IAAM,2BAAN,MAA0D;AAAA,EAW/D,YACE,SACA,UACA,QACA;AAdF,SAAS,uBAAuB;AAChC,SAAS,WAAW;AACpB,SAAS,8BAA8B;AACvC,SAAS,oBAAoB;AAY3B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EAEQ,QAAQ;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAGE;AAvEJ;AAwEI,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QAAI,oBAAoB,MAAM;AAC5B,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,mBAAmB,MAAM;AAC3B,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,WAAW,MAAM;AACnB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,kBAAkB,QAAQ,eAAe,SAAS,QAAQ;AAC5D,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,EAAE,QAAQ,SAAS,IAAI,6BAA6B,MAAM;AAEhE,UAAM,WAAiC;AAAA,MACrC,SAAS,KAAK;AAAA,MACd,QAAQ,SAAS,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;AAAA,MACtC,8BAA8B,KAAK,SAAS;AAAA,MAC5C,iBAAiB;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,MACA,kBAAiB,0DAAkB,YAAlB,mBAA2B;AAAA,IAI9C;AAEA,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,cAAM,EAAE,YAAY,aAAa,IAAI,aAAa,IAAI;AACtD,eAAO;AAAA,UACL,SAAS;AAAA,YACP,GAAG;AAAA,YACH,KAAI,gBAAW,UAAX,mBAAkB,UAAS,EAAE,WAAW,IAAI,CAAC;AAAA,UACnD;AAAA,UACA,UAAU,CAAC,GAAG,UAAU,GAAG,YAAY;AAAA,QACzC;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,cAAM,IAAIC,+BAA8B;AAAA,UACtC,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,SAAS;AAAA,YACP,GAAG;AAAA,YACH,YAAY;AAAA,cACV,OAAO;AAAA,gBACL;AAAA,kBACE,UAAU;AAAA,oBACR,MAAM,KAAK,KAAK;AAAA,oBAChB,aAAa,KAAK,KAAK;AAAA,oBACvB,aAAa;AAAA,sBACX,MAAM,KAAK,KAAK;AAAA,oBAClB;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA,YAAY,EAAE,MAAM,EAAE,MAAM,KAAK,KAAK,KAAK,EAAE;AAAA,YAC/C;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,WACJ,SAC6D;AA5LjE;AA6LI,UAAM,EAAE,SAAS,SAAS,IAAI,KAAK,QAAQ,OAAO;AAElD,UAAM,WAAW,MAAM,KAAK,OAAO,OAAO;AAAA,MACxC,IAAI,gBAAgB,OAAO;AAAA,IAC7B;AAEA,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,UAAM,mBAAmB,SAAS,QAC9B,EAAE,SAAS,EAAE,OAAO,SAAS,MAAoB,EAAE,IACnD;AAEJ,WAAO;AAAA,MACL,OACE,gCAAS,WAAT,mBAAiB,YAAjB,mBAA0B,YAA1B,mBACI,IAAI,UAAK;AA5MrB,YAAAC;AA4MwB,gBAAAA,MAAA,KAAK,SAAL,OAAAA,MAAa;AAAA,SAC1B,KAAK,QAFR,YAEe;AAAA,MACjB,YAAW,gCAAS,WAAT,mBAAiB,YAAjB,mBAA0B,YAA1B,mBACP,OAAO,UAAQ,CAAC,CAAC,KAAK,aADf,mBAEP,IAAI,UAAK;AAhNnB,YAAAA,KAAAC,KAAAC,KAAAC,KAAAC,KAAAC;AAgNuB;AAAA,UACb,cAAc;AAAA,UACd,aAAYJ,OAAAD,MAAA,KAAK,YAAL,gBAAAA,IAAc,cAAd,OAAAC,MAA2B,KAAK,OAAO,WAAW;AAAA,UAC9D,WAAUE,OAAAD,MAAA,KAAK,YAAL,gBAAAA,IAAc,SAAd,OAAAC,MAAsB,QAAQ,KAAK,OAAO,WAAW,CAAC;AAAA,UAChE,MAAM,KAAK,WAAUE,OAAAD,MAAA,KAAK,YAAL,gBAAAA,IAAc,UAAd,OAAAC,MAAuB,EAAE;AAAA,QAChD;AAAA;AAAA,MACF,cAAc,uBAAuB,SAAS,UAAU;AAAA,MACxD,OAAO;AAAA,QACL,eAAc,oBAAS,UAAT,mBAAgB,gBAAhB,YAA+B,OAAO;AAAA,QACpD,mBAAkB,oBAAS,UAAT,mBAAgB,iBAAhB,YAAgC,OAAO;AAAA,MAC3D;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,SAAS,SAAS,IAAI,KAAK,QAAQ,OAAO;AAElD,UAAM,WAAW,MAAM,KAAK,OAAO,OAAO;AAAA,MACxC,IAAI,sBAAsB,OAAO;AAAA,IACnC;AAEA,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,QAAI,eAA4C;AAChD,QAAI,QAA4D;AAAA,MAC9D,cAAc,OAAO;AAAA,MACrB,kBAAkB,OAAO;AAAA,IAC3B;AACA,QAAI,mBACF;AAEF,QAAI,CAAC,SAAS,QAAQ;AACpB,YAAM,IAAI,MAAM,iBAAiB;AAAA,IACnC;AAEA,UAAM,SAAS,IAAI,eAAoB;AAAA,MACrC,MAAM,MAAM,YAAY;AACtB,yBAAiB,SAAS,SAAS,QAAS;AAC1C,qBAAW,QAAQ,EAAE,SAAS,MAAM,OAAO,MAAM,CAAC;AAAA,QACpD;AACA,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF,CAAC;AAED,UAAM,wBAOF,CAAC;AAEL,WAAO;AAAA,MACL,QAAQ,OAAO;AAAA,QACb,IAAI,gBAGF;AAAA,UACA,UAAU,OAAO,YAAY;AAhRvC;AAiRY,qBAAS,aAAa,OAAc;AAClC,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,MAAM,CAAC;AAAA,YAC7C;AAGA,gBAAI,CAAC,MAAM,SAAS;AAClB,2BAAa,MAAM,KAAK;AACxB;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAGpB,gBAAI,MAAM,yBAAyB;AACjC,2BAAa,MAAM,uBAAuB;AAC1C;AAAA,YACF;AACA,gBAAI,MAAM,2BAA2B;AACnC,2BAAa,MAAM,yBAAyB;AAC5C;AAAA,YACF;AACA,gBAAI,MAAM,qBAAqB;AAC7B,2BAAa,MAAM,mBAAmB;AACtC;AAAA,YACF;AACA,gBAAI,MAAM,qBAAqB;AAC7B,2BAAa,MAAM,mBAAmB;AACtC;AAAA,YACF;AAEA,gBAAI,MAAM,aAAa;AACrB,6BAAe;AAAA,gBACb,MAAM,YAAY;AAAA,cACpB;AAAA,YACF;AAEA,gBAAI,MAAM,UAAU;AAClB,sBAAQ;AAAA,gBACN,eAAc,iBAAM,SAAS,UAAf,mBAAsB,gBAAtB,YAAqC,OAAO;AAAA,gBAC1D,mBACE,iBAAM,SAAS,UAAf,mBAAsB,iBAAtB,YAAsC,OAAO;AAAA,cACjD;AAEA,kBAAI,MAAM,SAAS,OAAO;AACxB,mCAAmB;AAAA,kBACjB,SAAS;AAAA,oBACP,OAAO,MAAM,SAAS;AAAA,kBACxB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,iBAAI,iBAAM,sBAAN,mBAAyB,UAAzB,mBAAgC,MAAM;AACxC,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,WAAW,MAAM,kBAAkB,MAAM;AAAA,cAC3C,CAAC;AAAA,YACH;AAEA,kBAAM,oBAAoB,MAAM;AAChC,kBAAI,4DAAmB,UAAnB,mBAA0B,YAAW,MAAM;AAC7C,oBAAM,UAAU,kBAAkB,MAAM;AACxC,oCAAsB,kBAAkB,iBAAkB,IAAI;AAAA,gBAC5D,YAAY,QAAQ;AAAA,gBACpB,UAAU,QAAQ;AAAA,gBAClB,UAAU;AAAA,cACZ;AAAA,YACF;AAEA,kBAAM,oBAAoB,MAAM;AAChC,iBAAI,4DAAmB,UAAnB,mBAA0B,SAAS;AACrC,oBAAM,eACJ,sBAAsB,kBAAkB,iBAAkB;AAC5D,oBAAM,SAAQ,uBAAkB,MAAM,QAAQ,UAAhC,YAAyC;AAEvD,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,cAAc;AAAA,gBACd,YAAY,aAAa;AAAA,gBACzB,UAAU,aAAa;AAAA,gBACvB,eAAe;AAAA,cACjB,CAAC;AAED,2BAAa,YAAY;AAAA,YAC3B;AAEA,kBAAM,mBAAmB,MAAM;AAC/B,gBAAI,oBAAoB,MAAM;AAC5B,oBAAM,QAAQ,iBAAiB;AAC/B,oBAAM,eAAe,sBAAsB,KAAK;AAGhD,kBAAI,gBAAgB,MAAM;AACxB,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,cAAc;AAAA,kBACd,YAAY,aAAa;AAAA,kBACzB,UAAU,aAAa;AAAA,kBACvB,MAAM,aAAa;AAAA,gBACrB,CAAC;AAED,uBAAO,sBAAsB,KAAK;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AAAA,UAEA,MAAM,YAAY;AAChB,uBAAW,QAAQ;AAAA,cACjB,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AACF;;;AIrYA;AAAA,EAEE;AAAA,OACK;AAQA,IAAM,wBAAN,MAAgE;AAAA,EASrE,YACE,SACA,UACA,QACA;AAZF,SAAS,uBAAuB;AAEhC,SAAS,WAAW;AACpB,SAAS,uBAAuB;AAChC,SAAS,wBAAwB;AAS/B,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,MAAM,QAAQ;AAAA,IACZ;AAAA,EACF,GAEgC;AAC9B,UAAM,KAAK,OAAO,cAAsB;AACtC,YAAM,UAAU;AAAA,QACd;AAAA,QACA,YAAY,KAAK,SAAS;AAAA,QAC1B,WAAW,KAAK,SAAS;AAAA,MAC3B;AAEA,YAAM,UAAU,IAAI,mBAAmB;AAAA,QACrC,aAAa;AAAA,QACb,MAAM,KAAK,UAAU,OAAO;AAAA,QAC5B,SAAS,KAAK;AAAA,MAChB,CAAC;AACD,YAAM,cAAc,MAAM,KAAK,OAAO,OAAO,KAAK,OAAO;AAEzD,YAAM,SAAS,KAAK,MAAM,IAAI,YAAY,EAAE,OAAO,YAAY,IAAI,CAAC;AAEpE,aAAO;AAAA,IACT;AAEA,UAAM,YAAY,MAAM,QAAQ,IAAI,OAAO,IAAI,EAAE,CAAC;AAElD,UAAM,WAAW,UAAU;AAAA,MACzB,CAAC,KAAK,MAAM;AACV,YAAI,WAAW,KAAK,EAAE,SAAS;AAC/B,YAAI,MAAM,UAAU,EAAE;AACtB,eAAO;AAAA,MACT;AAAA,MACA,EAAE,YAAY,CAAC,GAAG,OAAO,EAAE,QAAQ,EAAE,EAAE;AAAA,IACzC;AAEA,WAAO;AAAA,EACT;AACF;;;ALVO,SAAS,oBACd,UAAyC,CAAC,GACnB;AACvB,QAAM,6BAA6B,MAAG;AAjExC;AAkEI,eAAIC;AAAA,OACF,aAAQ,mBAAR,YAA0B;AAAA,QACxB,QAAQ,YAAY;AAAA,UAClB,cAAc,QAAQ;AAAA,UACtB,aAAa;AAAA,UACb,yBAAyB;AAAA,UACzB,aAAa;AAAA,QACf,CAAC;AAAA,QACD,aAAa;AAAA,UACX,aAAa,YAAY;AAAA,YACvB,cAAc,QAAQ;AAAA,YACtB,aAAa;AAAA,YACb,yBAAyB;AAAA,YACzB,aAAa;AAAA,UACf,CAAC;AAAA,UACD,iBAAiB,YAAY;AAAA,YAC3B,cAAc,QAAQ;AAAA,YACtB,aAAa;AAAA,YACb,yBAAyB;AAAA,YACzB,aAAa;AAAA,UACf,CAAC;AAAA,UACD,cAAc,oBAAoB;AAAA,YAChC,cAAc,QAAQ;AAAA,YACtB,yBAAyB;AAAA,UAC3B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA;AAEF,QAAM,kBAAkB,CACtB,SACA,WAAgC,CAAC,MAEjC,IAAI,yBAAyB,SAAS,UAAU;AAAA,IAC9C,QAAQ,2BAA2B;AAAA,IACnC;AAAA,EACF,CAAC;AAEH,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,QAAM,uBAAuB,CAC3B,SACA,WAAqC,CAAC,MAEtC,IAAI,sBAAsB,SAAS,UAAU;AAAA,IAC3C,QAAQ,2BAA2B;AAAA,EACrC,CAAC;AAEH,WAAS,gBAAgB;AACzB,WAAS,YAAY;AACrB,WAAS,gBAAgB;AACzB,WAAS,qBAAqB;AAE9B,SAAO;AACT;AAKO,IAAM,UAAU,oBAAoB;", "names": ["BedrockRuntimeClient", "UnsupportedFunctionalityError", "UnsupportedFunctionalityError", "i", "UnsupportedFunctionalityError", "_a", "_b", "_c", "_d", "_e", "_f", "BedrockRuntimeClient"]}