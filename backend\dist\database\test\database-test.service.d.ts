import { DatabaseService } from '../database.service';
import { UserDataAccessService } from '../services/user-data-access.service';
import { DreamDataAccessService } from '../services/dream-data-access.service';
import { AgentDataAccessService } from '../services/agent-data-access.service';
import { ConversationDataAccessService } from '../services/conversation-data-access.service';
export declare class DatabaseTestService {
    private databaseService;
    private userDataAccess;
    private dreamDataAccess;
    private agentDataAccess;
    private conversationDataAccess;
    private readonly logger;
    constructor(databaseService: DatabaseService, userDataAccess: UserDataAccessService, dreamDataAccess: DreamDataAccessService, agentDataAccess: AgentDataAccessService, conversationDataAccess: ConversationDataAccessService);
    runDatabaseTests(): Promise<{
        success: boolean;
        results: any[];
        errors: any[];
    }>;
    private testDatabaseConnection;
    private testAgentPermissions;
    private testUserOperations;
    private testAgentConfiguration;
    getDatabaseStats(): Promise<any>;
}
