"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const rag_service_1 = require("./rag.service");
const knowledge_data_access_service_1 = require("../database/services/knowledge-data-access.service");
let KnowledgeController = class KnowledgeController {
    constructor(ragService, knowledgeDataAccess) {
        this.ragService = ragService;
        this.knowledgeDataAccess = knowledgeDataAccess;
    }
    async createKnowledgeBase(request, req) {
        try {
            const userId = req?.user?.id;
            const knowledgeBase = await this.knowledgeDataAccess.createKnowledgeBase(request, userId);
            return {
                success: true,
                data: knowledgeBase,
                message: '知识库创建成功',
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getKnowledgeBases(agentType, includePublic, req) {
        try {
            const userId = req?.user?.id;
            const includePublicFlag = includePublic !== 'false';
            const knowledgeBases = await this.knowledgeDataAccess.getKnowledgeBases(agentType, userId, includePublicFlag);
            return {
                success: true,
                data: knowledgeBases,
                count: knowledgeBases.length,
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getKnowledgeBase(id) {
        try {
            const knowledgeBase = await this.knowledgeDataAccess.getKnowledgeBaseById(id);
            return {
                success: true,
                data: knowledgeBase,
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.NOT_FOUND);
        }
    }
    async deleteKnowledgeBase(id, req) {
        try {
            const userId = req?.user?.id;
            await this.knowledgeDataAccess.deleteKnowledgeBase(id, userId);
            return {
                success: true,
                message: '知识库删除成功',
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async createKnowledgeEntry(request, req) {
        try {
            const userId = req?.user?.id;
            const entry = await this.ragService.addKnowledgeEntry(request, userId);
            return {
                success: true,
                data: entry,
                message: '知识条目创建成功',
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async createKnowledgeEntriesBatch(entries, req) {
        try {
            const userId = req?.user?.id;
            const results = await this.ragService.addKnowledgeEntriesBatch(entries, userId);
            return {
                success: true,
                data: results,
                message: `批量创建完成，成功 ${results.length}/${entries.length} 个条目`,
                success_count: results.length,
                total_count: entries.length,
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getKnowledgeEntries(knowledgeBaseId, limit, offset) {
        try {
            const limitNum = limit ? parseInt(limit) : 50;
            const offsetNum = offset ? parseInt(offset) : 0;
            const entries = await this.knowledgeDataAccess.getKnowledgeEntries(knowledgeBaseId, limitNum, offsetNum);
            return {
                success: true,
                data: entries,
                count: entries.length,
                limit: limitNum,
                offset: offsetNum,
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateKnowledgeEntry(id, request) {
        try {
            const entry = await this.knowledgeDataAccess.updateKnowledgeEntry(id, request);
            return {
                success: true,
                data: entry,
                message: '知识条目更新成功',
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async deleteKnowledgeEntry(id) {
        try {
            await this.knowledgeDataAccess.deleteKnowledgeEntry(id);
            return {
                success: true,
                message: '知识条目删除成功',
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async searchKnowledge(request, req) {
        try {
            const userId = req?.user?.id;
            const results = await this.ragService.searchKnowledge(request, userId);
            return {
                success: true,
                data: results,
                count: results.length,
                query: request.query,
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async performRAG(body, req) {
        try {
            const userId = req?.user?.id;
            const { query, agent_type, config } = body;
            const response = await this.ragService.performRAG(query, agent_type, userId, config);
            return {
                success: true,
                data: response,
                query,
                agent_type,
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async reindexKnowledgeBase(id) {
        try {
            await this.ragService.reindexKnowledgeBase(id);
            return {
                success: true,
                message: '知识库重新索引完成',
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUsageStats(agentType, days, req) {
        try {
            const userId = req?.user?.id;
            const daysNum = days ? parseInt(days) : 30;
            const stats = await this.knowledgeDataAccess.getUsageStats(agentType, userId, daysNum);
            return {
                success: true,
                data: stats,
                agent_type: agentType,
                days: daysNum,
            };
        }
        catch (error) {
            throw new common_1.HttpException({ success: false, message: error.message }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.KnowledgeController = KnowledgeController;
__decorate([
    (0, common_1.Post)('bases'),
    (0, swagger_1.ApiOperation)({ summary: '创建知识库' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '知识库创建成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "createKnowledgeBase", null);
__decorate([
    (0, common_1.Get)('bases'),
    (0, swagger_1.ApiOperation)({ summary: '获取知识库列表' }),
    (0, swagger_1.ApiQuery)({ name: 'agent_type', required: false, enum: ['almighty', 'taoist', 'freud', 'papa', 'shared'] }),
    (0, swagger_1.ApiQuery)({ name: 'include_public', required: false, type: Boolean }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)('agent_type')),
    __param(1, (0, common_1.Query)('include_public')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "getKnowledgeBases", null);
__decorate([
    (0, common_1.Get)('bases/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取单个知识库详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "getKnowledgeBase", null);
__decorate([
    (0, common_1.Delete)('bases/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除知识库' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "deleteKnowledgeBase", null);
__decorate([
    (0, common_1.Post)('entries'),
    (0, swagger_1.ApiOperation)({ summary: '添加知识条目' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '知识条目创建成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "createKnowledgeEntry", null);
__decorate([
    (0, common_1.Post)('entries/batch'),
    (0, swagger_1.ApiOperation)({ summary: '批量添加知识条目' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '批量创建完成' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "createKnowledgeEntriesBatch", null);
__decorate([
    (0, common_1.Get)('bases/:knowledgeBaseId/entries'),
    (0, swagger_1.ApiOperation)({ summary: '获取知识条目列表' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'offset', required: false, type: Number }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('knowledgeBaseId')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('offset')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "getKnowledgeEntries", null);
__decorate([
    (0, common_1.Put)('entries/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新知识条目' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "updateKnowledgeEntry", null);
__decorate([
    (0, common_1.Delete)('entries/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除知识条目' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "deleteKnowledgeEntry", null);
__decorate([
    (0, common_1.Post)('search'),
    (0, swagger_1.ApiOperation)({ summary: '知识库语义搜索' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '搜索完成' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "searchKnowledge", null);
__decorate([
    (0, common_1.Post)('rag'),
    (0, swagger_1.ApiOperation)({ summary: '执行RAG检索增强生成' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'RAG处理完成' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "performRAG", null);
__decorate([
    (0, common_1.Post)('bases/:id/reindex'),
    (0, swagger_1.ApiOperation)({ summary: '重新索引知识库' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '重新索引完成' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "reindexKnowledgeBase", null);
__decorate([
    (0, common_1.Get)('stats/usage'),
    (0, swagger_1.ApiOperation)({ summary: '获取知识库使用统计' }),
    (0, swagger_1.ApiQuery)({ name: 'agent_type', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'days', required: false, type: Number }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)('agent_type')),
    __param(1, (0, common_1.Query)('days')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeController.prototype, "getUsageStats", null);
exports.KnowledgeController = KnowledgeController = __decorate([
    (0, swagger_1.ApiTags)('Knowledge Base'),
    (0, common_1.Controller)('knowledge'),
    __metadata("design:paramtypes", [rag_service_1.RAGService,
        knowledge_data_access_service_1.KnowledgeDataAccessService])
], KnowledgeController);
//# sourceMappingURL=knowledge.controller.js.map