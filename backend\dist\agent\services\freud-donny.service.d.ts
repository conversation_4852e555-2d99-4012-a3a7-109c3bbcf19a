import { BaseDonnyService, DreamInterpretationRequest, DreamInterpretationResponse, AgentPersona } from './base-donny.service';
import { AgentRuntimeService, AgentMessage, AgentResponse } from './agent-runtime.service';
import { ConversationDataAccessService } from '../../database/services/conversation-data-access.service';
import { AgentDataAccessService } from '../../database/services/agent-data-access.service';
import { RAGService } from '../../knowledge/rag.service';
export declare class FreudDonnyService extends BaseDonnyService {
    protected agentId: string;
    protected agentType: "freud";
    protected persona: Agent<PERSON>ersona;
    constructor(agentRuntimeService: AgentRuntimeService, conversationDataService: ConversationDataAccessService, agentDataService: AgentDataAccessService, ragService: RAGService);
    protected analyzeWithKnowledge(request: DreamInterpretationRequest, knowledgeResults: any[]): Promise<string>;
    protected generatePersonalizedResponse(request: DreamInterpretationRequest, interpretation: string, knowledgeResults: any[]): Promise<DreamInterpretationResponse>;
    protected handleGeneralConversation(message: AgentMessage, context?: any): Promise<AgentResponse>;
    private analyzeManifestContent;
    private analyzeLatentContent;
    private analyzeDreamWork;
    private extractPsychoanalyticalInsights;
    private generatePsychoanalyticalInsights;
    private generateTherapeuticSupport;
    private generatePsychologicalAdvice;
    private calculateConfidence;
}
