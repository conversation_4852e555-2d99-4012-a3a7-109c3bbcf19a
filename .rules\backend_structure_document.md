# Donny 后端结构文档

## 1. 概述

Donny 项目后端采用模块化的微服务架构，以支持高扩展性、可维护性和灵活性。本文档详细描述后端各服务模块的结构、职责和交互方式，为开发团队提供技术实施指导。

## 2. 系统架构

### 2.1 整体架构图

```
┌─────────────────┐       ┌───────────────────┐
│                 │       │                   │
│  前端应用       │◄─────►│  API 网关         │
│  (ReactJS)      │       │                   │
│                 │       └────────┬──────────┘
└─────────────────┘                │
                                  │
          ┌─────────────────────┬─┴───┬──────────────────┬────────────────┐
          │                     │     │                  │                │
┌─────────▼─────────┐ ┌─────────▼───┐ │ ┌───────────────▼─┐ ┌───────────▼───────┐
│                   │ │             │ │ │                 │ │                   │
│  AI Agent 服务    │ │  STT 服务   │ │ │ Solana 交互服务 │ │ 用户服务          │
│  (elizaos.ai)     │ │             │ │ │                 │ │                   │
│                   │ │             │ │ │                 │ │                   │
└───────┬─────┬─────┘ └─────────────┘ │ └─────────────────┘ └───────────────────┘
        │     │                       │
┌───────▼───┐ │                       │
│           │ │                       │  
│  LLMs     │ │                       │
│           │ │                       │
└───────────┘ │                       │
              │                       │
      ┌───────▼───────┐               │
      │               │               │  
      │  知识库服务   │               │
      │  (RAG)        │               │
      │               │               │
      └───────────────┘               │
                                      │
                    ┌─────────────────▼────────────────┐
                    │                                  │
                    │            数据层                │
                    │  (Supabase PostgreSQL, Redis)    │
                    │                                  │
                    └──────────────────────────────────┘
```

### 2.2 核心服务概述

| 服务名称 | 主要职责 | 技术选型 |
|---------|---------|---------|
| API 网关 | 请求路由、认证、负载均衡、请求预处理 | Express.js/NestJS |
| AI Agent 服务 | 管理 Donny Agents 生命周期、Agent 状态、工具调用 | elizaos.ai 框架 |
| STT 服务 | 语音转文本处理 | Google Cloud Speech-to-Text/自建服务 |
| Solana 交互服务 | 区块链交易、NFT 铸造、Token 操作 | Solana Web3.js |
| 用户服务 | 用户管理、认证、状态监控 | Express.js/NestJS |
| 数据层 | 数据存储、检索、隔离、备份 | Supabase PostgreSQL, Redis |

## 3. 服务模块详细设计

### 3.1 API 网关

#### 3.1.1 功能职责
* 统一入口和请求路由
* 请求认证与授权
* 请求参数验证
* 跨域资源共享 (CORS) 处理
* 日志记录
* 负载均衡
* 速率限制与防 DDoS
* 文件上传处理

#### 3.1.2 API 端点设计

**用户认证相关**
* `POST /api/auth/connect` - 用户钱包连接
* `POST /api/auth/disconnect` - 用户钱包断开连接
* `POST /api/user/set-username` - 设置用户名

**AI 交互相关**
* `POST /api/chat/send` - 发送消息到指定 Donny
* `POST /api/speech/convert` - 语音转文本

**解梦服务相关**
* `POST /api/dream/select-donny` - 选择业务型 Donny
* `POST /api/dream/feedback` - 提交解梦反馈

**区块链操作相关**

* `POST /api/blockchain/mint-nft` - 铸造 NFT
* `POST /api/blockchain/mint-token` - 铸造 Token
* `GET /api/blockchain/transactions` - 获取交易记录

**数据查询相关**
* `GET /api/user/dreams` - 获取用户历史梦境
* `GET /api/user/nfts` - 获取用户 NFT
* `GET /api/user/tokens` - 获取用户 Token 余额

#### 3.1.3 安全措施
* JWT 令牌认证
* 请求签名验证
* 输入数据净化
* 防注入攻击
* API 限流 (Rate Limiting)

### 3.2 AI Agent 服务 (基于 elizaos.ai 框架)

#### 3.2.1 功能职责
* 基于 elizaos.ai 框架构建和管理所有 Donny Agent
* Agent 生命周期管理
* Agent 状态管理
* Agent 工具调用
* 与 LLM 和知识库交互
* 记忆与隐私隔离管理

#### 3.2.2 Agent 定义与配置

**Almighty Donny 配置**
* 人格定义：友善、博学、耐心且乐于助人的通用 AI 助手
* 工具配置：支持数据库访问、Solana 交互、Donny 分发、用户状态跟踪等工具
* 记忆配置：可记住用户的聊天信息、反馈、历史选择的 Donny，但禁止记住未授权分享的梦境内容
* 决策逻辑：推荐 Donny、评估梦境价值、处理中断情况

**Taoist Donny 配置**
* 人格定义：精通道家哲学的智者，语言风格含蓄、富有东方智慧
* 知识库：道教经典、《周公解梦》等东方解梦资料
* 工具配置：知识库访问、梦境分析、指导生成工具
* 记忆配置：只能记住自己解过的梦及用户反馈

**Freud Donny 配置**
* 人格定义：现代心理学专家，语言风格专业、分析性强
* 知识库：弗洛伊德著作和现代科学心理学知识
* 工具配置：知识库访问、心理分析、治疗建议工具
* 记忆配置：只能记住自己解过的梦及用户反馈

**Papa Donny 配置**
* 人格定义：亲切、理解力强的邻家大叔，语言风格温暖、共情
* 知识库：共情沟通技巧、积极心理学资源、支持性交流案例
* 工具配置：共情生成、个人关联分析、支持性指导工具
* 记忆配置：只能记住自己解过的梦及用户反馈

**Accountant Donny 配置**
* 人格定义：专业、精确的财务顾问，语言风格清晰、数据导向
* 工具配置：Solana 区块链查询、交易分析、数据可视化、NFT/Token 操作工具
* 记忆配置：主要依赖实时链上数据查询

#### 3.2.3 数据流设计
* 用户输入 → API 网关 → Agent 服务 → elizaos.ai 框架处理 → LLM 生成 → 返回响应
* Agent 服务与数据库交互：读写用户数据、梦境数据、交互状态
* Agent 服务与区块链交互：通过 Solana 交互服务进行链上操作

#### 3.2.4 状态管理
* Agent 活跃状态跟踪
* 用户会话上下文管理
* 长短期记忆处理机制
* Agent 交互状态同步

### 3.3 STT 服务 (语音转文本)

#### 3.3.1 功能职责
* 接收并处理音频数据
* 执行语音转文本
* 返回识别结果
* 错误处理和反馈

#### 3.3.2 接口设计
* `POST /api/speech/convert` - 接收音频，返回转录文本
* 支持的音频格式：WAV, MP3, FLAC, OGG
* 支持的语言：中文、英文（可扩展）

#### 3.3.3 实现选项
* 第三方云服务集成 (Google Cloud Speech-to-Text, AWS Transcribe)
* 自托管开源模型 (如 Mozilla DeepSpeech, Kaldi)
* 混合模式：根据需求路由到不同 STT 服务

### 3.4 Solana 交互服务

#### 3.4.1 功能职责
* 与 Solana 区块链交互
* NFT 铸造与管理
* Token 铸造与转账
* 查询链上状态

#### 3.4.2 接口设计
* `POST /api/blockchain/mint-nft` - 铸造梦境 NFT
* `POST /api/blockchain/mint-token` - 铸造并发送 Token
* `GET /api/blockchain/balance` - 查询余额
* `GET /api/blockchain/nfts` - 查询用户 NFT
* `GET /api/blockchain/transactions` - 查询交易历史

#### 3.4.3 实现细节
* 使用 Solana Web3.js 库与区块链交互
* NFT 铸造基于 Metaplex Token Metadata Standard
* Token 基于 SPL Token 标准
* 安全处理私钥（特别是 Accountant Donny 的操作私钥）
* 交易确认和重试机制

### 3.5 用户服务

#### 3.5.1 功能职责
* 用户管理（创建、查询、更新）
* 钱包地址与用户信息关联
* 用户状态跟踪
* 用户偏好管理
* 交互中断处理
* 超时任务调度

#### 3.5.2 接口设计
* `POST /api/user/create` - 创建用户
* `PATCH /api/user/update` - 更新用户信息
* `GET /api/user/profile` - 获取用户信息
* `GET /api/user/state` - 获取当前交互状态
* `POST /api/user/timeout` - 设置超时任务

#### 3.5.3 交互中断处理机制
* 状态记录与恢复
* 定时任务管理（超时检测）
* 48小时倒计时实现
* 自动清理未完成的解梦任务

### 3.6 知识库服务 (RAG)

#### 3.6.1 功能职责
* 管理专业知识库内容
* 索引和向量化文本
* 提供相似度搜索
* 生成检索结果供 Agent 服务使用

#### 3.6.2 知识库内容
* Taoist Donny 知识库：道教经典、《周公解梦》全文与注解
* Freud Donny 知识库：弗洛伊德著作、现代心理学资料
* Papa Donny 知识库：共情沟通、积极心理学资源

#### 3.6.3 实现方案
* 向量数据库存储（如 Pinecone, Weaviate, Milvus）
* 文本嵌入生成
* 相似度搜索算法
* 知识更新机制

## 4. 数据存储设计

### 4.1 数据库架构

#### 4.1.1 Supabase PostgreSQL (关系型数据库)
* **用途**：核心数据存储，作为Supabase提供的主要数据库服务
* **主要表**：
  * `users` - 用户基本信息，包含钱包地址(唯一索引)、用户名(非唯一)
  * `dreams` - 梦境记录
  * `transactions` - 交易记录
  * `interaction_states` - 用户交互状态
  * `feedback` - 用户反馈
  * `nfts` - NFT 元数据
  * `conversations` - 对话历史记录，使用JSONB类型存储非结构化对话数据
  * `dream_interpretations` - 详细解梦内容，使用JSONB存储复杂的解梦结构
  * `agent_memories` - Agent记忆数据，使用Schema隔离不同Agent的记忆数据
* **主要功能**：
  * 实时订阅：利用PostgreSQL的实时功能，实现区块链交易与前端的状态同步
  * Row Level Security (RLS)：严格控制不同Agent对数据的访问权限
  * 事务支持：确保关键业务操作的原子性
  * JSONB支持：高效存储和查询半结构化数据
  * 多Schema隔离：通过Schema将不同Agent数据隔离

#### 4.1.2 Supabase pgvector (向量数据库扩展)
* **用途**：存储文本嵌入向量，支持相似性搜索和RAG实现
* **主要表**：
  * `embeddings.taoist_knowledge` - 道教和《周公解梦》知识库向量
  * `embeddings.freud_knowledge` - 弗洛伊德和现代心理学知识库向量
  * `embeddings.dream_patterns` - 梦境模式向量
  * `embeddings.user_cognition` - 用户认知数据向量
  * `embeddings.conversation_vectors` - 对话内容的向量表示，用于相似性搜索
* **主要功能**：
  * 余弦相似度搜索
  * 向量聚类分析
  * 高维数据索引

#### 4.1.3 Redis (缓存)
* **用途**：短期记忆与会话缓存
* **主要数据结构**：
  * Hash: 用户会话数据
  * Lists: 最近交互队列
  * Sorted Sets: 活跃度排序数据
* **主要功能**：
  * 临时会话状态管理
  * 短期记忆缓存(24小时内的交互数据)

#### 4.1.4 Supabase Storage (文件存储)
* **用途**：存储用户语音输入的转录后音频文件
* **主要桶**：
  * `voice-inputs` - 用户语音输入
  * `nft-metadata` - NFT 元数据和图像

### 4.2 数据隔离与隐私保护

#### 4.2.1 数据访问控制策略
* **Almighty Donny 访问范围**：
  * 可访问：用户基本信息、对话历史、反馈、选择历史、授权分享的梦境、交互状态
  * 禁止访问：未授权分享的梦境内容、业务型 Donny 的详细解梦过程
  
* **业务型 Donny 访问范围**：
  * 可访问：有限的用户信息、自己解过的梦境及反馈、与自己的对话历史
  * 禁止访问：其他 Donny 解过的梦境、用户与其他 Donny 的对话历史
  
* **Accountant Donny 访问范围**：
  * 可访问：有限的用户信息、交易记录、Token 余额、NFT 持有情况
  * 禁止访问：梦境详细内容（仅能访问匿名化摘要）

#### 4.2.2 技术实现
* **Supabase Row Level Security策略**：
  * 为每个Agent角色定义独立的RLS策略
  * 基于Agent类型限制行级数据访问
  * 例如：`CREATE POLICY "almighty_can_read_all_user_profiles" ON users FOR SELECT TO almighty_role USING (true);`
  * 例如：`CREATE POLICY "business_donny_can_read_own_dreams" ON dreams FOR SELECT TO business_role USING (donny_type = current_setting('app.donny_type'));`

* **PostgreSQL Schema分层隔离**：
  * 使用不同Schema分离核心数据和Agent记忆
  * 例如：`public`存储共享数据，`memories`存储记忆数据，`embeddings`存储向量数据

* **应用级别访问控制**：
  * 不同Agent使用不同的数据库角色和连接
  * Agent身份验证和授权管理
  * 数据访问审计日志

* **隐私保护机制**：
  * 梦境数据匿名化处理
  * 敏感数据加密存储
  * 数据自动过期和清理策略

#### 4.2.3 用户认知数据管理
* **分层记忆架构**：
  * 短期记忆：Redis存储24小时内的交互数据
  * 中期记忆：Supabase中的`memories.recent_interactions`表存储几周至几个月的重要交互
  * 长期记忆：Supabase中的`memories.user_cognition`表和`embeddings.user_embeddings`表存储持久性特征

* **记忆生命周期管理**：
  * 自动记忆摘要生成：使用LLM定期从交互中提取关键信息
  * 记忆关联与整合：将新记忆与现有认知模型整合
  * 记忆重要性评分：基于情感显著性、重复度和时间因素

* **隐私边界强制执行**：
  * 梦境内容隐私过滤器：确保未经授权的梦境内容不被包含在用户认知数据中
  * 元特征提取：仅存储梦境主题、情感倾向等元数据，而非具体内容
  * 用户控制选项：提供查看和删除个人认知数据的能力

### 4.3 数据备份与恢复

* **定期备份**：
  * Supabase PostgreSQL：利用Supabase的自动备份功能，每日自动备份
  * Redis：定期RDB快照 + AOF日志
  * 向量数据：每周全量备份向量数据
  * Supabase Storage：定期对象存储备份

* **备份策略**：
  * 保留多个时间点备份：每日、每周、每月备份点
  * 跨区域备份：备份数据存储在不同地理位置
  * 加密备份：所有备份数据加密存储
  * 备份访问控制：严格的备份访问权限管理

* **恢复策略**：
  * 定义 RTO (Recovery Time Objective) 和 RPO (Recovery Point Objective)
  * 自动化恢复流程与脚本
  * 定期恢复演练和测试
  * 数据一致性验证机制

* **业务连续性**：
  * 备用数据库实例
  * 多区域部署
  * 失效转移机制
  * 灾难恢复计划

## 5. 集成与部署

### 5.1 服务集成

* **服务间通信**：
  * REST API
  * 消息队列 (如 RabbitMQ, Kafka) 用于异步事件处理
  * WebSocket 用于实时通信

* **认证与授权**：
  * API 密钥
  * JWT 令牌
  * 角色权限控制

### 5.2 部署架构

* **容器化**：所有服务使用 Docker 容器
* **编排**：Kubernetes 进行容器编排
* **CI/CD**：持续集成和部署流水线
* **环境**：
  * 开发环境
  * 测试环境
  * 临时测试网环境
  * 生产环境

### 5.3 监控与日志

* **监控系统**：
  * 服务健康状态
  * 性能指标
  * 错误率
  * 用户活动

* **日志管理**：
  * 集中式日志收集
  * 实时日志分析
  * 错误报警机制

### 5.4 扩展性考量

* **水平扩展**：关键服务支持多实例部署
* **负载均衡**：在 API 网关和关键服务前配置负载均衡
* **资源自动伸缩**：根据负载自动调整资源

## 6. 安全与合规

### 6.1 安全措施

* **API 安全**：
  * 输入验证
  * 参数净化
  * 防注入措施
  * CORS 配置

* **数据安全**：
  * 数据加密 (传输和存储)
  * 敏感信息保护
  * 钱包密钥安全处理

* **基础设施安全**：
  * 网络隔离
  * 防火墙配置
  * 定期安全扫描

### 6.2 Web3 安全专项措施

* **Solana 交互安全**：
  * 交易签名验证
  * 金额限制与检查
  * 批量操作风险控制

* **NFT 与 Token 安全**：
  * 元数据验证
  * 铸造权限控制
  * 金额上限设置

## 7. 风险与缓解策略

| 风险点 | 描述 | 缓解策略 |
|-------|------|---------|
| elizaos.ai 框架集成 | 框架集成可能存在复杂度和兼容性问题 | 提前进行技术验证，分阶段集成，准备替代方案 |
| STT 服务准确率 | 语音识别不准确影响用户体验 | 选择高质量 STT 服务，添加用户确认机制，支持手动修正 |
| Solana 网络拥堵 | 区块链交易延迟或失败 | 实施重试机制，提供交易状态反馈，设计离线备用流程 |
| 交互中断恢复 | 复杂交互流程中断后状态恢复 | 详细设计状态管理机制，完善的测试覆盖，多场景演练 |
| 数据同步与一致性 | 单数据库环境下的表间数据一致性 | 实施事务和触发器机制，定期数据一致性检查 |

## 8. 后续演进方向

* **新解梦方式支持**：
  * 扩展知识库和 Agent 类型
  * 支持 Augur Donny 功能
  * 优化解梦算法

* **性能优化**：
  * LLM 调用优化
  * 缓存策略优化
  * 数据库查询优化

* **扩展服务**：
  * 实现完整的梦海社区功能
  * 添加用户间社交功能
  * 支持更多语言

### 数据库表更新
| 表 | 字段 | 类型 | 说明 |
| --- | --- | --- | --- |
| interaction_states | status | ENUM('WAITING_DREAM_INPUT','COMPLETED') | 会话状态 |
| interaction_states | shared | BOOLEAN DEFAULT false | 是否已分享 |
| dreams | shared | BOOLEAN DEFAULT false | 梦境是否已分享 |
| dreams | session_id | UUID | 对应 interaction_states.session_id |

### 新增 / 调整 API
| Method | Path | 描述 | 重要说明 |
| --- | --- | --- | --- |
| GET | /session/{session_id} | 获取业务 Donny 会话状态 | status 为 WAITING_DREAM_INPUT 时返回 code: NO_INTERPRETATION |
| POST | /share | 触发梦境分享（由 Accountant Donny 调用） | 请求体需携带 dream_id, anonymous_dream_text, interpretation_summary, user_wallet |

错误码补充
| code | 含义 |
| ----- | ----- |
| NO_INTERPRETATION | 尚无可分享的解梦数据 | 