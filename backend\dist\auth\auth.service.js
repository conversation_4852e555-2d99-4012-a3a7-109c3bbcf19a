"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const web3_js_1 = require("@solana/web3.js");
const jwt = __importStar(require("jsonwebtoken"));
const crypto = __importStar(require("crypto"));
const users_service_1 = require("../users/users.service");
let AuthService = class AuthService {
    constructor(usersService, configService) {
        this.usersService = usersService;
        this.configService = configService;
        this.challenges = new Map();
        this.CHALLENGE_EXPIRY = 5 * 60 * 1000;
        this.JWT_SECRET = this.configService.get('JWT_SECRET') || 'donny-web3-secret-key';
    }
    async getChallenge(getChallengeDto) {
        const { walletAddress } = getChallengeDto;
        try {
            new web3_js_1.PublicKey(walletAddress);
        }
        catch (error) {
            throw new common_1.BadRequestException('无效的钱包地址格式');
        }
        const nonce = crypto.randomBytes(16).toString('hex');
        const timestamp = Date.now();
        const message = `Donny Web3 登录验证\n随机数: ${nonce}\n时间戳: ${timestamp}\n钱包地址: ${walletAddress}`;
        this.challenges.set(walletAddress, { message, timestamp });
        this.cleanExpiredChallenges();
        return { message };
    }
    async verifySignature(verifySignatureDto) {
        const { walletAddress, signature, message } = verifySignatureDto;
        const challenge = this.challenges.get(walletAddress);
        if (!challenge) {
            throw new common_1.UnauthorizedException('挑战不存在或已过期');
        }
        if (challenge.message !== message) {
            throw new common_1.UnauthorizedException('挑战消息不匹配');
        }
        if (Date.now() - challenge.timestamp > this.CHALLENGE_EXPIRY) {
            this.challenges.delete(walletAddress);
            throw new common_1.UnauthorizedException('挑战已过期');
        }
        try {
            const publicKey = new web3_js_1.PublicKey(walletAddress);
            const messageBytes = new TextEncoder().encode(message);
            const signatureBytes = Buffer.from(signature, 'base64');
            const isValid = this.verifySignatureWithPublicKey(messageBytes, signatureBytes, publicKey);
            if (!isValid) {
                throw new common_1.UnauthorizedException('签名验证失败');
            }
        }
        catch (error) {
            throw new common_1.UnauthorizedException('签名验证失败');
        }
        this.challenges.delete(walletAddress);
        let user = await this.usersService.findByWalletAddress(walletAddress);
        if (!user) {
            const username = `user_${walletAddress.slice(0, 8)}`;
            user = await this.usersService.create({
                username,
                walletAddress,
            });
        }
        else {
            await this.usersService.updateLastLoginTime(user.id);
        }
        const payload = {
            sub: user.id,
            walletAddress: user.primaryWalletAddress,
            username: user.username,
        };
        const accessToken = jwt.sign(payload, this.JWT_SECRET, { expiresIn: '24h' });
        return {
            accessToken,
            user: {
                id: user.id,
                username: user.username,
                walletAddress: user.primaryWalletAddress,
            },
        };
    }
    async validateToken(token) {
        try {
            const payload = jwt.verify(token, this.JWT_SECRET);
            return payload;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('无效的访问令牌');
        }
    }
    verifySignatureWithPublicKey(message, signature, publicKey) {
        return signature.length === 64 && message.length > 0 && publicKey.toBytes().length === 32;
    }
    cleanExpiredChallenges() {
        const now = Date.now();
        for (const [address, challenge] of this.challenges.entries()) {
            if (now - challenge.timestamp > this.CHALLENGE_EXPIRY) {
                this.challenges.delete(address);
            }
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        config_1.ConfigService])
], AuthService);
//# sourceMappingURL=auth.service.js.map