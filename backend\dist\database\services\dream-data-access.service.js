"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DreamDataAccessService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DreamDataAccessService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../database.service");
let DreamDataAccessService = DreamDataAccessService_1 = class DreamDataAccessService {
    constructor(databaseService) {
        this.databaseService = databaseService;
        this.logger = new common_1.Logger(DreamDataAccessService_1.name);
    }
    async createDream(agentType, userId, content, title, metadata, privacyLevel = 'private') {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'dreams',
            operation: 'insert',
            data: {
                user_id: userId,
                title,
                content,
                metadata,
                status: 'pending',
                privacy_level: privacyLevel,
            },
        });
        if (result.error) {
            return { dream: null, error: result.error };
        }
        return { dream: result.data[0] };
    }
    async getUserDreams(agentType, userId, status, limit = 20) {
        const filters = { user_id: userId };
        if (status) {
            filters.status = status;
        }
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'dreams',
            operation: 'select',
            filters,
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data.slice(0, limit);
    }
    async getDreamById(agentType, dreamId) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'dreams',
            operation: 'select',
            filters: { id: dreamId },
        });
        if (result.error || !result.data || result.data.length === 0) {
            return null;
        }
        return result.data[0];
    }
    async updateDreamStatus(agentType, dreamId, status) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'dreams',
            operation: 'update',
            data: { status },
            filters: { id: dreamId },
        });
        return { success: !result.error, error: result.error };
    }
    async createInterpretation(agentType, dreamId, content, confidenceScore, metadata, status = 'draft') {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'interpretations',
            operation: 'insert',
            data: {
                dream_id: dreamId,
                agent_type: agentType,
                content,
                confidence_score: confidenceScore,
                metadata,
                status,
            },
        });
        if (result.error) {
            return { interpretation: null, error: result.error };
        }
        return { interpretation: result.data[0] };
    }
    async getDreamInterpretations(agentType, dreamId) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'interpretations',
            operation: 'select',
            filters: { dream_id: dreamId },
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data;
    }
    async getAgentInterpretations(agentType, dreamId, targetAgentType) {
        const filters = { dream_id: dreamId };
        if (targetAgentType) {
            filters.agent_type = targetAgentType;
        }
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'interpretations',
            operation: 'select',
            filters,
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data;
    }
    async updateInterpretationStatus(agentType, interpretationId, status) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'interpretations',
            operation: 'update',
            data: { status },
            filters: { id: interpretationId },
        });
        return { success: !result.error, error: result.error };
    }
    async updateInterpretationContent(agentType, interpretationId, content, confidenceScore, metadata) {
        const updateData = { content };
        if (confidenceScore !== undefined) {
            updateData.confidence_score = confidenceScore;
        }
        if (metadata !== undefined) {
            updateData.metadata = metadata;
        }
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'interpretations',
            operation: 'update',
            data: updateData,
            filters: { id: interpretationId },
        });
        return { success: !result.error, error: result.error };
    }
    async createUserFeedback(userId, interpretationId, rating, feedbackText, feedbackType) {
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'user_feedback',
            operation: 'insert',
            data: {
                interpretation_id: interpretationId,
                user_id: userId,
                rating,
                feedback_text: feedbackText,
                feedback_type: feedbackType,
            },
        });
        if (result.error) {
            return { feedback: null, error: result.error };
        }
        return { feedback: result.data[0] };
    }
    async getInterpretationFeedback(agentType, interpretationId) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'user_feedback',
            operation: 'select',
            filters: { interpretation_id: interpretationId },
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data;
    }
    async getUserFeedback(agentType, userId) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'user_feedback',
            operation: 'select',
            filters: { user_id: userId },
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data;
    }
    async getDreamFullInfo(agentType, dreamId) {
        try {
            const dream = await this.getDreamById(agentType, dreamId);
            if (!dream) {
                return { dream: null, interpretations: [], feedback: [], error: { message: 'Dream not found' } };
            }
            const interpretations = await this.getDreamInterpretations(agentType, dreamId);
            const allFeedback = [];
            for (const interpretation of interpretations) {
                const interpretationFeedback = await this.getInterpretationFeedback(agentType, interpretation.id);
                allFeedback.push(...interpretationFeedback);
            }
            return {
                dream,
                interpretations,
                feedback: allFeedback,
            };
        }
        catch (error) {
            this.logger.error(`获取梦境完整信息失败: ${error.message}`);
            return { dream: null, interpretations: [], feedback: [], error };
        }
    }
};
exports.DreamDataAccessService = DreamDataAccessService;
exports.DreamDataAccessService = DreamDataAccessService = DreamDataAccessService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], DreamDataAccessService);
//# sourceMappingURL=dream-data-access.service.js.map