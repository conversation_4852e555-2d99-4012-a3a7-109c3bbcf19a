import { DatabaseService } from '../database.service';
export interface ConversationSession {
    id: string;
    user_id: string;
    session_type: 'general' | 'dream_analysis' | 'consultation';
    status: 'active' | 'completed' | 'abandoned';
    metadata?: any;
    started_at: string;
    ended_at?: string;
    created_at: string;
}
export interface ConversationMessage {
    id: string;
    session_id: string;
    sender_type: 'user' | 'agent';
    sender_id: string;
    message_type: 'text' | 'image' | 'audio' | 'system';
    content: string;
    metadata?: any;
    created_at: string;
}
export declare class ConversationDataAccessService {
    private databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService);
    createConversationSession(agentType: string, userId: string, sessionType?: 'general' | 'dream_analysis' | 'consultation', metadata?: any): Promise<{
        session: ConversationSession;
        error?: any;
    }>;
    getUserConversationSessions(agentType: string, userId: string, status?: 'active' | 'completed' | 'abandoned', limit?: number): Promise<ConversationSession[]>;
    getConversationSessionById(agentType: string, sessionId: string): Promise<ConversationSession | null>;
    updateConversationSessionStatus(agentType: string, sessionId: string, status: 'active' | 'completed' | 'abandoned'): Promise<{
        success: boolean;
        error?: any;
    }>;
    addMessageToSession(agentType: string, sessionId: string, senderType: 'user' | 'agent', senderId: string, content: string, messageType?: 'text' | 'image' | 'audio' | 'system', metadata?: any): Promise<{
        message: ConversationMessage;
        error?: any;
    }>;
    getSessionMessages(agentType: string, sessionId: string, limit?: number, offset?: number): Promise<ConversationMessage[]>;
    getRecentMessages(agentType: string, sessionId: string, count?: number): Promise<ConversationMessage[]>;
    getConversationFullInfo(agentType: string, sessionId: string, messageLimit?: number): Promise<{
        session: ConversationSession;
        messages: ConversationMessage[];
        error?: any;
    }>;
    getUserConversationStats(agentType: string, userId: string): Promise<{
        totalSessions: number;
        activeSessions: number;
        completedSessions: number;
        totalMessages: number;
        error?: any;
    }>;
    cleanupOldSessions(agentType: string, daysOld?: number): Promise<{
        cleanedCount: number;
        error?: any;
    }>;
    searchMessages(agentType: string, userId: string, searchText: string, limit?: number): Promise<ConversationMessage[]>;
    createBatchMessages(agentType: string, sessionId: string, messages: Array<{
        senderType: 'user' | 'agent';
        senderId: string;
        content: string;
        messageType?: 'text' | 'image' | 'audio' | 'system';
        metadata?: any;
        timestamp?: Date;
    }>): Promise<{
        successCount: number;
        errors: any[];
    }>;
}
