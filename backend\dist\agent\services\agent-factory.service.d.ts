import { ModuleRef } from '@nestjs/core';
import { BaseDonnyService, AgentPersona } from './base-donny.service';
import { AgentType } from '../../database/types/rag.types';
export interface AgentInfo {
    id: string;
    type: AgentType;
    name: string;
    persona: Agent<PERSON>ersona;
    capabilities: string[];
    status: 'active' | 'inactive' | 'maintenance';
    createdAt: Date;
    lastActivity?: Date;
    messageCount?: number;
}
export interface AgentCreationRequest {
    agentType: AgentType;
    userId?: string;
    sessionId?: string;
    configuration?: any;
}
export declare class AgentFactoryService {
    private readonly moduleRef;
    private readonly logger;
    private readonly activeAgents;
    private readonly agentRegistry;
    constructor(moduleRef: ModuleRef);
    private initializeAgentRegistry;
    createAgent(request: AgentCreationRequest): Promise<BaseDonnyService>;
    getAgent(agentType: AgentType, userId?: string, sessionId?: string): BaseDonnyService | null;
    getSupportedAgentTypes(): Promise<AgentInfo[]>;
    getActiveAgentStats(): {
        totalActive: number;
        byType: Record<string, number>;
        oldestActivity: Date | null;
        newestActivity: Date | null;
    };
    recommendAgent(content: string, context?: any): Promise<{
        recommended: AgentType;
        confidence: number;
        reasoning: string;
        alternatives: Array<{
            type: AgentType;
            confidence: number;
            reasoning: string;
        }>;
    }>;
    cleanupInactiveAgents(maxIdleMinutes?: number): Promise<number>;
    reloadAgentConfiguration(agentType: AgentType): Promise<boolean>;
    private generateAgentInstanceId;
    private applyConfiguration;
}
