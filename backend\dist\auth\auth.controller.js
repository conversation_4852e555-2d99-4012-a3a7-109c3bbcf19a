"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_service_1 = require("./auth.service");
const auth_dto_1 = require("./dto/auth.dto");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    getChallenge(getChallengeDto) {
        return this.authService.getChallenge(getChallengeDto);
    }
    verifySignature(verifySignatureDto) {
        return this.authService.verifySignature(verifySignatureDto);
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('challenge'),
    (0, swagger_1.ApiOperation)({ summary: '获取登录挑战' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取挑战成功',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', description: '挑战消息' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '无效的钱包地址格式' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_1.GetChallengeDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getChallenge", null);
__decorate([
    (0, common_1.Post)('verify'),
    (0, swagger_1.ApiOperation)({ summary: '验证钱包签名并登录' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '登录成功',
        type: auth_dto_1.AuthResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '签名验证失败或挑战过期' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_1.VerifySignatureDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verifySignature", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('认证'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map