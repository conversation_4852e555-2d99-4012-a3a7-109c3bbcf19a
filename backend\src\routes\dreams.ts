import { Router } from 'express';
import { authenticateJWT } from '../middlewares/auth';
import { AppError } from '../middlewares/errorHandler';
import {
  createDream,
  getUserDreams,
  getDreamById,
  updateDreamInterpretation,
  updateDreamShareStatus
} from '../services/dreamService';

const router = Router();

// 获取用户的所有梦境
router.get('/', authenticateJWT, async (req, res, next) => {
  try {
    if (!req.user || !req.user.userId) {
      throw new AppError('User not authenticated', 401);
    }

    const dreams = await getUserDreams(req.user.userId);

    res.json({
      status: 'success',
      data: {
        dreams,
      },
    });
  } catch (error) {
    next(error);
  }
});

// 创建新梦境
router.post('/', authenticateJWT, async (req, res, next) => {
  try {
    if (!req.user || !req.user.userId) {
      throw new AppError('User not authenticated', 401);
    }

    const { content } = req.body;

    if (!content) {
      throw new AppError('Dream content is required', 400);
    }

    const dream = await createDream(req.user.userId, content);

    res.status(201).json({
      status: 'success',
      data: {
        dream,
      },
    });
  } catch (error) {
    next(error);
  }
});

// 获取特定梦境
router.get('/:dreamId', authenticateJWT, async (req, res, next) => {
  try {
    const { dreamId } = req.params;

    const dream = await getDreamById(dreamId);

    if (!dream) {
      throw new AppError('Dream not found', 404);
    }

    // 验证梦境所有权
    if (dream.userId !== req.user?.userId) {
      throw new AppError('Unauthorized access to dream', 403);
    }

    res.json({
      status: 'success',
      data: {
        dream,
      },
    });
  } catch (error) {
    next(error);
  }
});

// 更新梦境解析
router.patch('/:dreamId/interpretation', authenticateJWT, async (req, res, next) => {
  try {
    const { dreamId } = req.params;
    const { interpretation, donnyType } = req.body;

    if (!interpretation || !donnyType) {
      throw new AppError('Interpretation and donnyType are required', 400);
    }

    // 验证梦境所有权
    const dream = await getDreamById(dreamId);

    if (!dream) {
      throw new AppError('Dream not found', 404);
    }

    if (dream.userId !== req.user?.userId) {
      throw new AppError('Unauthorized access to dream', 403);
    }

    const updatedDream = await updateDreamInterpretation(dreamId, interpretation, donnyType);

    res.json({
      status: 'success',
      data: {
        dream: updatedDream,
      },
    });
  } catch (error) {
    next(error);
  }
});

// 分享梦境
router.patch('/:dreamId/share', authenticateJWT, async (req, res, next) => {
  try {
    const { dreamId } = req.params;

    // 验证梦境所有权
    const dream = await getDreamById(dreamId);

    if (!dream) {
      throw new AppError('Dream not found', 404);
    }

    if (dream.userId !== req.user?.userId) {
      throw new AppError('Unauthorized access to dream', 403);
    }

    const updatedDream = await updateDreamShareStatus(dreamId, true);

    res.json({
      status: 'success',
      data: {
        dream: updatedDream,
      },
    });
  } catch (error) {
    next(error);
  }
});

export default router;
