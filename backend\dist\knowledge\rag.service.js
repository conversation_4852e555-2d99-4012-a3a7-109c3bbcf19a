"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RAGService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RAGService = void 0;
const common_1 = require("@nestjs/common");
const embedding_service_1 = require("./embedding.service");
const knowledge_data_access_service_1 = require("../database/services/knowledge-data-access.service");
let RAGService = RAGService_1 = class RAGService {
    constructor(embeddingService, knowledgeDataAccess) {
        this.embeddingService = embeddingService;
        this.knowledgeDataAccess = knowledgeDataAccess;
        this.logger = new common_1.Logger(RAGService_1.name);
        this.defaultConfig = {
            max_context_tokens: 4000,
            similarity_threshold: 0.7,
            max_search_results: 5,
            embedding_model: 'text-embedding-ada-002',
            generation_model: 'gpt-3.5-turbo',
        };
    }
    async performRAG(query, agentType, userId, config) {
        const finalConfig = { ...this.defaultConfig, ...config };
        try {
            this.logger.log(`开始RAG处理: ${query.slice(0, 50)}...`);
            const queryEmbedding = await this.embeddingService.generateEmbedding({
                text: query,
                model: finalConfig.embedding_model,
            });
            const searchResults = await this.knowledgeDataAccess.searchKnowledgeEntries(queryEmbedding.embedding, agentType, finalConfig.similarity_threshold, finalConfig.max_search_results);
            await this.logSearchUsage(searchResults, query, agentType, userId);
            const context = this.buildContext(searchResults, finalConfig.max_context_tokens);
            const generatedResponse = await this.generateEnhancedResponse(query, context, agentType, finalConfig.generation_model);
            const response = {
                search_results: searchResults,
                generated_response: generatedResponse,
                sources_used: searchResults.map(r => r.id),
                context_tokens_used: this.estimateTokens(context),
            };
            this.logger.log(`RAG处理完成，返回 ${searchResults.length} 个结果`);
            return response;
        }
        catch (error) {
            this.logger.error(`RAG处理失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async searchKnowledge(request, userId) {
        try {
            this.logger.log(`知识库搜索: ${request.query.slice(0, 50)}...`);
            const queryEmbedding = await this.embeddingService.generateEmbedding({
                text: request.query,
            });
            const results = await this.knowledgeDataAccess.searchKnowledgeEntries(queryEmbedding.embedding, request.agent_type, request.match_threshold || 0.7, request.match_count || 10);
            const filteredResults = this.applyMetadataFilter(results, request.filter_metadata);
            await this.logSearchUsage(filteredResults, request.query, request.agent_type, userId);
            this.logger.log(`搜索完成，返回 ${filteredResults.length} 个结果`);
            return filteredResults;
        }
        catch (error) {
            this.logger.error(`知识库搜索失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async addKnowledgeEntry(request, userId) {
        try {
            this.logger.log(`添加知识条目: ${request.title || request.content.slice(0, 50)}...`);
            const embeddingResponse = await this.embeddingService.generateEmbedding({
                text: this.prepareTextForEmbedding(request),
            });
            const entry = await this.knowledgeDataAccess.createKnowledgeEntry(request, embeddingResponse.embedding);
            this.logger.log(`知识条目添加成功: ${entry.id}`);
            return entry;
        }
        catch (error) {
            this.logger.error(`添加知识条目失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async addKnowledgeEntriesBatch(entries, userId) {
        const results = [];
        try {
            this.logger.log(`开始批量添加 ${entries.length} 个知识条目`);
            const texts = entries.map(entry => this.prepareTextForEmbedding(entry));
            const embeddings = await this.embeddingService.generateBatchEmbeddings(texts);
            for (let i = 0; i < entries.length; i++) {
                try {
                    const entry = await this.knowledgeDataAccess.createKnowledgeEntry(entries[i], embeddings[i].embedding);
                    results.push(entry);
                }
                catch (error) {
                    this.logger.warn(`添加知识条目失败 [${i}]: ${error.message}`);
                }
            }
            this.logger.log(`批量添加完成，成功 ${results.length}/${entries.length} 个条目`);
            return results;
        }
        catch (error) {
            this.logger.error(`批量添加知识条目失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async reindexKnowledgeBase(knowledgeBaseId) {
        try {
            this.logger.log(`开始重新索引知识库: ${knowledgeBaseId}`);
            const entries = await this.knowledgeDataAccess.getKnowledgeEntries(knowledgeBaseId, 1000);
            let successCount = 0;
            let errorCount = 0;
            for (const entry of entries) {
                try {
                    const text = this.prepareTextForEmbedding({
                        title: entry.title,
                        content: entry.content,
                    });
                    const embeddingResponse = await this.embeddingService.generateEmbedding({
                        text,
                    });
                    await this.knowledgeDataAccess.updateKnowledgeEntry(entry.id, {}, embeddingResponse.embedding);
                    successCount++;
                }
                catch (error) {
                    this.logger.warn(`重新索引条目失败 [${entry.id}]: ${error.message}`);
                    errorCount++;
                }
                await this.delay(50);
            }
            this.logger.log(`知识库重新索引完成: 成功 ${successCount}，失败 ${errorCount}`);
        }
        catch (error) {
            this.logger.error(`重新索引知识库失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    prepareTextForEmbedding(request) {
        const parts = [];
        if (request.title) {
            parts.push(`标题: ${request.title}`);
        }
        if (request.content) {
            parts.push(`内容: ${request.content}`);
        }
        if (request.metadata) {
            const keyInfo = Object.entries(request.metadata)
                .filter(([key, value]) => typeof value === 'string' && value.length < 100)
                .map(([key, value]) => `${key}: ${value}`)
                .join(', ');
            if (keyInfo) {
                parts.push(`标签: ${keyInfo}`);
            }
        }
        return parts.join('\n');
    }
    buildContext(results, maxTokens) {
        let context = '';
        let estimatedTokens = 0;
        for (const result of results) {
            const snippet = this.formatContextSnippet(result);
            const snippetTokens = this.estimateTokens(snippet);
            if (estimatedTokens + snippetTokens > maxTokens) {
                break;
            }
            context += snippet + '\n\n';
            estimatedTokens += snippetTokens;
        }
        return context.trim();
    }
    formatContextSnippet(result) {
        const parts = [];
        if (result.title) {
            parts.push(`【${result.title}】`);
        }
        parts.push(result.content);
        if (result.similarity) {
            parts.push(`(相似度: ${(result.similarity * 100).toFixed(1)}%)`);
        }
        return parts.join('\n');
    }
    async generateEnhancedResponse(query, context, agentType, model) {
        this.logger.debug(`增强响应生成暂未实现，返回原始搜索结果`);
        return undefined;
    }
    applyMetadataFilter(results, filter) {
        if (!filter || Object.keys(filter).length === 0) {
            return results;
        }
        return results.filter(result => {
            return Object.entries(filter).every(([key, value]) => {
                return result.metadata[key] === value;
            });
        });
    }
    async logSearchUsage(results, query, agentType, userId) {
        if (!agentType || results.length === 0) {
            return;
        }
        try {
            const topResult = results[0];
            await this.knowledgeDataAccess.logKnowledgeUsage(agentType, topResult.id, query, topResult.similarity, 'conversation', userId);
        }
        catch (error) {
            this.logger.warn(`记录使用日志失败: ${error.message}`);
        }
    }
    estimateTokens(text) {
        const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
        const otherChars = text.length - chineseChars;
        return Math.ceil(chineseChars / 1.5 + otherChars / 4);
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
};
exports.RAGService = RAGService;
exports.RAGService = RAGService = RAGService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [embedding_service_1.EmbeddingService,
        knowledge_data_access_service_1.KnowledgeDataAccessService])
], RAGService);
//# sourceMappingURL=rag.service.js.map