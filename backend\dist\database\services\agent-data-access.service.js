"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AgentDataAccessService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentDataAccessService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../database.service");
let AgentDataAccessService = AgentDataAccessService_1 = class AgentDataAccessService {
    constructor(databaseService) {
        this.databaseService = databaseService;
        this.logger = new common_1.Logger(AgentDataAccessService_1.name);
    }
    async getAllAgentTypes() {
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'agent_types',
            operation: 'select',
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data;
    }
    async getAgentTypeByName(name) {
        const result = await this.databaseService.executeWithPermission('almighty', {
            table: 'agent_types',
            operation: 'select',
            filters: { name },
        });
        if (result.error || !result.data || result.data.length === 0) {
            return null;
        }
        return result.data[0];
    }
    async createAgentMemory(agentType, userId, memoryType, content, importanceScore = 0.5, expiresAt) {
        const memoryData = {
            agent_type: agentType,
            user_id: userId,
            memory_type: memoryType,
            content,
            importance_score: importanceScore,
            access_count: 0,
        };
        if (expiresAt) {
            memoryData.expires_at = expiresAt.toISOString();
        }
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'agent_memories',
            operation: 'insert',
            data: memoryData,
        });
        if (result.error) {
            return { memory: null, error: result.error };
        }
        return { memory: result.data[0] };
    }
    async getAgentMemories(agentType, userId, memoryType, limit = 50) {
        const filters = {
            agent_type: agentType,
            user_id: userId,
        };
        if (memoryType) {
            filters.memory_type = memoryType;
        }
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'agent_memories',
            operation: 'select',
            filters,
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data
            .sort((a, b) => {
            if (b.importance_score !== a.importance_score) {
                return b.importance_score - a.importance_score;
            }
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        })
            .slice(0, limit);
    }
    async updateMemoryAccess(agentType, memoryId) {
        const getResult = await this.databaseService.executeWithPermission(agentType, {
            table: 'agent_memories',
            operation: 'select',
            filters: { id: memoryId },
        });
        if (getResult.error || !getResult.data || getResult.data.length === 0) {
            return { success: false, error: { message: 'Memory not found' } };
        }
        const memory = getResult.data[0];
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'agent_memories',
            operation: 'update',
            data: {
                access_count: memory.access_count + 1,
                last_accessed_at: new Date().toISOString(),
            },
            filters: { id: memoryId },
        });
        return { success: !result.error, error: result.error };
    }
    async cleanupExpiredMemories(agentType) {
        try {
            const client = this.databaseService.getClient();
            const { data, error } = await client
                .from('agent_memories')
                .delete()
                .eq('agent_type', agentType)
                .not('expires_at', 'is', null)
                .lt('expires_at', new Date().toISOString());
            if (error) {
                return { deletedCount: 0, error };
            }
            let deletedCount = 0;
            if (data) {
                deletedCount = Array.isArray(data) ? data.length : 1;
            }
            this.logger.log(`清理了 ${deletedCount} 条过期的 ${agentType} 记忆`);
            return { deletedCount };
        }
        catch (error) {
            this.logger.error(`清理过期记忆失败: ${error.message}`);
            return { deletedCount: 0, error };
        }
    }
    async getKnowledgeByCategory(agentType, category, limit = 20) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'knowledge_entries',
            operation: 'select',
            filters: { category },
        });
        if (result.error || !result.data) {
            return [];
        }
        return result.data.slice(0, limit);
    }
    async searchKnowledgeByTags(agentType, tags, limit = 20) {
        try {
            const client = this.databaseService.getClient();
            const { data, error } = await client
                .from('knowledge_entries')
                .select('*')
                .overlaps('tags', tags)
                .limit(limit);
            if (error) {
                this.logger.warn(`搜索知识条目失败: ${error.message}`);
                return [];
            }
            return data || [];
        }
        catch (error) {
            this.logger.error(`搜索知识条目错误: ${error.message}`);
            return [];
        }
    }
    async searchKnowledgeByText(agentType, searchText, category, limit = 20) {
        try {
            const client = this.databaseService.getClient();
            let query = client
                .from('knowledge_entries')
                .select('*')
                .or(`title.ilike.%${searchText}%,content.ilike.%${searchText}%`);
            if (category) {
                query = query.eq('category', category);
            }
            query = query.limit(limit);
            const { data, error } = await query;
            if (error) {
                this.logger.warn(`全文搜索失败: ${error.message}`);
                return [];
            }
            return data || [];
        }
        catch (error) {
            this.logger.error(`全文搜索错误: ${error.message}`);
            return [];
        }
    }
    async createKnowledgeEntry(agentType, title, content, category, tags, metadata, source) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'knowledge_entries',
            operation: 'insert',
            data: {
                title,
                content,
                category,
                tags,
                metadata,
                source,
            },
        });
        if (result.error) {
            return { entry: null, error: result.error };
        }
        return { entry: result.data[0] };
    }
    async getAgentConfig(agentType) {
        try {
            const agentTypeInfo = await this.getAgentTypeByName(agentType);
            if (!agentTypeInfo) {
                return { persona: null, tools: null, permissions: [], error: { message: 'Agent type not found' } };
            }
            const permissions = await this.databaseService.getAgentPermissions(agentType);
            return {
                persona: agentTypeInfo.persona_config,
                tools: agentTypeInfo.tools_config,
                permissions,
            };
        }
        catch (error) {
            this.logger.error(`获取Agent配置失败: ${error.message}`);
            return { persona: null, tools: null, permissions: [], error };
        }
    }
    async updateMemoryImportance(agentType, memoryId, importanceScore) {
        const result = await this.databaseService.executeWithPermission(agentType, {
            table: 'agent_memories',
            operation: 'update',
            data: { importance_score: Math.max(0, Math.min(1, importanceScore)) },
            filters: { id: memoryId },
        });
        return { success: !result.error, error: result.error };
    }
    async createBatchMemories(agentType, userId, memories) {
        let successCount = 0;
        const errors = [];
        for (const memory of memories) {
            const { error } = await this.createAgentMemory(agentType, userId, memory.type, memory.content, memory.importance, memory.expiresAt);
            if (error) {
                errors.push(error);
            }
            else {
                successCount++;
            }
        }
        this.logger.log(`批量创建记忆完成: 成功 ${successCount}/${memories.length}`);
        return { successCount, errors };
    }
};
exports.AgentDataAccessService = AgentDataAccessService;
exports.AgentDataAccessService = AgentDataAccessService = AgentDataAccessService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], AgentDataAccessService);
//# sourceMappingURL=agent-data-access.service.js.map