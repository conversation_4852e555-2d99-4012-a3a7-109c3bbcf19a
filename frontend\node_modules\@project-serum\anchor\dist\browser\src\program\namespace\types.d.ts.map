{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../../src/program/namespace/types.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,MAAM,OAAO,CAAC;AACvB,OAAO,EAAE,GAAG,EAAE,MAAM,QAAQ,CAAC;AAC7B,OAAO,EACL,WAAW,IAAI,cAAc,EAC7B,cAAc,EACd,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EAClB,QAAQ,EACR,cAAc,EACd,OAAO,EACP,UAAU,EACV,gBAAgB,EAChB,kBAAkB,EACnB,MAAM,WAAW,CAAC;AACnB,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AAE3C;;GAEG;AACH,MAAM,MAAM,eAAe,CAAC,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC;AAE3E;;GAEG;AACH,MAAM,MAAM,cAAc,CAAC,CAAC,SAAS,cAAc,IAAI;KACpD,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG;QAAE,IAAI,EAAE,CAAC,CAAA;KAAE;CAClC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,kBAAkB,CAAC,GAAG,SAAS,GAAG,IAAI,cAAc,CAC9D,eAAe,CAAC,GAAG,CAAC,CACrB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,WAAW,CAAC,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,SAAS,GACxE,UAAU,GACV,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAEzC;;GAEG;AACH,MAAM,MAAM,UAAU,CAAC,CAAC,SAAS,UAAU,IAAI;KAC5C,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG;QAAE,IAAI,EAAE,CAAC,CAAA;KAAE;CAClC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,cAAc,CAAC,GAAG,SAAS,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;AAE3E,MAAM,MAAM,yBAAyB,CACnC,GAAG,SAAS,GAAG,EACf,CAAC,SAAS,cAAc,EACxB,GAAG,EACH,EAAE,SAAS;KAAG,CAAC,IAAI,MAAM,cAAc,CAAC,CAAC,CAAC,GAAG,OAAO;CAAE,GAAG;KACtD,CAAC,IAAI,MAAM,cAAc,CAAC,CAAC,CAAC,GAAG,OAAO;CACxC,IACC;KACD,CAAC,IAAI,MAAM,cAAc,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAClD,GAAG,EACH,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACpB,GAAG,CACJ,GACC,EAAE,CAAC,CAAC,CAAC;CACR,CAAC;AAEF,MAAM,MAAM,oBAAoB,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC,SAAS,cAAc,IAAI;KAC3E,CAAC,IAAI,MAAM,cAAc,CAAC,CAAC,CAAC,GAAG,SAAS,CACvC,GAAG,EACH,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACpB,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1C;CACF,CAAC;AAEF,MAAM,MAAM,oBAAoB,CAC9B,GAAG,SAAS,GAAG,EACf,CAAC,SAAS,eAAe,CAAC,GAAG,CAAC,EAC9B,GAAG,IACD,CAAC,GAAG,IAAI,EAAE,wBAAwB,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC;AAEvD,MAAM,MAAM,wBAAwB,CAClC,GAAG,SAAS,GAAG,EACf,CAAC,SAAS,GAAG,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,IACnC;IACF,GAAG,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;IACtC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;CACzC,CAAC;AAEF,MAAM,MAAM,2BAA2B,CACrC,GAAG,SAAS,GAAG,EACf,CAAC,SAAS,eAAe,CAAC,GAAG,CAAC,IAC5B,4BAA4B,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAExD,KAAK,4BAA4B,CAAC,CAAC,SAAS,cAAc,GAAG,cAAc,IAAI;KAC5E,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,0BAA0B,CAAC,CAAC,GAAG;QAAE,IAAI,EAAE,CAAC,CAAA;KAAE,CAAC;CAC9D,CAAC;AAEF,KAAK,0BAA0B,CAAC,CAAC,SAAS,cAAc,IACtD,CAAC,SAAS,cAAc,GACpB,4BAA4B,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GACnD,SAAS,CAAC;AAEhB,MAAM,MAAM,SAAS,CACnB,GAAG,SAAS,GAAG,EACf,CAAC,SAAS,GAAG,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,EACrC,GAAG,IACD,CAAC,GAAG,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC;AAE1D,KAAK,OAAO,GAAG;IACb,SAAS,EAAE,SAAS,CAAC;IACrB,IAAI,EAAE,OAAO,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;CACf,GAAG;KACD,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,MAAM;CAC3E,GAAG;KACD,CAAC,IAAI,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,EAAE;CAC7D,CAAC;AAEF,MAAM,MAAM,UAAU,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,IAAI,CAAC,SAAS,MAAM,OAAO,GACxE,OAAO,CAAC,CAAC,CAAC,GACV,CAAC,SAAS;IAAE,OAAO,EAAE,MAAM,OAAO,CAAA;CAAE,GACpC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,GACrB,CAAC,SAAS;IAAE,MAAM,EAAE;QAAE,OAAO,EAAE,MAAM,OAAO,CAAA;KAAE,CAAA;CAAE,GAChD,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,GACtC,CAAC,SAAS;IAAE,MAAM,EAAE,MAAM,OAAO,CAAA;CAAE,GACnC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,GAC3B,CAAC,SAAS;IAAE,OAAO,EAAE;QAAE,OAAO,EAAE,MAAM,OAAO,CAAA;KAAE,CAAA;CAAE,GACjD,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,GACvC,CAAC,SAAS;IAAE,OAAO,EAAE,MAAM,OAAO,CAAA;CAAE,GACpC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,GAC5B,CAAC,SAAS;IAAE,GAAG,EAAE,MAAM,OAAO,CAAA;CAAE,GAChC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GACnB,CAAC,SAAS;IAAE,GAAG,EAAE;QAAE,OAAO,EAAE,MAAM,OAAO,CAAA;KAAE,CAAA;CAAE,GAC7C,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAC9B,CAAC,SAAS;IAAE,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;CAAE,GAC3D,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GACxB,OAAO,CAAC;AAEZ;;GAEG;AACH,KAAK,SAAS,CAAC,CAAC,SAAS,QAAQ,EAAE,EAAE,OAAO,IAAI;KAC7C,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,QAAQ,GACjC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,GACjC,OAAO;CACZ,GAAG,OAAO,EAAE,CAAC;AACd;;GAEG;AACH,KAAK,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,GACxC,YAAY,CAAC,CAAC,CAAC,GACf,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,GAC/B,kBAAkB,GAClB,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,GACjC,YAAY,CAAC,CAAC,CAAC,GACf,CAAC,CAAC;AAEN;;GAEG;AACH,OAAO,MAAM,eAAe,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,SAAS,OAAO,GACxD,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,GACtB,KAAK,CAAC;AAEV;;GAEG;AACH,OAAO,MAAM,gBAAgB,CAC3B,CAAC,SAAS,aAAa,EACvB,OAAO,IACL,CAAC,SAAS,kBAAkB,GAC5B;KACG,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC;CACtE,GACD,CAAC,SAAS,kBAAkB,GAC5B;KACG,EAAE,IAAI,MAAM,CAAC,IAAI,OAAO,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE,CAAC,GAAG,eAAe,CAC9D,CAAC,CAAC,EAAE,CAAC,EACL,OAAO,CACR;CACF,GACD,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAE1B;;;GAGG;AACH,OAAO,MAAM,UAAU,CAAC,CAAC,SAAS,gBAAgB,EAAE,OAAO,IAAI;KAE5D,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CACzE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EACxB,OAAO,CACR;CACF,CAAC;AAEF,KAAK,YAAY,CAAC,CAAC,SAAS,kBAAkB,EAAE,OAAO,IAAI;KACxD,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC;CACxE,CAAC;AAEF,MAAM,MAAM,OAAO,CACjB,CAAC,SAAS,UAAU,EACpB,OAAO,IACL,CAAC,CAAC,MAAM,CAAC,SAAS,gBAAgB,GAClC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,GAC9B,CAAC,CAAC,MAAM,CAAC,SAAS,kBAAkB,GACpC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,GAChC,KAAK,CAAC;AAEV,KAAK,iBAAiB,CAAC,CAAC,SAAS,UAAU,EAAE,EAAE,OAAO,IAAI;KACvD,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC;CACnD,CAAC;AAEF,KAAK,aAAa,CAAC,CAAC,SAAS,UAAU,EAAE,EAAE,OAAO,IAAI;KACnD,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC;CACnD,CAAC;AAEF,KAAK,WAAW,GAAG,0BAA0B,CAAC;AAC9C;;MAEM;AACN,KAAK,YAAY,GAAG,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAE/C,KAAK,eAAe,CAClB,CAAC,SAAS,UAAU,EAAE,EACtB,OAAO,GAAG,YAAY,EACtB,OAAO,GAAG,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,IACjC,WAAW,SAAS,YAAY,CAAC,OAAO,CAAC,GACzC,eAAe,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAC7C,OAAO,CAAC;AAEZ,KAAK,eAAe,CAClB,CAAC,SAAS,UAAU,EAAE,EACtB,OAAO,GAAG,YAAY,EACtB,OAAO,GAAG,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,IACjC,WAAW,SAAS,YAAY,CAAC,OAAO,CAAC,GACzC,eAAe,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAC7C,OAAO,CAAC;AAEZ,KAAK,eAAe,CAClB,CAAC,SAAS,UAAU,EAAE,EACtB,OAAO,GAAG,YAAY,IACpB,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAE9B;;;MAGM;AACN,KAAK,cAAc,CACjB,CAAC,SAAS,UAAU,EAAE,EACtB,OAAO,GAAG,YAAY,EACtB,OAAO,GAAG,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,IAGnC,WAAW,SAAS,YAAY,CAAC,OAAO,CAAC,GACrC,eAAe,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAC7C,OAAO,CAAC;AAEd,MAAM,MAAM,QAAQ,CAAC,CAAC,SAAS,GAAG,IAAI,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAE9E,KAAK,YAAY,CACf,CAAC,SAAS,GAAG,EACb,KAAK,SAAS,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,EAC9C,OAAO,IACL;KACD,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC;CAC5E,CAAC;AAEF,MAAM,MAAM,SAAS,CAAC,CAAC,SAAS,GAAG,EAAE,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI;KAC3D,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,YAAY,CAChE,CAAC,EACD,CAAC,EACD,OAAO,CACR;CACF,CAAC;AAEF,MAAM,MAAM,WAAW,CAAC,CAAC,SAAS,GAAG,IAAI,iBAAiB,CACxD,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAC1B,QAAQ,CAAC,CAAC,CAAC,CACZ,CAAC;AAEF,MAAM,MAAM,YAAY,CAAC,GAAG,SAAS,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC"}