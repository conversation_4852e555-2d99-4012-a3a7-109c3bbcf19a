import { BaseMemory, MemoryType, MemoryImportance, MemoryInsight } from './memory.types';
import { AgentType } from '../database/types/rag.types';
import { MemoryManagerService } from './services/memory-manager.service';
export declare class MemoryController {
    private readonly memoryManager;
    private readonly logger;
    constructor(memoryManager: MemoryManagerService);
    storeMemory(memoryData: Partial<BaseMemory>): Promise<BaseMemory>;
    retrieveMemories(agentType?: AgentType, userId?: string, sessionId?: string, memoryType?: MemoryType, category?: string, importance?: MemoryImportance, searchText?: string, limit?: number, offset?: number): Promise<BaseMemory[]>;
    getContextualMemories(agentType: AgentType, userId: string, context: string, limit?: number): Promise<BaseMemory[]>;
    updateMemory(memoryId: string, updates: Partial<BaseMemory>): Promise<BaseMemory>;
    deleteMemory(memoryId: string): Promise<{
        success: boolean;
    }>;
    getMemoryStats(agentType?: AgentType, userId?: string): Promise<any>;
    generateInsights(agentType: AgentType, userId: string): Promise<MemoryInsight[]>;
    triggerConsolidation(): Promise<{
        shortToMedium: number;
        mediumToLong: number;
    }>;
    triggerCleanup(): Promise<{
        shortTerm: number;
        mediumTerm: number;
        longTerm: number;
    }>;
    semanticSearch(query: string, agentType?: AgentType, userId?: string, threshold?: number, limit?: number): Promise<BaseMemory[]>;
    healthCheck(): Promise<{
        status: string;
        services: Record<string, boolean>;
    }>;
}
