"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const supabase_config_1 = require("../config/supabase.config");
let SupabaseService = class SupabaseService {
    constructor(configService) {
        this.configService = configService;
        this.client = null;
    }
    onModuleInit() {
        this.client = (0, supabase_config_1.createSupabaseClient)(this.configService);
        if (this.client) {
            console.log('✅ Supabase客户端初始化成功');
        }
        else {
            console.log('⚠️  Supabase未配置，使用内存存储模式');
        }
    }
    getClient() {
        return this.client;
    }
    isAvailable() {
        return this.client !== null;
    }
    async createUser(userData) {
        if (!this.client) {
            throw new Error('Supabase客户端未初始化');
        }
        const { data, error } = await this.client
            .from('users')
            .insert([
            {
                username: userData.username,
                primary_wallet_address: userData.primaryWalletAddress,
            },
        ])
            .select()
            .single();
        if (error) {
            throw new Error(`创建用户失败: ${error.message}`);
        }
        await this.addWalletAddress(String(data.id), userData.primaryWalletAddress, true);
        return data;
    }
    async findUserById(id) {
        if (!this.client) {
            throw new Error('Supabase客户端未初始化');
        }
        const { data, error } = await this.client
            .from('users')
            .select(`
        *,
        wallet_addresses(*)
      `)
            .eq('id', id)
            .single();
        if (error && error.code !== 'PGRST116') {
            throw new Error(`查询用户失败: ${error.message}`);
        }
        return data;
    }
    async findUserByWalletAddress(walletAddress) {
        if (!this.client) {
            throw new Error('Supabase客户端未初始化');
        }
        const { data, error } = await this.client
            .from('wallet_addresses')
            .select(`
        *,
        users(*)
      `)
            .eq('wallet_address', walletAddress)
            .single();
        if (error && error.code !== 'PGRST116') {
            throw new Error(`通过钱包地址查询用户失败: ${error.message}`);
        }
        return data?.users || null;
    }
    async updateUser(id, updateData) {
        if (!this.client) {
            throw new Error('Supabase客户端未初始化');
        }
        const { data, error } = await this.client
            .from('users')
            .update(updateData)
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new Error(`更新用户失败: ${error.message}`);
        }
        return data;
    }
    async deleteUser(id) {
        if (!this.client) {
            throw new Error('Supabase客户端未初始化');
        }
        const { error } = await this.client
            .from('users')
            .delete()
            .eq('id', id);
        if (error) {
            throw new Error(`删除用户失败: ${error.message}`);
        }
    }
    async addWalletAddress(userId, walletAddress, isPrimary = false) {
        if (!this.client) {
            throw new Error('Supabase客户端未初始化');
        }
        const { data, error } = await this.client
            .from('wallet_addresses')
            .insert([
            {
                user_id: userId,
                wallet_address: walletAddress,
                is_primary: isPrimary,
            },
        ])
            .select()
            .single();
        if (error) {
            throw new Error(`添加钱包地址失败: ${error.message}`);
        }
        return data;
    }
    async findAllUsers() {
        if (!this.client) {
            throw new Error('Supabase客户端未初始化');
        }
        const { data, error } = await this.client
            .from('users')
            .select(`
        *,
        wallet_addresses(*)
      `);
        if (error) {
            throw new Error(`查询用户列表失败: ${error.message}`);
        }
        return data;
    }
    async createSession(sessionData) {
        if (!this.client) {
            throw new Error('Supabase客户端未初始化');
        }
        const { data, error } = await this.client
            .from('user_sessions')
            .insert([
            {
                user_id: sessionData.userId,
                wallet_address: sessionData.walletAddress,
                challenge_message: sessionData.challengeMessage,
                challenge_timestamp: new Date().toISOString(),
                expires_at: sessionData.expiresAt.toISOString(),
            },
        ])
            .select()
            .single();
        if (error) {
            throw new Error(`创建会话失败: ${error.message}`);
        }
        return data;
    }
    async findSessionByWalletAddress(walletAddress) {
        if (!this.client) {
            throw new Error('Supabase客户端未初始化');
        }
        const { data, error } = await this.client
            .from('user_sessions')
            .select('*')
            .eq('wallet_address', walletAddress)
            .eq('is_verified', false)
            .gt('expires_at', new Date().toISOString())
            .order('created_at', { ascending: false })
            .limit(1)
            .single();
        if (error && error.code !== 'PGRST116') {
            throw new Error(`查询会话失败: ${error.message}`);
        }
        return data;
    }
    async verifySession(sessionId) {
        if (!this.client) {
            throw new Error('Supabase客户端未初始化');
        }
        const { data, error } = await this.client
            .from('user_sessions')
            .update({ is_verified: true })
            .eq('id', sessionId)
            .select()
            .single();
        if (error) {
            throw new Error(`验证会话失败: ${error.message}`);
        }
        return data;
    }
};
exports.SupabaseService = SupabaseService;
exports.SupabaseService = SupabaseService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], SupabaseService);
//# sourceMappingURL=supabase.service.js.map