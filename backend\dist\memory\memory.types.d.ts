import { AgentType } from '../database/types/rag.types';
export declare enum MemoryType {
    SHORT_TERM = "short_term",
    MEDIUM_TERM = "medium_term",
    LONG_TERM = "long_term"
}
export declare enum MemoryImportance {
    LOW = 1,
    NORMAL = 2,
    HIGH = 3,
    CRITICAL = 4,
    PERMANENT = 5
}
export interface BaseMemory {
    id: string;
    agentType: AgentType;
    userId: string;
    sessionId?: string;
    memoryType: MemoryType;
    importance: MemoryImportance;
    content: any;
    metadata: MemoryMetadata;
    embedding?: number[];
    accessCount: number;
    lastAccessedAt: Date;
    createdAt: Date;
    updatedAt: Date;
    expiresAt?: Date;
}
export interface MemoryMetadata {
    category: string;
    tags: string[];
    sentiment?: number;
    confidence: number;
    source: string;
    relatedMemoryIds: string[];
    version: number;
    encrypted: boolean;
    private: boolean;
    consolidatedFrom?: string;
    consolidationReason?: string;
    originalCreatedAt?: string;
    reinforcementHistory?: any;
    activationLevel?: number;
    lastActivated?: string;
    reinforcementCount?: number;
    lastReinforcedAt?: string;
    decayRate?: number;
    consolidationScore?: number;
    consolidationDate?: string;
    archivalStatus?: string;
    emotionalSignificance?: number;
    narrativeContext?: string;
}
export interface ShortTermMemory extends BaseMemory {
    memoryType: MemoryType.SHORT_TERM;
    conversationTurn?: number;
    contextWindow?: number;
    activationLevel: number;
    lastActivated: Date;
}
export interface MediumTermMemory extends BaseMemory {
    memoryType: MemoryType.MEDIUM_TERM;
    reinforcementCount: number;
    lastReinforcedAt: Date;
    decayRate: number;
    consolidationScore: number;
}
export interface LongTermMemory extends BaseMemory {
    memoryType: MemoryType.LONG_TERM;
    consolidationDate: Date;
    archivalStatus: string;
    emotionalSignificance: number;
    narrativeContext: string;
}
export interface MemoryQuery {
    agentType?: AgentType;
    userId?: string;
    sessionId?: string;
    memoryType?: MemoryType;
    category?: string;
    tags?: string[];
    importance?: MemoryImportance;
    timeRange?: {
        start: Date;
        end: Date;
    };
    searchText?: string;
    embedding?: number[];
    similarityThreshold?: number;
    limit?: number;
    offset?: number;
}
export interface MemoryConsolidationRule {
    id: string;
    name: string;
    description: string;
    triggerConditions: any;
    actions: any[];
    priority: number;
    active: boolean;
}
export interface MemoryInsight {
    id: string;
    userId: string;
    agentType: AgentType;
    type: string;
    title: string;
    description: string;
    confidence: number;
    supportingMemoryIds: string[];
    generatedAt: Date;
    relevanceScore: number;
}
export interface MemoryStats {
    totalMemories: number;
    byType: Record<MemoryType, number>;
    byImportance: Record<MemoryImportance, number>;
    byCategory: Record<string, number>;
    storageUsed: number;
    averageImportance: number;
    oldestMemory: Date;
    newestMemory: Date;
    consolidationsPending: number;
}
export interface MemoryEvolutionEvent {
    id: string;
    memoryId: string;
    eventType: 'created' | 'accessed' | 'updated' | 'consolidated' | 'archived' | 'forgotten';
    oldValue?: any;
    newValue?: any;
    reason: string;
    confidence: number;
    timestamp: Date;
    agentType: AgentType;
    userId: string;
}
