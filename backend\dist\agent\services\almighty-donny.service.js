"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AlmightyDonnyService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlmightyDonnyService = void 0;
const common_1 = require("@nestjs/common");
const agent_runtime_service_1 = require("./agent-runtime.service");
const user_data_access_service_1 = require("../../database/services/user-data-access.service");
const conversation_data_access_service_1 = require("../../database/services/conversation-data-access.service");
const agent_data_access_service_1 = require("../../database/services/agent-data-access.service");
let AlmightyDonnyService = AlmightyDonnyService_1 = class AlmightyDonnyService {
    constructor(agentRuntimeService, userDataService, conversationDataService, agentDataService) {
        this.agentRuntimeService = agentRuntimeService;
        this.userDataService = userDataService;
        this.conversationDataService = conversationDataService;
        this.agentDataService = agentDataService;
        this.logger = new common_1.Logger(AlmightyDonnyService_1.name);
        this.agentId = 'almighty-donny';
        this.userFlows = new Map();
    }
    async onModuleInit() {
        this.logger.log('正在初始化Almighty Donny...');
        await this.initializeAlmightyDonny();
    }
    async initializeAlmightyDonny() {
        try {
            const agentConfig = await this.agentDataService.getAgentConfig('almighty');
            const config = {
                agentId: this.agentId,
                agentType: 'almighty',
                name: 'Almighty Donny',
                persona: agentConfig.persona || this.getDefaultPersona(),
                tools: agentConfig.tools || this.getDefaultTools(),
                modelProvider: 'openai',
            };
            await this.agentRuntimeService.createAgent(config);
            this.logger.log('Almighty Donny 初始化完成');
        }
        catch (error) {
            this.logger.error(`初始化Almighty Donny失败: ${error.message}`);
            throw error;
        }
    }
    async handleUserMessage(userId, content, messageId) {
        this.logger.log(`处理用户消息 - 用户: ${userId}`);
        try {
            let userFlow = this.userFlows.get(userId);
            if (!userFlow) {
                userFlow = {
                    userId,
                    currentStep: 'greeting',
                    lastActivity: new Date(),
                };
                this.userFlows.set(userId, userFlow);
            }
            userFlow.lastActivity = new Date();
            const message = {
                id: messageId || `msg_${Date.now()}`,
                userId,
                content,
                type: 'text',
                timestamp: new Date(),
            };
            const response = await this.processUserFlow(userFlow, message);
            await this.saveConversation(userId, message, response);
            return response;
        }
        catch (error) {
            this.logger.error(`处理用户消息失败: ${error.message}`);
            return {
                content: '抱歉，我现在遇到了一些技术问题。请稍后再试。',
                type: 'text',
                metadata: { error: true },
            };
        }
    }
    async processUserFlow(userFlow, message) {
        const { content } = message;
        const lowerContent = content.toLowerCase();
        if (this.isDreamInput(content)) {
            return await this.handleDreamInput(userFlow, content);
        }
        if (this.isDonnySelection(content)) {
            return await this.handleDonnySelection(userFlow, content);
        }
        switch (userFlow.currentStep) {
            case 'greeting':
                return await this.handleGreeting(userFlow, content);
            case 'dream_input':
                return await this.handleDreamInput(userFlow, content);
            case 'donny_selection':
                return await this.handleDonnySelection(userFlow, content);
            case 'payment':
                return await this.handlePayment(userFlow, content);
            case 'interpretation':
                return await this.handleInterpretation(userFlow, content);
            case 'feedback':
                return await this.handleFeedback(userFlow, content);
            default:
                return await this.handleGeneral(userFlow, content);
        }
    }
    async handleGreeting(userFlow, content) {
        userFlow.currentStep = 'dream_input';
        return {
            content: `你好！我是Almighty Donny，很高兴见到你！🌟

我是这里的总管家，可以帮助你：
- 🌙 专业解梦服务（选择最适合的解梦专家）
- 💭 梦境分享到"梦海"并获得奖励
- 📊 查看你的历史记录和收藏

请告诉我你的梦境，我会为你推荐最合适的解梦专家！`,
            type: 'text',
            metadata: { step: 'greeting' },
        };
    }
    async handleDreamInput(userFlow, content) {
        userFlow.dreamContent = content;
        userFlow.currentStep = 'donny_selection';
        const recommendations = await this.analyzeDreamAndRecommend(content);
        const recommendationText = recommendations.map(r => `🔮 **${this.getDonnyDisplayName(r.donnyType)}** (${Math.round(r.confidence * 100)}%匹配)\n   ${r.reasoning}`).join('\n\n');
        return {
            content: `我已经收到了你的梦境！让我为你分析一下... 🔍

基于你的梦境内容，我推荐以下专家：

${recommendationText}

你可以：
1. 输入 "道家" 或 "taoist" 选择道家解梦
2. 输入 "心理" 或 "freud" 选择心理学解梦  
3. 输入 "暖心" 或 "papa" 选择共情式解梦
4. 直接说 "我选择推荐的" 使用最佳推荐

你想选择哪位专家来解析你的梦境呢？`,
            type: 'text',
            metadata: {
                step: 'dream_selection',
                recommendations,
                dreamContent: content
            },
        };
    }
    async handleDonnySelection(userFlow, content) {
        const selectedDonny = this.parseDonnySelection(content);
        if (!selectedDonny) {
            return {
                content: `我没有理解你的选择。请告诉我：
- "道家" 或 "taoist" - 选择道家解梦
- "心理" 或 "freud" - 选择心理学解梦
- "暖心" 或 "papa" - 选择共情式解梦

或者说 "重新推荐" 让我再次分析你的梦境。`,
                type: 'text',
                metadata: { step: 'donny_selection' },
            };
        }
        userFlow.selectedDonny = selectedDonny;
        userFlow.currentStep = 'payment';
        const donnyName = this.getDonnyDisplayName(selectedDonny);
        return {
            content: `很好！你选择了 **${donnyName}** 🎯

解梦服务费用：0.1 SOL
服务包含：
- 专业详细的梦境解析
- 个性化建议和指导
- 7天内可追问相关问题

支付完成后，${donnyName} 将立即为你开始解梦！
如果你在48小时内对解梦结果不满意，我们提供全额退款。

请确认支付 0.1 SOL 继续，或者输入 "重新选择" 更换专家。`,
            type: 'text',
            metadata: {
                step: 'payment',
                selectedDonny,
                amount: 0.1,
                currency: 'SOL'
            },
            actions: [{
                    type: 'payment_request',
                    parameters: {
                        amount: 0.1,
                        currency: 'SOL',
                        description: `${donnyName} 解梦服务`,
                    },
                }],
        };
    }
    async handlePayment(userFlow, content) {
        userFlow.paymentStatus = 'completed';
        userFlow.currentStep = 'interpretation';
        const donnyName = this.getDonnyDisplayName(userFlow.selectedDonny);
        return {
            content: `支付成功！✅

我已经将你的梦境转发给 **${donnyName}**。
预计解梦时间：5-15分钟

在等待期间，你可以：
- 继续和我聊天
- 查看你的历史梦境记录
- 了解更多关于梦境解析的知识

${donnyName} 完成解梦后，我会立即通知你！`,
            type: 'text',
            metadata: {
                step: 'interpretation_pending',
                paymentStatus: 'completed',
                estimatedTime: '5-15分钟'
            },
            actions: [{
                    type: 'start_interpretation',
                    parameters: {
                        donnyType: userFlow.selectedDonny,
                        dreamContent: userFlow.dreamContent,
                        userId: userFlow.userId,
                    },
                }],
        };
    }
    async handleInterpretation(userFlow, content) {
        return {
            content: `${this.getDonnyDisplayName(userFlow.selectedDonny)} 正在为你解梦中... 

我会在解梦完成后立即通知你。你也可以问我其他问题或者查看你的历史记录。`,
            type: 'text',
            metadata: { step: 'interpretation_in_progress' },
        };
    }
    async handleFeedback(userFlow, content) {
        return {
            content: `感谢你的反馈！我已经记录下来，这将帮助我们改进服务。

还有什么我可以帮助你的吗？你可以：
- 分享这个梦境到"梦海"获得奖励
- 开始新的解梦
- 查看历史记录`,
            type: 'text',
            metadata: { step: 'feedback_received' },
        };
    }
    async handleGeneral(userFlow, content) {
        const message = {
            id: `msg_${Date.now()}`,
            userId: userFlow.userId,
            content,
            type: 'text',
            timestamp: new Date(),
        };
        return await this.agentRuntimeService.processMessage(this.agentId, message);
    }
    async analyzeDreamAndRecommend(dreamContent) {
        const recommendations = [];
        const lowerContent = dreamContent.toLowerCase();
        if (lowerContent.includes('自然') || lowerContent.includes('动物') || lowerContent.includes('水') || lowerContent.includes('山')) {
            recommendations.push({
                donnyType: 'taoist',
                confidence: 0.8,
                reasoning: '你的梦境包含自然元素，道家解梦擅长从天人合一的角度解析'
            });
        }
        if (lowerContent.includes('恐惧') || lowerContent.includes('压力') || lowerContent.includes('童年') || lowerContent.includes('父母')) {
            recommendations.push({
                donnyType: 'freud',
                confidence: 0.85,
                reasoning: '你的梦境涉及心理层面，心理学解梦能深入分析潜意识'
            });
        }
        if (lowerContent.includes('感情') || lowerContent.includes('朋友') || lowerContent.includes('家人') || lowerContent.includes('困惑')) {
            recommendations.push({
                donnyType: 'papa',
                confidence: 0.75,
                reasoning: '你的梦境涉及人际关系和情感，共情解梦提供温暖支持'
            });
        }
        if (recommendations.length === 0) {
            recommendations.push({
                donnyType: 'papa',
                confidence: 0.6,
                reasoning: '基于梦境的综合特点，推荐温暖共情的解梦方式'
            });
        }
        return recommendations.sort((a, b) => b.confidence - a.confidence);
    }
    isDreamInput(content) {
        const dreamKeywords = ['梦见', '梦到', '做梦', '梦里', '梦中', '睡觉时', '昨晚梦'];
        return dreamKeywords.some(keyword => content.includes(keyword)) && content.length > 10;
    }
    isDonnySelection(content) {
        const selectionKeywords = ['道家', 'taoist', '心理', 'freud', '暖心', 'papa', '选择', '推荐'];
        return selectionKeywords.some(keyword => content.toLowerCase().includes(keyword));
    }
    parseDonnySelection(content) {
        const lowerContent = content.toLowerCase();
        if (lowerContent.includes('道家') || lowerContent.includes('taoist')) {
            return 'taoist';
        }
        if (lowerContent.includes('心理') || lowerContent.includes('freud')) {
            return 'freud';
        }
        if (lowerContent.includes('暖心') || lowerContent.includes('papa')) {
            return 'papa';
        }
        if (lowerContent.includes('推荐')) {
            return 'papa';
        }
        return null;
    }
    getDonnyDisplayName(donnyType) {
        const names = {
            'taoist': '道家解梦师',
            'freud': '心理学解梦师',
            'papa': '共情解梦师',
        };
        return names[donnyType] || '解梦专家';
    }
    async saveConversation(userId, message, response) {
        try {
            this.logger.log(`保存对话记录 - 用户: ${userId}`);
        }
        catch (error) {
            this.logger.error(`保存对话失败: ${error.message}`);
        }
    }
    getDefaultPersona() {
        return {
            name: 'Almighty Donny',
            description: '友善、博学、耐心且乐于助人的通用AI助手，担任Donny平台的总管家',
            traits: [
                '热情欢迎每位用户',
                '博学通达，了解平台所有功能',
                '擅长引导用户选择合适的业务型Donny',
                '记忆力强，能记住用户的关键信息和偏好',
                '严格遵守隐私边界',
                '解决方案导向，善于解决用户疑问',
                '使用友好、平易近人的语言风格'
            ],
            responsibilities: [
                '用户接待与引导',
                '解梦流程协调',
                '状态监控与中断恢复',
                '反馈收集与处理',
                '梦境分享与奖励评估'
            ]
        };
    }
    getDefaultTools() {
        return [
            {
                name: 'database_access',
                description: '读写用户数据、对话历史等',
            },
            {
                name: 'donny_dispatcher',
                description: '将解梦任务分派给特定的解梦Donny Agent',
            },
            {
                name: 'payment_coordinator',
                description: '协调支付流程',
            },
            {
                name: 'user_state_tracker',
                description: '监控解梦流程状态，检测中断和超时情况',
            },
            {
                name: 'feedback_manager',
                description: '收集和分发用户反馈',
            }
        ];
    }
    getUserFlowState(userId) {
        return this.userFlows.get(userId) || null;
    }
    cleanupExpiredFlows() {
        const now = new Date();
        const expirationTime = 24 * 60 * 60 * 1000;
        for (const [userId, flow] of this.userFlows) {
            if (now.getTime() - flow.lastActivity.getTime() > expirationTime) {
                this.userFlows.delete(userId);
                this.logger.log(`清理过期用户流程: ${userId}`);
            }
        }
    }
};
exports.AlmightyDonnyService = AlmightyDonnyService;
exports.AlmightyDonnyService = AlmightyDonnyService = AlmightyDonnyService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [agent_runtime_service_1.AgentRuntimeService,
        user_data_access_service_1.UserDataAccessService,
        conversation_data_access_service_1.ConversationDataAccessService,
        agent_data_access_service_1.AgentDataAccessService])
], AlmightyDonnyService);
//# sourceMappingURL=almighty-donny.service.js.map