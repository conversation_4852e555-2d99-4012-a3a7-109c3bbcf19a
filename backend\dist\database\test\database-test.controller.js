"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseTestController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const database_test_service_1 = require("./database-test.service");
let DatabaseTestController = class DatabaseTestController {
    constructor(databaseTestService) {
        this.databaseTestService = databaseTestService;
    }
    async runTests() {
        return this.databaseTestService.runDatabaseTests();
    }
    async getStats() {
        return this.databaseTestService.getDatabaseStats();
    }
};
exports.DatabaseTestController = DatabaseTestController;
__decorate([
    (0, common_1.Get)('run'),
    (0, swagger_1.ApiOperation)({ summary: '运行数据库功能测试' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '测试结果',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean' },
                results: { type: 'array' },
                errors: { type: 'array' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseTestController.prototype, "runTests", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取数据库统计信息' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '数据库统计信息',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseTestController.prototype, "getStats", null);
exports.DatabaseTestController = DatabaseTestController = __decorate([
    (0, swagger_1.ApiTags)('数据库测试'),
    (0, common_1.Controller)('test/database'),
    __metadata("design:paramtypes", [database_test_service_1.DatabaseTestService])
], DatabaseTestController);
//# sourceMappingURL=database-test.controller.js.map