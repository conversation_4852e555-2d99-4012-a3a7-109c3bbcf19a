"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EmbeddingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmbeddingService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let EmbeddingService = EmbeddingService_1 = class EmbeddingService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(EmbeddingService_1.name);
        this.defaultModel = 'text-embedding-ada-002';
        this.openaiApiKey = this.configService.get('OPENAI_API_KEY');
    }
    async generateEmbedding(request) {
        const { text, model = this.defaultModel } = request;
        try {
            const cleanText = this.preprocessText(text);
            if (!cleanText) {
                throw new Error('空文本无法生成向量');
            }
            const response = await this.callOpenAIEmbedding(cleanText, model);
            this.logger.log(`生成向量成功，维度: ${response.embedding.length}`);
            return response;
        }
        catch (error) {
            this.logger.error(`向量生成失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async generateBatchEmbeddings(texts, model = this.defaultModel) {
        const results = [];
        try {
            const batchSize = 100;
            for (let i = 0; i < texts.length; i += batchSize) {
                const batch = texts.slice(i, i + batchSize);
                const batchResults = await Promise.all(batch.map(text => this.generateEmbedding({ text, model })));
                results.push(...batchResults);
                if (i + batchSize < texts.length) {
                    await this.delay(100);
                }
            }
            this.logger.log(`批量生成 ${results.length} 个向量完成`);
            return results;
        }
        catch (error) {
            this.logger.error(`批量向量生成失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    cosineSimilarity(vectorA, vectorB) {
        if (vectorA.length !== vectorB.length) {
            throw new Error('向量维度不匹配');
        }
        let dotProduct = 0;
        let normA = 0;
        let normB = 0;
        for (let i = 0; i < vectorA.length; i++) {
            dotProduct += vectorA[i] * vectorB[i];
            normA += vectorA[i] * vectorA[i];
            normB += vectorB[i] * vectorB[i];
        }
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
    preprocessText(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }
        return text
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
            .slice(0, 8000);
    }
    async callOpenAIEmbedding(text, model) {
        if (!this.openaiApiKey) {
            throw new Error('OpenAI API密钥未配置');
        }
        const response = await fetch('https://api.openai.com/v1/embeddings', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.openaiApiKey}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                input: text,
                model: model,
            }),
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`OpenAI API请求失败: ${response.status} - ${errorData.error?.message || '未知错误'}`);
        }
        const data = await response.json();
        return {
            embedding: data.data[0].embedding,
            model: data.model,
            usage: data.usage,
        };
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    validateEmbeddingDimension(embedding, expectedDim = 1536) {
        return embedding && embedding.length === expectedDim;
    }
    getSupportedModels() {
        return [
            'text-embedding-ada-002',
            'text-embedding-3-small',
            'text-embedding-3-large',
        ];
    }
};
exports.EmbeddingService = EmbeddingService;
exports.EmbeddingService = EmbeddingService = EmbeddingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], EmbeddingService);
//# sourceMappingURL=embedding.service.js.map