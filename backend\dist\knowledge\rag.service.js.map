{"version": 3, "file": "rag.service.js", "sourceRoot": "", "sources": ["../../src/knowledge/rag.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2DAAuD;AACvD,sGAAgG;AAYzF,IAAM,UAAU,kBAAhB,MAAM,UAAU;IAWrB,YACU,gBAAkC,EAClC,mBAA+C;QAD/C,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,wBAAmB,GAAnB,mBAAmB,CAA4B;QAZxC,WAAM,GAAG,IAAI,eAAM,CAAC,YAAU,CAAC,IAAI,CAAC,CAAC;QAErC,kBAAa,GAAsB;YAClD,kBAAkB,EAAE,IAAI;YACxB,oBAAoB,EAAE,GAAG;YACzB,kBAAkB,EAAE,CAAC;YACrB,eAAe,EAAE,wBAAwB;YACzC,gBAAgB,EAAE,eAAe;SAClC,CAAC;IAKC,CAAC;IAKJ,KAAK,CAAC,UAAU,CACd,KAAa,EACb,SAAoB,EACpB,MAAe,EACf,MAAmC;QAEnC,MAAM,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,MAAM,EAAE,CAAC;QAEzD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;YAGrD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;gBACnE,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,WAAW,CAAC,eAAe;aACnC,CAAC,CAAC;YAGH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CACzE,cAAc,CAAC,SAAS,EACxB,SAAS,EACT,WAAW,CAAC,oBAAoB,EAChC,WAAW,CAAC,kBAAkB,CAC/B,CAAC;YAGF,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YAGnE,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAC;YAGjF,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAC3D,KAAK,EACL,OAAO,EACP,SAAS,EACT,WAAW,CAAC,gBAAgB,CAC7B,CAAC;YAEF,MAAM,QAAQ,GAAgB;gBAC5B,cAAc,EAAE,aAAa;gBAC7B,kBAAkB,EAAE,iBAAiB;gBACrC,YAAY,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1C,mBAAmB,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;aAClD,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,aAAa,CAAC,MAAM,MAAM,CAAC,CAAC;YAC1D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,OAA+B,EAAE,MAAe;QACpE,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;YAG3D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;gBACnE,IAAI,EAAE,OAAO,CAAC,KAAK;aACpB,CAAC,CAAC;YAGH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CACnE,cAAc,CAAC,SAAS,EACxB,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,eAAe,IAAI,GAAG,EAC9B,OAAO,CAAC,WAAW,IAAI,EAAE,CAC1B,CAAC;YAGF,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;YAGnF,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAEtF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,eAAe,CAAC,MAAM,MAAM,CAAC,CAAC;YACzD,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CACrB,OAAoC,EACpC,MAAe;QAEf,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;YAG/E,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;gBACtE,IAAI,EAAE,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;aAC5C,CAAC,CAAC;YAGH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAC/D,OAAO,EACP,iBAAiB,CAAC,SAAS,CAC5B,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAC5B,OAAsC,EACtC,MAAe;QAEf,MAAM,OAAO,GAAqB,EAAE,CAAC;QAErC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,OAAO,CAAC,MAAM,QAAQ,CAAC,CAAC;YAGlD,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;YACxE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;YAG9E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxC,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAC/D,OAAO,CAAC,CAAC,CAAC,EACV,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CACxB,CAAC;oBACF,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAExD,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;YACrE,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,eAAuB;QAChD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,eAAe,EAAE,CAAC,CAAC;YAGjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;YAE1F,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,UAAU,GAAG,CAAC,CAAC;YAGnB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC;wBACxC,KAAK,EAAE,KAAK,CAAC,KAAK;wBAClB,OAAO,EAAE,KAAK,CAAC,OAAO;qBACQ,CAAC,CAAC;oBAElC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;wBACtE,IAAI;qBACL,CAAC,CAAC;oBAEH,MAAM,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CACjD,KAAK,CAAC,EAAE,EACR,EAAE,EACF,iBAAiB,CAAC,SAAS,CAC5B,CAAC;oBAEF,YAAY,EAAE,CAAC;gBACjB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC7D,UAAU,EAAE,CAAC;gBACf,CAAC;gBAGD,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACvB,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,YAAY,OAAO,UAAU,EAAE,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,uBAAuB,CAAC,OAA8D;QAC5F,MAAM,KAAK,GAAG,EAAE,CAAC;QAEjB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,KAAK,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,KAAK,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACvC,CAAC;QAGD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;iBAC7C,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;iBACzE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,KAAK,EAAE,CAAC;iBACzC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,CAAC,IAAI,CAAC,OAAO,OAAO,EAAE,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAKO,YAAY,CAAC,OAAuB,EAAE,SAAiB;QAC7D,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,eAAe,GAAG,CAAC,CAAC;QAExB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAEnD,IAAI,eAAe,GAAG,aAAa,GAAG,SAAS,EAAE,CAAC;gBAChD,MAAM;YACR,CAAC;YAED,OAAO,IAAI,OAAO,GAAG,MAAM,CAAC;YAC5B,eAAe,IAAI,aAAa,CAAC;QACnC,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;IACxB,CAAC;IAKO,oBAAoB,CAAC,MAAoB;QAC/C,MAAM,KAAK,GAAG,EAAE,CAAC;QAEjB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QAClC,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE3B,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAKO,KAAK,CAAC,wBAAwB,CACpC,KAAa,EACb,OAAe,EACf,SAAoB,EACpB,KAAa;QAMb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,mBAAmB,CACzB,OAAuB,EACvB,MAA4B;QAE5B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;gBACnD,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,KAAK,CAAC,cAAc,CAC1B,OAAuB,EACvB,KAAa,EACb,SAAqB,EACrB,MAAe;QAEf,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAC9C,SAAS,EACT,SAAS,CAAC,EAAE,EACZ,KAAK,EACL,SAAS,CAAC,UAAU,EACpB,cAAc,EACd,MAAM,CACP,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAKO,cAAc,CAAC,IAAY;QAEjC,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACnE,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;QAE9C,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,GAAG,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC;IACxD,CAAC;IAKO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF,CAAA;AArXY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;qCAaiB,oCAAgB;QACb,0DAA0B;GAb9C,UAAU,CAqXtB"}