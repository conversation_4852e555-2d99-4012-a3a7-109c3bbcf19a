import { Router } from 'express';
import jwt from 'jsonwebtoken';
import { AppError } from '../middlewares/errorHandler';
import config from '../utils/config';
import { getUserByWalletAddress, createUser } from '../services/userService';

const router = Router();

// 验证钱包签名并生成JWT令牌
router.post('/verify-signature', async (req, res, next) => {
  try {
    const { walletAddress, signature, message } = req.body;

    if (!walletAddress || !signature || !message) {
      throw new AppError('Missing required fields', 400);
    }

    // TODO: 实现实际的签名验证逻辑
    // 这里简化处理，实际项目中应该验证签名
    const isValidSignature = true;

    if (!isValidSignature) {
      throw new AppError('Invalid signature', 401);
    }

    // 查找或创建用户
    let user = await getUserByWalletAddress(walletAddress);

    if (!user) {
      user = await createUser(walletAddress);
    }

    // 生成JWT令牌
    const token = jwt.sign(
      { userId: user.id, walletAddress },
      config.jwtSecret,
      { expiresIn: '7d' }
    );

    res.json({
      status: 'success',
      data: {
        token,
        user: {
          userId: user.id,
          walletAddress: user.walletAddress,
          username: user.username,
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

// 更新用户名
router.patch('/username', async (req, res, next) => {
  try {
    const { userId } = req.body;
    const { username } = req.body;

    if (!userId || !username) {
      throw new AppError('Missing required fields', 400);
    }

    // TODO: 实现用户名更新逻辑

    res.json({
      status: 'success',
      data: {
        message: 'Username updated successfully',
      },
    });
  } catch (error) {
    next(error);
  }
});

export default router;
