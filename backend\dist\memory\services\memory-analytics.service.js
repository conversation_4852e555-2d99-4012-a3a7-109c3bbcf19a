"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MemoryAnalyticsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryAnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../../database/database.service");
let MemoryAnalyticsService = MemoryAnalyticsService_1 = class MemoryAnalyticsService {
    constructor(databaseService) {
        this.databaseService = databaseService;
        this.logger = new common_1.Logger(MemoryAnalyticsService_1.name);
        this.insightThresholds = {
            patternMinOccurrences: 3,
            emotionalStateConfidence: 0.7,
            preferenceMinSamples: 5,
        };
        this.logger.log('记忆分析服务初始化完成');
    }
    async generateInsights(agentType, userId) {
        this.logger.log(`生成记忆洞察 - Agent: ${agentType}, 用户: ${userId}`);
        try {
            const insights = [];
            const [patternInsights, emotionalInsights, preferenceInsights, behavioralInsights, temporalInsights,] = await Promise.all([
                this.generatePatternInsights(agentType, userId),
                this.generateEmotionalInsights(agentType, userId),
                this.generatePreferenceInsights(agentType, userId),
                this.generateBehavioralInsights(agentType, userId),
                this.generateTemporalInsights(agentType, userId),
            ]);
            insights.push(...patternInsights, ...emotionalInsights, ...preferenceInsights, ...behavioralInsights, ...temporalInsights);
            insights.sort((a, b) => b.relevanceScore - a.relevanceScore);
            const limitedInsights = insights.slice(0, 20);
            this.logger.log(`生成了${limitedInsights.length}个洞察`);
            return limitedInsights;
        }
        catch (error) {
            this.logger.error(`生成记忆洞察失败: ${error.message}`);
            throw error;
        }
    }
    async recordEvent(event) {
        try {
            const { error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'memory_evolution_events',
                operation: 'insert',
                data: {
                    id: event.id,
                    memory_id: event.memoryId,
                    event_type: event.eventType,
                    old_value: event.oldValue,
                    new_value: event.newValue,
                    reason: event.reason,
                    confidence: event.confidence,
                    timestamp: event.timestamp.toISOString(),
                    agent_type: event.agentType,
                    user_id: event.userId,
                },
            });
            if (error) {
                this.logger.warn(`记录记忆事件失败: ${error.message}`);
            }
        }
        catch (error) {
            this.logger.warn(`记录记忆事件异常: ${error.message}`);
        }
    }
    async getMemoryEvolutionHistory(memoryId, agentType, userId, timeRange) {
        try {
            const filters = {};
            if (memoryId)
                filters.memory_id = memoryId;
            if (agentType)
                filters.agent_type = agentType;
            if (userId)
                filters.user_id = userId;
            if (timeRange) {
                filters.timestamp = {
                    $gte: timeRange.start.toISOString(),
                    $lte: timeRange.end.toISOString(),
                };
            }
            const { data, error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'memory_evolution_events',
                operation: 'select',
                filters,
            });
            if (error) {
                throw new Error(`查询演进历史失败: ${error.message}`);
            }
            return (data || []).map(row => ({
                id: row.id,
                memoryId: row.memory_id,
                eventType: row.event_type,
                oldValue: row.old_value,
                newValue: row.new_value,
                reason: row.reason,
                confidence: row.confidence,
                timestamp: new Date(row.timestamp),
                agentType: row.agent_type,
                userId: row.user_id,
            }));
        }
        catch (error) {
            this.logger.error(`获取记忆演进历史失败: ${error.message}`);
            return [];
        }
    }
    async generatePatternInsights(agentType, userId) {
        const insights = [];
        try {
            const { data: memories, error } = await this.databaseService.executeWithPermission(agentType, {
                table: 'agent_memories',
                operation: 'select',
                filters: {
                    agent_type: agentType,
                    user_id: userId,
                },
            });
            if (error || !memories) {
                return insights;
            }
            const conversationPatterns = this.analyzeConversationPatterns(memories);
            if (conversationPatterns.length > 0) {
                insights.push({
                    id: `pattern_conversation_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'conversation_pattern',
                    title: '对话模式分析',
                    description: this.formatPatternDescription(conversationPatterns),
                    confidence: 0.8,
                    supportingMemoryIds: conversationPatterns.map(p => p.memoryId),
                    generatedAt: new Date(),
                    relevanceScore: 0.85,
                });
            }
            const timePatterns = this.analyzeTimePatterns(memories);
            if (timePatterns.active) {
                insights.push({
                    id: `pattern_time_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'time_pattern',
                    title: '活跃时间模式',
                    description: `用户在${timePatterns.peakHours.join(', ')}点最活跃，主要活动时段为${timePatterns.activeRange}`,
                    confidence: 0.75,
                    supportingMemoryIds: timePatterns.supportingMemoryIds,
                    generatedAt: new Date(),
                    relevanceScore: 0.7,
                });
            }
            const topicPatterns = this.analyzeTopicPatterns(memories);
            if (topicPatterns.length > 0) {
                insights.push({
                    id: `pattern_topic_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'topic_pattern',
                    title: '主题偏好模式',
                    description: `用户经常讨论的主题包括: ${topicPatterns.slice(0, 3).map(t => t.topic).join(', ')}`,
                    confidence: 0.8,
                    supportingMemoryIds: topicPatterns.flatMap(p => p.memoryIds),
                    generatedAt: new Date(),
                    relevanceScore: 0.8,
                });
            }
        }
        catch (error) {
            this.logger.warn(`生成模式洞察失败: ${error.message}`);
        }
        return insights;
    }
    async generateEmotionalInsights(agentType, userId) {
        const insights = [];
        try {
            const { data: emotionalMemories, error } = await this.databaseService.executeWithPermission(agentType, {
                table: 'agent_memories',
                operation: 'select',
                filters: {
                    agent_type: agentType,
                    user_id: userId,
                    'metadata->sentiment': { $ne: null },
                },
            });
            if (error || !emotionalMemories || emotionalMemories.length < 3) {
                return insights;
            }
            const emotionalTrend = this.analyzeEmotionalTrend(emotionalMemories);
            if (emotionalTrend.confidence > this.insightThresholds.emotionalStateConfidence) {
                insights.push({
                    id: `emotion_trend_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'emotional_trend',
                    title: '情感状态趋势',
                    description: emotionalTrend.description,
                    confidence: emotionalTrend.confidence,
                    supportingMemoryIds: emotionalTrend.supportingMemoryIds,
                    generatedAt: new Date(),
                    relevanceScore: 0.9,
                });
            }
            const emotionalStability = this.analyzeEmotionalStability(emotionalMemories);
            if (emotionalStability.insight) {
                insights.push({
                    id: `emotion_stability_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'emotional_stability',
                    title: '情感稳定性分析',
                    description: emotionalStability.description,
                    confidence: emotionalStability.confidence,
                    supportingMemoryIds: emotionalStability.supportingMemoryIds,
                    generatedAt: new Date(),
                    relevanceScore: 0.75,
                });
            }
        }
        catch (error) {
            this.logger.warn(`生成情感洞察失败: ${error.message}`);
        }
        return insights;
    }
    async generatePreferenceInsights(agentType, userId) {
        const insights = [];
        try {
            const { data: preferenceMemories, error } = await this.databaseService.executeWithPermission(agentType, {
                table: 'agent_memories',
                operation: 'select',
                filters: {
                    agent_type: agentType,
                    user_id: userId,
                    'metadata->category': 'preference',
                },
            });
            if (error || !preferenceMemories || preferenceMemories.length < this.insightThresholds.preferenceMinSamples) {
                return insights;
            }
            const preferenceEvolution = this.analyzePreferenceEvolution(preferenceMemories);
            if (preferenceEvolution.changes.length > 0) {
                insights.push({
                    id: `preference_evolution_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'preference_evolution',
                    title: '偏好演变分析',
                    description: `检测到${preferenceEvolution.changes.length}个偏好变化：${preferenceEvolution.description}`,
                    confidence: 0.8,
                    supportingMemoryIds: preferenceEvolution.supportingMemoryIds,
                    generatedAt: new Date(),
                    relevanceScore: 0.8,
                });
            }
            const preferenceStrength = this.analyzePreferenceStrength(preferenceMemories);
            if (preferenceStrength.strongPreferences.length > 0) {
                insights.push({
                    id: `preference_strength_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'preference_strength',
                    title: '偏好强度分析',
                    description: `识别出${preferenceStrength.strongPreferences.length}个强偏好：${preferenceStrength.description}`,
                    confidence: 0.85,
                    supportingMemoryIds: preferenceStrength.supportingMemoryIds,
                    generatedAt: new Date(),
                    relevanceScore: 0.85,
                });
            }
        }
        catch (error) {
            this.logger.warn(`生成偏好洞察失败: ${error.message}`);
        }
        return insights;
    }
    async generateBehavioralInsights(agentType, userId) {
        const insights = [];
        try {
            const { data: interactionHistory, error } = await this.databaseService.executeWithPermission(agentType, {
                table: 'agent_memories',
                operation: 'select',
                columns: 'id, created_at, access_count, metadata',
                filters: {
                    agent_type: agentType,
                    user_id: userId,
                },
            });
            if (error || !interactionHistory) {
                return insights;
            }
            const interactionPattern = this.analyzeInteractionFrequency(interactionHistory);
            if (interactionPattern.insight) {
                insights.push({
                    id: `behavior_interaction_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'interaction_pattern',
                    title: '交互行为模式',
                    description: interactionPattern.description,
                    confidence: 0.8,
                    supportingMemoryIds: interactionPattern.supportingMemoryIds,
                    generatedAt: new Date(),
                    relevanceScore: 0.7,
                });
            }
            const reviewBehavior = this.analyzeMemoryReviewBehavior(interactionHistory);
            if (reviewBehavior.insight) {
                insights.push({
                    id: `behavior_review_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'review_behavior',
                    title: '记忆回顾行为',
                    description: reviewBehavior.description,
                    confidence: 0.75,
                    supportingMemoryIds: reviewBehavior.supportingMemoryIds,
                    generatedAt: new Date(),
                    relevanceScore: 0.65,
                });
            }
        }
        catch (error) {
            this.logger.warn(`生成行为洞察失败: ${error.message}`);
        }
        return insights;
    }
    async generateTemporalInsights(agentType, userId) {
        const insights = [];
        try {
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const { data: recentMemories, error } = await this.databaseService.executeWithPermission(agentType, {
                table: 'agent_memories',
                operation: 'select',
                filters: {
                    agent_type: agentType,
                    user_id: userId,
                    created_at: { $gte: thirtyDaysAgo.toISOString() },
                },
            });
            if (error || !recentMemories || recentMemories.length < 5) {
                return insights;
            }
            const creationRhythm = this.analyzeMemoryCreationRhythm(recentMemories);
            if (creationRhythm.insight) {
                insights.push({
                    id: `temporal_rhythm_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'creation_rhythm',
                    title: '记忆创建节奏',
                    description: creationRhythm.description,
                    confidence: 0.75,
                    supportingMemoryIds: creationRhythm.supportingMemoryIds,
                    generatedAt: new Date(),
                    relevanceScore: 0.6,
                });
            }
            const densityChange = this.analyzeMemoryDensityChange(recentMemories);
            if (densityChange.insight) {
                insights.push({
                    id: `temporal_density_${Date.now()}`,
                    userId,
                    agentType,
                    type: 'memory_density',
                    title: '记忆密度变化',
                    description: densityChange.description,
                    confidence: 0.7,
                    supportingMemoryIds: densityChange.supportingMemoryIds,
                    generatedAt: new Date(),
                    relevanceScore: 0.65,
                });
            }
        }
        catch (error) {
            this.logger.warn(`生成时间洞察失败: ${error.message}`);
        }
        return insights;
    }
    analyzeConversationPatterns(memories) {
        const patterns = [];
        const conversationMemories = memories.filter(m => m.metadata?.category === 'conversation');
        if (conversationMemories.length >= this.insightThresholds.patternMinOccurrences) {
            patterns.push({
                pattern: 'frequent_conversation',
                frequency: conversationMemories.length,
                memoryId: conversationMemories[0].id,
            });
        }
        return patterns;
    }
    analyzeTimePatterns(memories) {
        const hours = memories.map(m => new Date(m.created_at).getHours());
        const hourCounts = hours.reduce((acc, hour) => {
            acc[hour] = (acc[hour] || 0) + 1;
            return acc;
        }, {});
        const peakHours = Object.entries(hourCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 3)
            .map(([hour]) => parseInt(hour));
        return {
            active: peakHours.length > 0,
            peakHours,
            activeRange: `${Math.min(...peakHours)}-${Math.max(...peakHours)}时`,
            supportingMemoryIds: memories.slice(0, 5).map(m => m.id),
        };
    }
    analyzeTopicPatterns(memories) {
        const topics = {};
        memories.forEach(memory => {
            if (memory.metadata?.tags) {
                memory.metadata.tags.forEach((tag) => {
                    if (!topics[tag]) {
                        topics[tag] = { count: 0, memoryIds: [] };
                    }
                    topics[tag].count++;
                    topics[tag].memoryIds.push(memory.id);
                });
            }
        });
        return Object.entries(topics)
            .filter(([, data]) => data.count >= this.insightThresholds.patternMinOccurrences)
            .map(([topic, data]) => ({ topic, ...data }))
            .sort((a, b) => b.count - a.count);
    }
    analyzeEmotionalTrend(memories) {
        const sentiments = memories
            .filter(m => m.metadata?.sentiment !== undefined)
            .map(m => ({ sentiment: m.metadata.sentiment, date: new Date(m.created_at) }))
            .sort((a, b) => a.date.getTime() - b.date.getTime());
        if (sentiments.length < 3) {
            return { confidence: 0 };
        }
        const avgSentiment = sentiments.reduce((sum, s) => sum + s.sentiment, 0) / sentiments.length;
        const trend = sentiments[sentiments.length - 1].sentiment - sentiments[0].sentiment;
        return {
            confidence: 0.8,
            description: trend > 0.1 ? '情感趋势向积极方向发展' :
                trend < -0.1 ? '情感趋势向消极方向发展' : '情感状态相对稳定',
            supportingMemoryIds: memories.slice(0, 5).map(m => m.id),
        };
    }
    analyzeEmotionalStability(memories) {
        const sentiments = memories
            .filter(m => m.metadata?.sentiment !== undefined)
            .map(m => m.metadata.sentiment);
        if (sentiments.length < 3) {
            return { insight: false };
        }
        const mean = sentiments.reduce((sum, s) => sum + s, 0) / sentiments.length;
        const variance = sentiments.reduce((sum, s) => sum + Math.pow(s - mean, 2), 0) / sentiments.length;
        const stability = 1 - Math.min(variance, 1);
        return {
            insight: true,
            confidence: 0.75,
            description: stability > 0.7 ? '情感状态比较稳定' : '情感波动较大',
            supportingMemoryIds: memories.slice(0, 5).map(m => m.id),
        };
    }
    analyzePreferenceEvolution(memories) {
        return {
            changes: memories.length > 5 ? ['偏好有所变化'] : [],
            description: '偏好相对稳定',
            supportingMemoryIds: memories.slice(0, 3).map(m => m.id),
        };
    }
    analyzePreferenceStrength(memories) {
        return {
            strongPreferences: memories.length > 3 ? ['明确偏好'] : [],
            description: '具有明确的偏好倾向',
            supportingMemoryIds: memories.slice(0, 3).map(m => m.id),
        };
    }
    analyzeInteractionFrequency(memories) {
        const totalInteractions = memories.reduce((sum, m) => sum + (m.access_count || 0), 0);
        const avgInteractions = totalInteractions / memories.length;
        return {
            insight: avgInteractions > 2,
            description: avgInteractions > 2 ? '积极的交互模式' : '交互相对较少',
            supportingMemoryIds: memories.slice(0, 5).map(m => m.id),
        };
    }
    analyzeMemoryReviewBehavior(memories) {
        const highAccessMemories = memories.filter(m => (m.access_count || 0) > 3);
        return {
            insight: highAccessMemories.length > 0,
            description: `有${highAccessMemories.length}个记忆被频繁回顾`,
            supportingMemoryIds: highAccessMemories.slice(0, 3).map(m => m.id),
        };
    }
    analyzeMemoryCreationRhythm(memories) {
        const dates = memories.map(m => new Date(m.created_at).toDateString());
        const uniqueDates = new Set(dates);
        const dailyAverage = memories.length / uniqueDates.size;
        return {
            insight: dailyAverage > 1,
            description: `平均每天创建${dailyAverage.toFixed(1)}个记忆`,
            supportingMemoryIds: memories.slice(0, 5).map(m => m.id),
        };
    }
    analyzeMemoryDensityChange(memories) {
        const sortedMemories = memories.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        const firstHalf = sortedMemories.slice(0, Math.floor(sortedMemories.length / 2));
        const secondHalf = sortedMemories.slice(Math.floor(sortedMemories.length / 2));
        const densityChange = secondHalf.length > firstHalf.length;
        return {
            insight: Math.abs(secondHalf.length - firstHalf.length) > 2,
            description: densityChange ? '记忆创建密度增加' : '记忆创建密度减少',
            supportingMemoryIds: memories.slice(0, 5).map(m => m.id),
        };
    }
    formatPatternDescription(patterns) {
        return patterns.map(p => `发现${p.pattern}模式，频率：${p.frequency}`).join('；');
    }
};
exports.MemoryAnalyticsService = MemoryAnalyticsService;
exports.MemoryAnalyticsService = MemoryAnalyticsService = MemoryAnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], MemoryAnalyticsService);
//# sourceMappingURL=memory-analytics.service.js.map