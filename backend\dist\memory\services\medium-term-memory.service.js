"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MediumTermMemoryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediumTermMemoryService = void 0;
const common_1 = require("@nestjs/common");
const memory_types_1 = require("../memory.types");
const database_service_1 = require("../../database/database.service");
const embedding_service_1 = require("../../knowledge/embedding.service");
let MediumTermMemoryService = MediumTermMemoryService_1 = class MediumTermMemoryService {
    constructor(databaseService, embeddingService) {
        this.databaseService = databaseService;
        this.embeddingService = embeddingService;
        this.logger = new common_1.Logger(MediumTermMemoryService_1.name);
        this.reinforcementThreshold = 3;
        this.decayRate = 0.95;
        this.consolidationThreshold = 0.8;
        this.logger.log('中期记忆服务初始化完成');
    }
    async store(memoryData) {
        this.logger.debug(`存储中期记忆 - 用户: ${memoryData.userId}, Agent: ${memoryData.agentType}`);
        try {
            const embedding = await this.generateEmbedding(memoryData.content);
            const memory = {
                ...memoryData,
                id: memoryData.id || `mtm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                memoryType: memory_types_1.MemoryType.MEDIUM_TERM,
                reinforcementCount: memoryData.reinforcementCount || 0,
                lastReinforcedAt: memoryData.lastReinforcedAt || new Date(),
                decayRate: memoryData.decayRate || this.decayRate,
                consolidationScore: memoryData.consolidationScore || this.calculateInitialConsolidationScore(memoryData),
                embedding,
                accessCount: 0,
                createdAt: new Date(),
                updatedAt: new Date(),
                lastAccessedAt: new Date(),
                expiresAt: memoryData.expiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            };
            const { data, error } = await this.databaseService.executeWithPermission(memory.agentType, {
                table: 'agent_memories',
                operation: 'insert',
                data: {
                    id: memory.id,
                    agent_type: memory.agentType,
                    user_id: memory.userId,
                    session_id: memory.sessionId,
                    memory_type: memory.memoryType,
                    importance: memory.importance,
                    content: memory.content,
                    metadata: {
                        ...memory.metadata,
                        reinforcementCount: memory.reinforcementCount,
                        lastReinforcedAt: memory.lastReinforcedAt.toISOString(),
                        decayRate: memory.decayRate,
                        consolidationScore: memory.consolidationScore,
                    },
                    embedding: memory.embedding,
                    access_count: memory.accessCount,
                    last_accessed_at: memory.lastAccessedAt.toISOString(),
                    created_at: memory.createdAt.toISOString(),
                    updated_at: memory.updatedAt.toISOString(),
                    expires_at: memory.expiresAt?.toISOString(),
                },
            });
            if (error) {
                throw new Error(`数据库存储失败: ${error.message}`);
            }
            this.logger.debug(`中期记忆存储成功 - ID: ${memory.id}`);
            return memory;
        }
        catch (error) {
            this.logger.error(`存储中期记忆失败: ${error.message}`);
            throw error;
        }
    }
    async retrieve(query) {
        this.logger.debug(`检索中期记忆 - 查询: ${JSON.stringify(query)}`);
        try {
            let results = [];
            if (query.searchText || query.embedding) {
                results = await this.semanticSearch(query);
            }
            else {
                results = await this.queryDatabase(query);
            }
            results = await this.applyDecayCalculation(results);
            results.sort((a, b) => b.consolidationScore - a.consolidationScore);
            const limitedResults = query.limit
                ? results.slice(query.offset || 0, (query.offset || 0) + query.limit)
                : results;
            await this.reinforceMemories(limitedResults);
            return limitedResults;
        }
        catch (error) {
            this.logger.error(`检索中期记忆失败: ${error.message}`);
            throw error;
        }
    }
    async semanticSearch(query) {
        this.logger.debug('执行中期记忆语义搜索');
        try {
            let queryEmbedding;
            if (query.embedding) {
                queryEmbedding = query.embedding;
            }
            else if (query.searchText) {
                const embeddingResponse = await this.embeddingService.generateEmbedding({ text: query.searchText });
                queryEmbedding = embeddingResponse.embedding;
            }
            else {
                throw new Error('语义搜索需要提供搜索文本或向量');
            }
            const results = await this.queryDatabase(query);
            if (queryEmbedding.length > 0) {
                return results
                    .filter(memory => memory.embedding && memory.embedding.length > 0)
                    .map(memory => ({
                    ...memory,
                    similarity: this.calculateSimilarity(queryEmbedding, memory.embedding),
                }))
                    .filter(memory => memory.similarity >= (query.similarityThreshold || 0.7))
                    .sort((a, b) => b.similarity - a.similarity);
            }
            return results;
        }
        catch (error) {
            this.logger.error(`语义搜索失败: ${error.message}`);
            return await this.queryDatabase(query);
        }
    }
    async update(memoryId, updates) {
        this.logger.debug(`更新中期记忆 - ID: ${memoryId}`);
        try {
            const existing = await this.findById(memoryId);
            if (!existing) {
                throw new Error(`中期记忆不存在: ${memoryId}`);
            }
            let embedding = existing.embedding;
            if (updates.content && updates.content !== existing.content) {
                embedding = await this.generateEmbedding(updates.content);
            }
            const updated = {
                ...existing,
                ...updates,
                embedding,
                updatedAt: new Date(),
            };
            const { error } = await this.databaseService.executeWithPermission(updated.agentType, {
                table: 'agent_memories',
                operation: 'update',
                data: {
                    content: updated.content,
                    metadata: {
                        ...updated.metadata,
                        reinforcementCount: updated.reinforcementCount,
                        lastReinforcedAt: updated.lastReinforcedAt.toISOString(),
                        decayRate: updated.decayRate,
                        consolidationScore: updated.consolidationScore,
                    },
                    importance: updated.importance,
                    embedding: updated.embedding,
                    access_count: updated.accessCount,
                    last_accessed_at: updated.lastAccessedAt.toISOString(),
                    updated_at: updated.updatedAt.toISOString(),
                },
                filters: { id: memoryId },
            });
            if (error) {
                throw new Error(`数据库更新失败: ${error.message}`);
            }
            return updated;
        }
        catch (error) {
            this.logger.error(`更新中期记忆失败: ${error.message}`);
            throw error;
        }
    }
    async delete(memoryId) {
        this.logger.debug(`删除中期记忆 - ID: ${memoryId}`);
        try {
            const { error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'delete',
                filters: { id: memoryId },
            });
            if (error) {
                this.logger.warn(`数据库删除失败: ${error.message}`);
                return false;
            }
            return true;
        }
        catch (error) {
            this.logger.error(`删除中期记忆失败: ${error.message}`);
            return false;
        }
    }
    async findById(memoryId) {
        try {
            const { data, error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'select',
                filters: {
                    id: memoryId,
                    memory_type: memory_types_1.MemoryType.MEDIUM_TERM,
                },
            });
            if (error || !data || data.length === 0) {
                return null;
            }
            return this.mapDbRowToMemory(data[0]);
        }
        catch (error) {
            this.logger.error(`查找中期记忆失败: ${error.message}`);
            return null;
        }
    }
    async cleanupLowImportanceMemories() {
        this.logger.log('开始清理低重要性的中期记忆');
        try {
            const now = new Date();
            const cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            const { data, error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'delete',
                filters: {
                    memory_type: memory_types_1.MemoryType.MEDIUM_TERM,
                    importance: memory_types_1.MemoryImportance.LOW,
                    created_at: { $lt: cutoffDate.toISOString() },
                    access_count: { $lt: 3 },
                },
            });
            const deletedCount = data?.affectedRows || 0;
            this.logger.log(`清理完成 - 删除了${deletedCount}条低重要性记忆`);
            return deletedCount;
        }
        catch (error) {
            this.logger.error(`清理低重要性记忆失败: ${error.message}`);
            return 0;
        }
    }
    async getConsolidationCandidates(limit = 100) {
        try {
            const results = await this.queryDatabase({
                memoryType: memory_types_1.MemoryType.MEDIUM_TERM,
                limit,
            });
            return results.filter(memory => memory.consolidationScore >= this.consolidationThreshold);
        }
        catch (error) {
            this.logger.error(`获取巩固候选失败: ${error.message}`);
            return [];
        }
    }
    async getStats(agentType, userId) {
        try {
            const filters = { memory_type: memory_types_1.MemoryType.MEDIUM_TERM };
            if (agentType)
                filters.agent_type = agentType;
            if (userId)
                filters.user_id = userId;
            const { data, error } = await this.databaseService.executeWithPermission('almighty', {
                table: 'agent_memories',
                operation: 'select',
                columns: 'importance, metadata, created_at, LENGTH(content::text) as content_size',
                filters,
            });
            if (error) {
                throw new Error(`获取统计失败: ${error.message}`);
            }
            const memories = data || [];
            const stats = {
                total: memories.length,
                byImportance: {},
                byCategory: {},
                storageUsed: 0,
                averageImportance: 0,
                oldestMemory: undefined,
                newestMemory: undefined,
            };
            if (memories.length === 0) {
                return stats;
            }
            let totalImportance = 0;
            let oldestDate = new Date();
            let newestDate = new Date(0);
            for (const memory of memories) {
                const importance = memory.importance || memory_types_1.MemoryImportance.NORMAL;
                stats.byImportance[importance] = (stats.byImportance[importance] || 0) + 1;
                totalImportance += importance;
                const category = memory.metadata?.category || 'general';
                stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
                stats.storageUsed += memory.content_size || 0;
                const createdAt = new Date(memory.created_at);
                if (createdAt < oldestDate)
                    oldestDate = createdAt;
                if (createdAt > newestDate)
                    newestDate = createdAt;
            }
            stats.averageImportance = totalImportance / memories.length;
            stats.oldestMemory = oldestDate;
            stats.newestMemory = newestDate;
            return stats;
        }
        catch (error) {
            this.logger.error(`获取统计信息失败: ${error.message}`);
            throw error;
        }
    }
    async generateEmbedding(content) {
        try {
            const text = typeof content === 'string' ? content : JSON.stringify(content);
            const response = await this.embeddingService.generateEmbedding({ text });
            return response.embedding;
        }
        catch (error) {
            this.logger.warn(`生成向量失败: ${error.message}`);
            return [];
        }
    }
    calculateInitialConsolidationScore(memoryData) {
        let score = 0.5;
        if (memoryData.importance) {
            score += memoryData.importance * 0.1;
        }
        if (memoryData.metadata?.sentiment) {
            score += Math.abs(memoryData.metadata.sentiment) * 0.2;
        }
        if (memoryData.metadata?.confidence) {
            score += memoryData.metadata.confidence * 0.2;
        }
        return Math.min(score, 1.0);
    }
    calculateSimilarity(vectorA, vectorB) {
        if (!vectorA || !vectorB || vectorA.length !== vectorB.length) {
            return 0;
        }
        let dotProduct = 0;
        let normA = 0;
        let normB = 0;
        for (let i = 0; i < vectorA.length; i++) {
            dotProduct += vectorA[i] * vectorB[i];
            normA += vectorA[i] * vectorA[i];
            normB += vectorB[i] * vectorB[i];
        }
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
    async applyDecayCalculation(memories) {
        const now = new Date();
        const updatedMemories = [];
        for (const memory of memories) {
            const daysSinceReinforced = (now.getTime() - memory.lastReinforcedAt.getTime()) / (24 * 60 * 60 * 1000);
            const decayedScore = memory.consolidationScore * Math.pow(memory.decayRate, daysSinceReinforced);
            if (Math.abs(decayedScore - memory.consolidationScore) > 0.01) {
                const updated = await this.update(memory.id, { consolidationScore: decayedScore });
                updatedMemories.push(updated);
            }
            else {
                updatedMemories.push(memory);
            }
        }
        return updatedMemories;
    }
    async reinforceMemories(memories) {
        for (const memory of memories) {
            const newReinforcementCount = memory.reinforcementCount + 1;
            const now = new Date();
            let newConsolidationScore = memory.consolidationScore;
            if (newReinforcementCount >= this.reinforcementThreshold) {
                newConsolidationScore = Math.min(memory.consolidationScore + 0.1, 1.0);
            }
            await this.update(memory.id, {
                reinforcementCount: newReinforcementCount,
                lastReinforcedAt: now,
                consolidationScore: newConsolidationScore,
                accessCount: memory.accessCount + 1,
                lastAccessedAt: now,
            });
        }
    }
    async queryDatabase(query) {
        const filters = { memory_type: memory_types_1.MemoryType.MEDIUM_TERM };
        if (query.agentType)
            filters.agent_type = query.agentType;
        if (query.userId)
            filters.user_id = query.userId;
        if (query.sessionId)
            filters.session_id = query.sessionId;
        if (query.category)
            filters['metadata->category'] = query.category;
        if (query.importance)
            filters.importance = query.importance;
        if (query.timeRange) {
            filters.created_at = {
                $gte: query.timeRange.start.toISOString(),
                $lte: query.timeRange.end.toISOString(),
            };
        }
        const { data, error } = await this.databaseService.executeWithPermission(query.agentType || 'almighty', {
            table: 'agent_memories',
            operation: 'select',
            filters,
        });
        if (error) {
            throw new Error(`数据库查询失败: ${error.message}`);
        }
        return (data || []).map(row => this.mapDbRowToMemory(row));
    }
    mapDbRowToMemory(row) {
        return {
            id: row.id,
            agentType: row.agent_type,
            userId: row.user_id,
            sessionId: row.session_id,
            memoryType: row.memory_type,
            importance: row.importance,
            content: row.content,
            metadata: row.metadata,
            embedding: row.embedding,
            reinforcementCount: row.metadata?.reinforcementCount || 0,
            lastReinforcedAt: new Date(row.metadata?.lastReinforcedAt || row.created_at),
            decayRate: row.metadata?.decayRate || this.decayRate,
            consolidationScore: row.metadata?.consolidationScore || 0.5,
            accessCount: row.access_count || 0,
            lastAccessedAt: new Date(row.last_accessed_at),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at),
            expiresAt: row.expires_at ? new Date(row.expires_at) : undefined,
        };
    }
};
exports.MediumTermMemoryService = MediumTermMemoryService;
exports.MediumTermMemoryService = MediumTermMemoryService = MediumTermMemoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService,
        embedding_service_1.EmbeddingService])
], MediumTermMemoryService);
//# sourceMappingURL=medium-term-memory.service.js.map