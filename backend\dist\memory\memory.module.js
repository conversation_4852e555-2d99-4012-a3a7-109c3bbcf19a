"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryModule = void 0;
const common_1 = require("@nestjs/common");
const database_module_1 = require("../database/database.module");
const knowledge_module_1 = require("../knowledge/knowledge.module");
const memory_manager_service_1 = require("./services/memory-manager.service");
const short_term_memory_service_1 = require("./services/short-term-memory.service");
const medium_term_memory_service_1 = require("./services/medium-term-memory.service");
const long_term_memory_service_1 = require("./services/long-term-memory.service");
const memory_consolidation_service_1 = require("./services/memory-consolidation.service");
const memory_analytics_service_1 = require("./services/memory-analytics.service");
const memory_controller_1 = require("./memory.controller");
let MemoryModule = class MemoryModule {
};
exports.MemoryModule = MemoryModule;
exports.MemoryModule = MemoryModule = __decorate([
    (0, common_1.Module)({
        imports: [
            database_module_1.DatabaseModule,
            knowledge_module_1.KnowledgeModule,
        ],
        providers: [
            memory_manager_service_1.MemoryManagerService,
            short_term_memory_service_1.ShortTermMemoryService,
            medium_term_memory_service_1.MediumTermMemoryService,
            long_term_memory_service_1.LongTermMemoryService,
            memory_consolidation_service_1.MemoryConsolidationService,
            memory_analytics_service_1.MemoryAnalyticsService,
        ],
        controllers: [memory_controller_1.MemoryController],
        exports: [
            memory_manager_service_1.MemoryManagerService,
            short_term_memory_service_1.ShortTermMemoryService,
            medium_term_memory_service_1.MediumTermMemoryService,
            long_term_memory_service_1.LongTermMemoryService,
            memory_consolidation_service_1.MemoryConsolidationService,
            memory_analytics_service_1.MemoryAnalyticsService,
        ],
    })
], MemoryModule);
//# sourceMappingURL=memory.module.js.map