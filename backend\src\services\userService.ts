import supabase from './supabase';
import { User } from '../types';
import { AppError } from '../middlewares/errorHandler';

// 根据钱包地址获取用户
export const getUserByWalletAddress = async (walletAddress: string): Promise<User | null> => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('wallet_address', walletAddress)
    .single();
  
  if (error) {
    if (error.code === 'PGRST116') {
      // 没有找到用户
      return null;
    }
    throw new AppError(`Error fetching user: ${error.message}`, 500);
  }
  
  return data ? {
    id: data.id,
    walletAddress: data.wallet_address,
    username: data.username,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
  } : null;
};

// 创建新用户
export const createUser = async (walletAddress: string, username?: string): Promise<User> => {
  const { data, error } = await supabase
    .from('users')
    .insert([
      { 
        wallet_address: walletAddress,
        username: username || `User_${walletAddress.substring(0, 6)}`,
      }
    ])
    .select()
    .single();
  
  if (error) {
    throw new AppError(`Error creating user: ${error.message}`, 500);
  }
  
  if (!data) {
    throw new AppError('Failed to create user', 500);
  }
  
  return {
    id: data.id,
    walletAddress: data.wallet_address,
    username: data.username,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
  };
};

// 更新用户信息
export const updateUser = async (userId: string, updates: { username?: string }): Promise<User> => {
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();
  
  if (error) {
    throw new AppError(`Error updating user: ${error.message}`, 500);
  }
  
  if (!data) {
    throw new AppError('User not found', 404);
  }
  
  return {
    id: data.id,
    walletAddress: data.wallet_address,
    username: data.username,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
  };
};
