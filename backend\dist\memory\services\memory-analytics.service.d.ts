import { MemoryInsight, MemoryEvolutionEvent } from '../memory.types';
import { AgentType } from '../../database/types/rag.types';
import { DatabaseService } from '../../database/database.service';
export declare class MemoryAnalyticsService {
    private readonly databaseService;
    private readonly logger;
    private readonly insightThresholds;
    constructor(databaseService: DatabaseService);
    generateInsights(agentType: AgentType, userId: string): Promise<MemoryInsight[]>;
    recordEvent(event: MemoryEvolutionEvent): Promise<void>;
    getMemoryEvolutionHistory(memoryId?: string, agentType?: AgentType, userId?: string, timeRange?: {
        start: Date;
        end: Date;
    }): Promise<MemoryEvolutionEvent[]>;
    private generatePatternInsights;
    private generateEmotionalInsights;
    private generatePreferenceInsights;
    private generateBehavioralInsights;
    private generateTemporalInsights;
    private analyzeConversationPatterns;
    private analyzeTimePatterns;
    private analyzeTopicPatterns;
    private analyzeEmotionalTrend;
    private analyzeEmotionalStability;
    private analyzePreferenceEvolution;
    private analyzePreferenceStrength;
    private analyzeInteractionFrequency;
    private analyzeMemoryReviewBehavior;
    private analyzeMemoryCreationRhythm;
    private analyzeMemoryDensityChange;
    private formatPatternDescription;
}
