export interface KnowledgeBase {
    id: string;
    name: string;
    description?: string;
    agent_type: AgentType;
    is_public: boolean;
    created_by?: string;
    created_at: Date;
    updated_at: Date;
}
export interface KnowledgeEntry {
    id: string;
    knowledge_base_id: string;
    title?: string;
    content: string;
    content_type: 'text' | 'markdown' | 'json';
    source_url?: string;
    metadata: Record<string, any>;
    embedding?: number[];
    embedding_model: string;
    created_at: Date;
    updated_at: Date;
}
export interface KnowledgeBasePermission {
    id: string;
    knowledge_base_id: string;
    agent_type: AgentType;
    permission_level: 'read' | 'write' | 'admin';
    created_at: Date;
}
export interface KnowledgeUsageLog {
    id: string;
    agent_type: AgentType;
    user_id?: string;
    knowledge_entry_id: string;
    query_text?: string;
    similarity_score: number;
    usage_context: 'conversation' | 'dream_analysis' | 'guidance';
    created_at: Date;
}
export interface SearchResult {
    id: string;
    title?: string;
    content: string;
    metadata: Record<string, any>;
    similarity: number;
}
export type AgentType = 'almighty' | 'taoist' | 'freud' | 'papa' | 'shared';
export interface EmbeddingRequest {
    text: string;
    model?: string;
}
export interface EmbeddingResponse {
    embedding: number[];
    model: string;
    usage?: {
        prompt_tokens: number;
        total_tokens: number;
    };
}
export interface SearchKnowledgeRequest {
    query: string;
    agent_type?: AgentType;
    match_threshold?: number;
    match_count?: number;
    filter_metadata?: Record<string, any>;
}
export interface CreateKnowledgeBaseRequest {
    name: string;
    description?: string;
    agent_type: AgentType;
    is_public?: boolean;
}
export interface CreateKnowledgeEntryRequest {
    knowledge_base_id: string;
    title?: string;
    content: string;
    content_type?: 'text' | 'markdown' | 'json';
    source_url?: string;
    metadata?: Record<string, any>;
}
export interface UpdateKnowledgeEntryRequest {
    title?: string;
    content?: string;
    content_type?: 'text' | 'markdown' | 'json';
    source_url?: string;
    metadata?: Record<string, any>;
}
export interface RAGResponse {
    search_results: SearchResult[];
    generated_response?: string;
    sources_used: string[];
    context_tokens_used: number;
}
export interface RAGPipelineConfig {
    max_context_tokens: number;
    similarity_threshold: number;
    max_search_results: number;
    embedding_model: string;
    generation_model: string;
}
