import { Logger, OnModuleInit } from '@nestjs/common';
import { AgentRuntimeService, AgentMessage, AgentResponse } from './agent-runtime.service';
import { ConversationDataAccessService } from '../../database/services/conversation-data-access.service';
import { AgentDataAccessService } from '../../database/services/agent-data-access.service';
import { RAGService } from '../../knowledge/rag.service';
import { AgentType } from '../../database/types/rag.types';
export interface DreamInterpretationRequest {
    userId: string;
    dreamContent: string;
    emotionalState?: string;
    additionalContext?: string;
    sessionId?: string;
}
export interface DreamInterpretationResponse {
    interpretation: string;
    keyInsights: string[];
    emotionalSupport?: string;
    actionableAdvice?: string;
    confidence: number;
    sources?: string[];
}
export interface AgentPersona {
    name: string;
    role: string;
    expertise: string[];
    communicationStyle: string;
    specialization: string;
    approach: string;
}
export declare abstract class BaseDonnyService implements OnModuleInit {
    protected readonly agentRuntimeService: AgentRuntimeService;
    protected readonly conversationDataService: ConversationDataAccessService;
    protected readonly agentDataService: AgentDataAccessService;
    protected readonly ragService: RAGService;
    protected readonly logger: Logger;
    protected agentId: string;
    protected agentType: AgentType;
    protected persona: AgentPersona;
    constructor(agentRuntimeService: AgentRuntimeService, conversationDataService: ConversationDataAccessService, agentDataService: AgentDataAccessService, ragService: RAGService);
    onModuleInit(): Promise<void>;
    private initializeAgent;
    interpretDream(request: DreamInterpretationRequest): Promise<DreamInterpretationResponse>;
    handleMessage(userId: string, content: string, context?: any): Promise<AgentResponse>;
    getAgentInfo(): AgentPersona;
    getCapabilities(): string[];
    protected abstract analyzeWithKnowledge(request: DreamInterpretationRequest, knowledgeResults: any[]): Promise<string>;
    protected abstract generatePersonalizedResponse(request: DreamInterpretationRequest, interpretation: string, knowledgeResults: any[]): Promise<DreamInterpretationResponse>;
    protected abstract handleGeneralConversation(message: AgentMessage, context?: any): Promise<AgentResponse>;
    protected isDreamInterpretationRequest(content: string): boolean;
    protected formatInterpretationResponse(interpretation: DreamInterpretationResponse): string;
    protected getPersonaPrompt(): any;
    protected getDefaultTools(): any[];
    protected saveInterpretationRecord(request: DreamInterpretationRequest, response: DreamInterpretationResponse): Promise<void>;
    protected extractDreamElements(content: string): {
        emotions: string[];
        symbols: string[];
        actions: string[];
        characters: string[];
    };
    private extractByKeywords;
}
