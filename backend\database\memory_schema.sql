-- 记忆系统数据库架构

-- 启用pgvector扩展（如果尚未启用）
CREATE EXTENSION IF NOT EXISTS vector;

-- Agent记忆表
CREATE TABLE IF NOT EXISTS agent_memories (
  id TEXT PRIMARY KEY,
  agent_type TEXT NOT NULL CHECK (agent_type IN ('almighty', 'taoist', 'freud', 'papa', 'shared')),
  user_id TEXT NOT NULL,
  session_id TEXT,
  memory_type TEXT NOT NULL CHECK (memory_type IN ('short_term', 'medium_term', 'long_term')),
  importance INTEGER NOT NULL CHECK (importance BETWEEN 1 AND 5),
  content JSONB NOT NULL,
  metadata JSONB NOT NULL DEFAULT '{}',
  embedding vector(1536), -- OpenAI ada-002 embedding dimension
  access_count INTEGER NOT NULL DEFAULT 0,
  last_accessed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  
  -- 创建索引
  CONSTRAINT fk_agent_memories_agent_type FOREIGN KEY (agent_type) REFERENCES agent_types(type) ON DELETE CASCADE
);

-- Agent类型参考表
CREATE TABLE IF NOT EXISTS agent_types (
  type TEXT PRIMARY KEY CHECK (type IN ('almighty', 'taoist', 'freud', 'papa', 'shared')),
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 记忆演进事件表
CREATE TABLE IF NOT EXISTS memory_evolution_events (
  id TEXT PRIMARY KEY,
  memory_id TEXT NOT NULL,
  event_type TEXT NOT NULL CHECK (event_type IN ('created', 'accessed', 'updated', 'consolidated', 'archived', 'forgotten')),
  old_value JSONB,
  new_value JSONB,
  reason TEXT NOT NULL,
  confidence DECIMAL(3,2) NOT NULL CHECK (confidence BETWEEN 0 AND 1),
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  agent_type TEXT NOT NULL,
  user_id TEXT NOT NULL,
  
  -- 外键约束
  CONSTRAINT fk_memory_events_agent_type FOREIGN KEY (agent_type) REFERENCES agent_types(type)
);

-- 记忆洞察表
CREATE TABLE IF NOT EXISTS memory_insights (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  agent_type TEXT NOT NULL,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  confidence DECIMAL(3,2) NOT NULL CHECK (confidence BETWEEN 0 AND 1),
  supporting_memory_ids TEXT[] NOT NULL DEFAULT '{}',
  relevance_score DECIMAL(3,2) NOT NULL CHECK (relevance_score BETWEEN 0 AND 1),
  generated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  
  -- 外键约束
  CONSTRAINT fk_insights_agent_type FOREIGN KEY (agent_type) REFERENCES agent_types(type)
);

-- 创建索引

-- agent_memories表索引
CREATE INDEX IF NOT EXISTS idx_agent_memories_agent_type ON agent_memories(agent_type);
CREATE INDEX IF NOT EXISTS idx_agent_memories_user_id ON agent_memories(user_id);
CREATE INDEX IF NOT EXISTS idx_agent_memories_session_id ON agent_memories(session_id);
CREATE INDEX IF NOT EXISTS idx_agent_memories_memory_type ON agent_memories(memory_type);
CREATE INDEX IF NOT EXISTS idx_agent_memories_importance ON agent_memories(importance);
CREATE INDEX IF NOT EXISTS idx_agent_memories_created_at ON agent_memories(created_at);
CREATE INDEX IF NOT EXISTS idx_agent_memories_expires_at ON agent_memories(expires_at);
CREATE INDEX IF NOT EXISTS idx_agent_memories_access_count ON agent_memories(access_count);

-- 复合索引
CREATE INDEX IF NOT EXISTS idx_agent_memories_agent_user ON agent_memories(agent_type, user_id);
CREATE INDEX IF NOT EXISTS idx_agent_memories_type_importance ON agent_memories(memory_type, importance);
CREATE INDEX IF NOT EXISTS idx_agent_memories_user_session ON agent_memories(user_id, session_id);

-- 向量相似度索引（使用cosine距离）
CREATE INDEX IF NOT EXISTS idx_agent_memories_embedding_cosine ON agent_memories 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- 向量相似度索引（使用欧几里得距离）
CREATE INDEX IF NOT EXISTS idx_agent_memories_embedding_l2 ON agent_memories 
USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);

-- GIN索引用于JSONB字段
CREATE INDEX IF NOT EXISTS idx_agent_memories_content_gin ON agent_memories USING gin(content);
CREATE INDEX IF NOT EXISTS idx_agent_memories_metadata_gin ON agent_memories USING gin(metadata);

-- memory_evolution_events表索引
CREATE INDEX IF NOT EXISTS idx_memory_events_memory_id ON memory_evolution_events(memory_id);
CREATE INDEX IF NOT EXISTS idx_memory_events_event_type ON memory_evolution_events(event_type);
CREATE INDEX IF NOT EXISTS idx_memory_events_timestamp ON memory_evolution_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_memory_events_agent_user ON memory_evolution_events(agent_type, user_id);

-- memory_insights表索引
CREATE INDEX IF NOT EXISTS idx_memory_insights_user_id ON memory_insights(user_id);
CREATE INDEX IF NOT EXISTS idx_memory_insights_agent_type ON memory_insights(agent_type);
CREATE INDEX IF NOT EXISTS idx_memory_insights_type ON memory_insights(type);
CREATE INDEX IF NOT EXISTS idx_memory_insights_generated_at ON memory_insights(generated_at);
CREATE INDEX IF NOT EXISTS idx_memory_insights_relevance_score ON memory_insights(relevance_score);

-- 创建触发器函数：自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为agent_memories表创建触发器
DROP TRIGGER IF EXISTS update_agent_memories_updated_at ON agent_memories;
CREATE TRIGGER update_agent_memories_updated_at
    BEFORE UPDATE ON agent_memories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 视图：活跃记忆概览
CREATE OR REPLACE VIEW active_memories_summary AS
SELECT 
    agent_type,
    user_id,
    memory_type,
    COUNT(*) as total_memories,
    AVG(importance) as avg_importance,
    AVG(access_count) as avg_access_count,
    MAX(created_at) as latest_memory,
    COUNT(CASE WHEN expires_at > NOW() OR expires_at IS NULL THEN 1 END) as active_memories
FROM agent_memories
WHERE (expires_at IS NULL OR expires_at > NOW())
GROUP BY agent_type, user_id, memory_type;

-- 视图：记忆演进统计
CREATE OR REPLACE VIEW memory_evolution_stats AS
SELECT 
    agent_type,
    user_id,
    event_type,
    COUNT(*) as event_count,
    AVG(confidence) as avg_confidence,
    MIN(timestamp) as first_event,
    MAX(timestamp) as last_event
FROM memory_evolution_events
GROUP BY agent_type, user_id, event_type;

-- 插入默认的agent类型
INSERT INTO agent_types (type, name, description) 
VALUES 
  ('almighty', 'Almighty Donny', '全能的Donny，具备所有权限和功能'),
  ('taoist', 'Taoist Donny', '道教解梦师Donny，专注于道教哲学和五行解梦'),
  ('freud', 'Freud Donny', '心理分析解梦师Donny，基于弗洛伊德精神分析理论'),
  ('papa', 'Papa Donny', '温暖的Papa Donny，提供情感支持和人生智慧'),
  ('shared', 'Shared Agent', '共享Agent，用于公共资源和跨Agent数据')
ON CONFLICT (type) DO NOTHING;

-- 创建存储过程：清理过期记忆
CREATE OR REPLACE FUNCTION cleanup_expired_memories()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除过期的短期记忆
    DELETE FROM agent_memories 
    WHERE memory_type = 'short_term' 
    AND expires_at IS NOT NULL 
    AND expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 记录清理事件
    INSERT INTO memory_evolution_events (
        id, memory_id, event_type, reason, confidence, 
        timestamp, agent_type, user_id
    )
    SELECT 
        'cleanup_' || EXTRACT(EPOCH FROM NOW())::TEXT || '_' || ROW_NUMBER() OVER(),
        'expired_batch',
        'forgotten',
        'Automatic cleanup of expired memories',
        1.0,
        NOW(),
        'almighty',
        'system'
    WHERE deleted_count > 0;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建存储过程：获取上下文记忆
CREATE OR REPLACE FUNCTION get_contextual_memories(
    p_agent_type TEXT,
    p_user_id TEXT,
    p_session_id TEXT,
    p_context_text TEXT,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    id TEXT,
    content JSONB,
    metadata JSONB,
    memory_type TEXT,
    importance INTEGER,
    similarity FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.content,
        m.metadata,
        m.memory_type,
        m.importance,
        CASE 
            WHEN m.embedding IS NOT NULL THEN 
                1 - (m.embedding <-> (
                    SELECT embedding FROM agent_memories 
                    WHERE content::text ILIKE '%' || p_context_text || '%' 
                    AND embedding IS NOT NULL 
                    LIMIT 1
                ))
            ELSE 0.5
        END as similarity
    FROM agent_memories m
    WHERE m.agent_type = p_agent_type
    AND m.user_id = p_user_id
    AND (m.session_id = p_session_id OR m.memory_type != 'short_term')
    AND (m.expires_at IS NULL OR m.expires_at > NOW())
    ORDER BY 
        CASE m.memory_type 
            WHEN 'short_term' THEN 3
            WHEN 'medium_term' THEN 2  
            WHEN 'long_term' THEN 1
        END DESC,
        m.importance DESC,
        similarity DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- 创建记忆统计函数
CREATE OR REPLACE FUNCTION get_memory_statistics(
    p_agent_type TEXT DEFAULT NULL,
    p_user_id TEXT DEFAULT NULL
)
RETURNS TABLE (
    total_memories BIGINT,
    short_term_count BIGINT,
    medium_term_count BIGINT,
    long_term_count BIGINT,
    avg_importance NUMERIC,
    total_access_count BIGINT,
    oldest_memory TIMESTAMPTZ,
    newest_memory TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_memories,
        COUNT(CASE WHEN memory_type = 'short_term' THEN 1 END) as short_term_count,
        COUNT(CASE WHEN memory_type = 'medium_term' THEN 1 END) as medium_term_count,
        COUNT(CASE WHEN memory_type = 'long_term' THEN 1 END) as long_term_count,
        AVG(importance) as avg_importance,
        SUM(access_count) as total_access_count,
        MIN(created_at) as oldest_memory,
        MAX(created_at) as newest_memory
    FROM agent_memories
    WHERE (p_agent_type IS NULL OR agent_type = p_agent_type)
    AND (p_user_id IS NULL OR user_id = p_user_id)
    AND (expires_at IS NULL OR expires_at > NOW());
END;
$$ LANGUAGE plpgsql;

-- 授予必要的权限
GRANT ALL PRIVILEGES ON TABLE agent_memories TO postgres;
GRANT ALL PRIVILEGES ON TABLE agent_types TO postgres;
GRANT ALL PRIVILEGES ON TABLE memory_evolution_events TO postgres;
GRANT ALL PRIVILEGES ON TABLE memory_insights TO postgres;

-- 创建RLS（行级安全）策略
ALTER TABLE agent_memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_evolution_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_insights ENABLE ROW LEVEL SECURITY;

-- 为不同agent类型创建策略
CREATE POLICY agent_memory_policy ON agent_memories
    FOR ALL
    TO authenticated
    USING (
        -- Almighty agent可以访问所有记忆
        current_setting('app.current_agent_type', true) = 'almighty'
        OR 
        -- 其他agent只能访问自己的记忆
        agent_type = current_setting('app.current_agent_type', true)
    );

CREATE POLICY memory_events_policy ON memory_evolution_events
    FOR ALL
    TO authenticated
    USING (
        current_setting('app.current_agent_type', true) = 'almighty'
        OR 
        agent_type = current_setting('app.current_agent_type', true)
    );

CREATE POLICY memory_insights_policy ON memory_insights
    FOR ALL
    TO authenticated
    USING (
        current_setting('app.current_agent_type', true) = 'almighty'
        OR 
        agent_type = current_setting('app.current_agent_type', true)
    ); 