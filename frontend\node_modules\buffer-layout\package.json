{"name": "buffer-layout", "version": "1.2.2", "description": "Translation between JavaScript values and Buffers", "keywords": ["<PERSON><PERSON><PERSON>", "struct", "endian", "pack data"], "homepage": "https://github.com/pabigot/buffer-layout", "bugs": "https://github.com/pabigot/buffer-layout/issues", "repository": {"type": "git", "url": "https://github.com/pabigot/buffer-layout.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "main": "./lib/Layout.js", "devDependencies": {"coveralls": "^3.0.0", "eslint": "~4.18.2", "eslint-config-google": "~0.9.1", "eslint-plugin-pabigot": "~1.1.0", "istanbul": "~0.4.5", "jsdoc": "~3.5.5", "lodash": "~4.17.5", "mocha": "~5.0.4"}, "engines": {"node": ">=4.5"}, "scripts": {"coverage": "istanbul cover _mocha -- -u tdd", "coveralls": "istanbul cover _mocha --report lcovonly -- -u tdd && cat ./coverage/lcov.info | coveralls", "eslint": "eslint lib/ test/", "jsdoc": "jsdoc -c jsdoc/conf.json", "test": "mocha -u tdd"}}