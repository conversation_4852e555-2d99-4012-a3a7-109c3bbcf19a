// API配置
export const API_BASE_URL = 'http://localhost:3001/api';

// 请求头设置
export const getHeaders = (token?: string) => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
};

// 处理API响应
export const handleResponse = async (response: Response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `API error: ${response.status}`);
  }
  
  return response.json();
};

// 存储和获取令牌
export const getToken = (): string | null => {
  return localStorage.getItem('donny_token');
};

export const setToken = (token: string): void => {
  localStorage.setItem('donny_token', token);
};

export const removeToken = (): void => {
  localStorage.removeItem('donny_token');
};
