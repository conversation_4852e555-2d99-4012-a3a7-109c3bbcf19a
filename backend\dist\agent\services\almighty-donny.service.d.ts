import { OnModuleInit } from '@nestjs/common';
import { AgentRuntimeService, AgentResponse } from './agent-runtime.service';
import { UserDataAccessService } from '../../database/services/user-data-access.service';
import { ConversationDataAccessService } from '../../database/services/conversation-data-access.service';
import { AgentDataAccessService } from '../../database/services/agent-data-access.service';
export interface DreamRequest {
    userId: string;
    dreamContent: string;
    selectedDonny?: string;
    additionalInfo?: string;
}
export interface DonnyRecommendation {
    donnyType: string;
    confidence: number;
    reasoning: string;
}
export interface UserFlowState {
    userId: string;
    currentStep: 'greeting' | 'dream_input' | 'donny_selection' | 'payment' | 'interpretation' | 'feedback';
    dreamContent?: string;
    selectedDonny?: string;
    sessionId?: string;
    paymentStatus?: 'pending' | 'completed' | 'failed';
    lastActivity: Date;
}
export declare class AlmightyDonnyService implements OnModuleInit {
    private readonly agentRuntimeService;
    private readonly userDataService;
    private readonly conversationDataService;
    private readonly agentDataService;
    private readonly logger;
    private agentId;
    private userFlows;
    constructor(agentRuntimeService: AgentRuntimeService, userDataService: UserDataAccessService, conversationDataService: ConversationDataAccessService, agentDataService: AgentDataAccessService);
    onModuleInit(): Promise<void>;
    private initializeAlmightyDonny;
    handleUserMessage(userId: string, content: string, messageId?: string): Promise<AgentResponse>;
    private processUserFlow;
    private handleGreeting;
    private handleDreamInput;
    private handleDonnySelection;
    private handlePayment;
    private handleInterpretation;
    private handleFeedback;
    private handleGeneral;
    private analyzeDreamAndRecommend;
    private isDreamInput;
    private isDonnySelection;
    private parseDonnySelection;
    private getDonnyDisplayName;
    private saveConversation;
    private getDefaultPersona;
    private getDefaultTools;
    getUserFlowState(userId: string): UserFlowState | null;
    cleanupExpiredFlows(): void;
}
