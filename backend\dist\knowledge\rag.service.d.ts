import { EmbeddingService } from './embedding.service';
import { KnowledgeDataAccessService } from '../database/services/knowledge-data-access.service';
import { AgentType, SearchKnowledgeRequest, SearchResult, RAGResponse, RAGPipelineConfig, CreateKnowledgeEntryRequest, KnowledgeEntry } from '../database/types/rag.types';
export declare class RAGService {
    private embeddingService;
    private knowledgeDataAccess;
    private readonly logger;
    private readonly defaultConfig;
    constructor(embeddingService: EmbeddingService, knowledgeDataAccess: KnowledgeDataAccessService);
    performRAG(query: string, agentType: AgentType, userId?: string, config?: Partial<RAGPipelineConfig>): Promise<RAGResponse>;
    searchKnowledge(request: SearchKnowledgeRequest, userId?: string): Promise<SearchResult[]>;
    addKnowledgeEntry(request: CreateKnowledgeEntryRequest, userId?: string): Promise<KnowledgeEntry>;
    addKnowledgeEntriesBatch(entries: CreateKnowledgeEntryRequest[], userId?: string): Promise<KnowledgeEntry[]>;
    reindexKnowledgeBase(knowledgeBaseId: string): Promise<void>;
    private prepareTextForEmbedding;
    private buildContext;
    private formatContextSnippet;
    private generateEnhancedResponse;
    private applyMetadataFilter;
    private logSearchUsage;
    private estimateTokens;
    private delay;
}
