"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var KnowledgeDataAccessService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeDataAccessService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("../database.service");
let KnowledgeDataAccessService = KnowledgeDataAccessService_1 = class KnowledgeDataAccessService {
    constructor(databaseService) {
        this.databaseService = databaseService;
        this.logger = new common_1.Logger(KnowledgeDataAccessService_1.name);
    }
    async createKnowledgeBase(request, userId) {
        try {
            const { data, error } = await this.databaseService.getClient()
                .from('knowledge_bases')
                .insert({
                name: request.name,
                description: request.description,
                agent_type: request.agent_type,
                is_public: request.is_public || false,
                created_by: userId,
            })
                .select()
                .single();
            if (error) {
                throw new Error(`创建知识库失败: ${error.message}`);
            }
            this.logger.log(`知识库创建成功: ${data.name} (${data.id})`);
            return data;
        }
        catch (error) {
            this.logger.error(`创建知识库失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getKnowledgeBases(agentType, userId, includePublic = true) {
        try {
            let query = this.databaseService.getClient()
                .from('knowledge_bases')
                .select('*');
            const conditions = [];
            if (agentType) {
                conditions.push(`agent_type.eq.${agentType}`);
            }
            if (userId) {
                if (includePublic) {
                    conditions.push(`or(created_by.eq.${userId},is_public.eq.true)`);
                }
                else {
                    conditions.push(`created_by.eq.${userId}`);
                }
            }
            else if (includePublic) {
                conditions.push('is_public.eq.true');
            }
            if (conditions.length > 0) {
                query = query.or(conditions.join(','));
            }
            const { data, error } = await query.order('created_at', { ascending: false });
            if (error) {
                throw new Error(`获取知识库列表失败: ${error.message}`);
            }
            this.logger.log(`获取到 ${data?.length || 0} 个知识库`);
            return data || [];
        }
        catch (error) {
            this.logger.error(`获取知识库列表失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getKnowledgeBaseById(id) {
        try {
            const { data, error } = await this.databaseService.getClient()
                .from('knowledge_bases')
                .select('*')
                .eq('id', id)
                .single();
            if (error) {
                throw new common_1.NotFoundException(`知识库不存在: ${id}`);
            }
            return data;
        }
        catch (error) {
            this.logger.error(`获取知识库失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async deleteKnowledgeBase(id, userId) {
        try {
            let query = this.databaseService.getClient()
                .from('knowledge_bases')
                .delete()
                .eq('id', id);
            if (userId) {
                query = query.eq('created_by', userId);
            }
            const { error } = await query;
            if (error) {
                throw new Error(`删除知识库失败: ${error.message}`);
            }
            this.logger.log(`知识库删除成功: ${id}`);
        }
        catch (error) {
            this.logger.error(`删除知识库失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async createKnowledgeEntry(request, embedding) {
        try {
            await this.getKnowledgeBaseById(request.knowledge_base_id);
            const { data, error } = await this.databaseService.getClient()
                .from('knowledge_entries')
                .insert({
                knowledge_base_id: request.knowledge_base_id,
                title: request.title,
                content: request.content,
                content_type: request.content_type || 'text',
                source_url: request.source_url,
                metadata: request.metadata || {},
                embedding: embedding ? `[${embedding.join(',')}]` : null,
            })
                .select()
                .single();
            if (error) {
                throw new Error(`创建知识条目失败: ${error.message}`);
            }
            this.logger.log(`知识条目创建成功: ${data.title || data.id}`);
            return data;
        }
        catch (error) {
            this.logger.error(`创建知识条目失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async updateKnowledgeEntry(id, request, embedding) {
        try {
            const updateData = {
                ...request,
                updated_at: new Date().toISOString(),
            };
            if (embedding) {
                updateData.embedding = `[${embedding.join(',')}]`;
            }
            const { data, error } = await this.databaseService.getClient()
                .from('knowledge_entries')
                .update(updateData)
                .eq('id', id)
                .select()
                .single();
            if (error) {
                throw new Error(`更新知识条目失败: ${error.message}`);
            }
            if (!data) {
                throw new common_1.NotFoundException(`知识条目不存在: ${id}`);
            }
            this.logger.log(`知识条目更新成功: ${data.title || data.id}`);
            return data;
        }
        catch (error) {
            this.logger.error(`更新知识条目失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getKnowledgeEntries(knowledgeBaseId, limit = 50, offset = 0) {
        try {
            const { data, error } = await this.databaseService.getClient()
                .from('knowledge_entries')
                .select('*')
                .eq('knowledge_base_id', knowledgeBaseId)
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);
            if (error) {
                throw new Error(`获取知识条目失败: ${error.message}`);
            }
            return data || [];
        }
        catch (error) {
            this.logger.error(`获取知识条目失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async deleteKnowledgeEntry(id) {
        try {
            const { error } = await this.databaseService.getClient()
                .from('knowledge_entries')
                .delete()
                .eq('id', id);
            if (error) {
                throw new Error(`删除知识条目失败: ${error.message}`);
            }
            this.logger.log(`知识条目删除成功: ${id}`);
        }
        catch (error) {
            this.logger.error(`删除知识条目失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async searchKnowledgeEntries(queryEmbedding, agentType, matchThreshold = 0.7, matchCount = 10) {
        try {
            const { data, error } = await this.databaseService.getClient()
                .rpc('search_knowledge_entries', {
                query_embedding: `[${queryEmbedding.join(',')}]`,
                match_threshold: matchThreshold,
                match_count: matchCount,
                filter_agent_type: agentType,
            });
            if (error) {
                throw new Error(`向量搜索失败: ${error.message}`);
            }
            this.logger.log(`向量搜索返回 ${data?.length || 0} 个结果`);
            return data || [];
        }
        catch (error) {
            this.logger.error(`向量搜索失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async logKnowledgeUsage(agentType, knowledgeEntryId, queryText, similarityScore, usageContext, userId) {
        try {
            const { error } = await this.databaseService.getClient()
                .from('knowledge_usage_logs')
                .insert({
                agent_type: agentType,
                user_id: userId,
                knowledge_entry_id: knowledgeEntryId,
                query_text: queryText,
                similarity_score: similarityScore,
                usage_context: usageContext,
            });
            if (error) {
                this.logger.warn(`记录使用日志失败: ${error.message}`);
            }
            else {
                this.logger.debug(`使用日志记录成功: ${agentType} -> ${knowledgeEntryId}`);
            }
        }
        catch (error) {
            this.logger.warn(`记录使用日志异常: ${error.message}`);
        }
    }
    async getUsageStats(agentType, userId, days = 30) {
        try {
            let query = this.databaseService.getClient()
                .from('knowledge_usage_logs')
                .select('agent_type, usage_context, created_at')
                .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString());
            if (agentType) {
                query = query.eq('agent_type', agentType);
            }
            if (userId) {
                query = query.eq('user_id', userId);
            }
            const { data, error } = await query;
            if (error) {
                throw new Error(`获取使用统计失败: ${error.message}`);
            }
            return this.aggregateUsageStats(data || []);
        }
        catch (error) {
            this.logger.error(`获取使用统计失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    aggregateUsageStats(logs) {
        const stats = {
            total_queries: logs.length,
            by_agent: {},
            by_context: {},
            by_date: {},
        };
        logs.forEach(log => {
            if (!stats.by_agent[log.agent_type]) {
                stats.by_agent[log.agent_type] = 0;
            }
            stats.by_agent[log.agent_type]++;
            if (!stats.by_context[log.usage_context]) {
                stats.by_context[log.usage_context] = 0;
            }
            stats.by_context[log.usage_context]++;
            const date = new Date(log.created_at).toISOString().split('T')[0];
            if (!stats.by_date[date]) {
                stats.by_date[date] = 0;
            }
            stats.by_date[date]++;
        });
        return stats;
    }
};
exports.KnowledgeDataAccessService = KnowledgeDataAccessService;
exports.KnowledgeDataAccessService = KnowledgeDataAccessService = KnowledgeDataAccessService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], KnowledgeDataAccessService);
//# sourceMappingURL=knowledge-data-access.service.js.map