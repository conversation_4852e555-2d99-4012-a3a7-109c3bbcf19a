import { AgentFactoryService, AgentInfo } from './services/agent-factory.service';
import { DreamInterpretationResponse } from './services/base-donny.service';
import { AgentType } from '../database/types/rag.types';
export interface DreamInterpretationApiRequest {
    userId: string;
    dreamContent: string;
    agentType?: AgentType;
    emotionalState?: string;
    additionalContext?: string;
    sessionId?: string;
}
export interface AgentRecommendationRequest {
    content: string;
    userId?: string;
    context?: any;
}
export declare class BusinessAgentController {
    private readonly agentFactory;
    private readonly logger;
    constructor(agentFactory: AgentFactoryService);
    getSupportedAgentTypes(): Promise<AgentInfo[]>;
    recommendAgent(request: AgentRecommendationRequest): Promise<{
        recommended: AgentType;
        confidence: number;
        reasoning: string;
        alternatives: Array<{
            type: AgentType;
            confidence: number;
            reasoning: string;
        }>;
    }>;
    interpretDream(request: DreamInterpretationApiRequest): Promise<DreamInterpretationResponse>;
    chatWithAgent(agentType: AgentType, request: {
        userId: string;
        content: string;
        sessionId?: string;
        context?: any;
    }): Promise<import("./services/agent-runtime.service").AgentResponse>;
    getAgentInfo(agentType: AgentType): Promise<{
        agentType: AgentType;
        persona: import("./services/base-donny.service").AgentPersona;
        capabilities: string[];
        status: string;
    }>;
    getActiveAgentStats(): Promise<{
        totalActive: number;
        byType: Record<string, number>;
        oldestActivity: Date | null;
        newestActivity: Date | null;
    }>;
    cleanupInactiveAgents(maxIdleMinutes?: string): Promise<{
        message: string;
        cleanedCount: number;
    }>;
    reloadAgentConfiguration(agentType: AgentType): Promise<{
        message: string;
    }>;
    batchInterpretDreams(request: {
        dreams: Array<{
            userId: string;
            dreamContent: string;
            agentType?: AgentType;
            sessionId?: string;
        }>;
    }): Promise<{
        total: number;
        successful: number;
        failed: number;
        results: any[];
        errors: any[];
    }>;
}
