import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Sphere } from '@react-three/drei';
import styled from 'styled-components';
import { media } from '../../styles/theme';
import { DonnyType } from '../../types/donny';

// 3D模型容器
const ModelContainer = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  z-index: 10;

  ${media.md} {
    width: 150px;
    height: 150px;
  }

  ${media.sm} {
    width: 120px;
    height: 120px;
  }
`;

// 简单的旋转球体作为占位符
const RotatingSphere = () => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
    }
  });

  return (
    <Sphere ref={meshRef} args={[1, 32, 32]}>
      <meshStandardMaterial color="#B6EBED" />
    </Sphere>
  );
};

interface DonnyModelProps {
  type?: DonnyType;
}

export const DonnyModel: React.FC<DonnyModelProps> = ({ type = 'almighty' }) => {
  // 根据类型选择不同的颜色
  const getColor = () => {
    switch (type) {
      case 'taoist':
        return '#8AEDB5';
      case 'freud':
        return '#ED8A8A';
      case 'papa':
        return '#EDD58A';
      case 'accountant':
        return '#8A9DED';
      case 'recorder':
        return '#C78AED';
      default:
        return '#B6EBED';
    }
  };

  return (
    <ModelContainer>
      <Canvas camera={{ position: [0, 0, 3] }}>
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} />
        <RotatingSphere />
        <OrbitControls enableZoom={false} />
      </Canvas>
    </ModelContainer>
  );
};
