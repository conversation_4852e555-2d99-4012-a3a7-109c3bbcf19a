"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MemoryController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const memory_types_1 = require("./memory.types");
const memory_manager_service_1 = require("./services/memory-manager.service");
let MemoryController = MemoryController_1 = class MemoryController {
    constructor(memoryManager) {
        this.memoryManager = memoryManager;
        this.logger = new common_1.Logger(MemoryController_1.name);
    }
    async storeMemory(memoryData) {
        this.logger.log('存储记忆请求');
        try {
            const storedMemory = await this.memoryManager.storeMemory(memoryData);
            return storedMemory;
        }
        catch (error) {
            this.logger.error(`存储记忆失败: ${error.message}`);
            throw error;
        }
    }
    async retrieveMemories(agentType, userId, sessionId, memoryType, category, importance, searchText, limit, offset) {
        this.logger.log('检索记忆请求');
        const query = {
            agentType,
            userId,
            sessionId,
            memoryType,
            category,
            importance,
            searchText,
            limit,
            offset,
        };
        try {
            const memories = await this.memoryManager.retrieveMemory(query);
            return memories;
        }
        catch (error) {
            this.logger.error(`检索记忆失败: ${error.message}`);
            throw error;
        }
    }
    async getContextualMemories(agentType, userId, context, limit = 10) {
        this.logger.log(`获取上下文记忆 - Agent: ${agentType}, 用户: ${userId}, 上下文: ${context}`);
        try {
            const memories = await this.memoryManager.getContextualMemories(agentType, userId, context, limit);
            return memories;
        }
        catch (error) {
            this.logger.error(`获取上下文记忆失败: ${error.message}`);
            throw error;
        }
    }
    async updateMemory(memoryId, updates) {
        this.logger.log(`更新记忆请求 - ID: ${memoryId}`);
        try {
            const updatedMemory = await this.memoryManager.updateMemory(memoryId, updates);
            return updatedMemory;
        }
        catch (error) {
            this.logger.error(`更新记忆失败: ${error.message}`);
            throw error;
        }
    }
    async deleteMemory(memoryId) {
        this.logger.log(`删除记忆请求 - ID: ${memoryId}`);
        try {
            const success = await this.memoryManager.deleteMemory(memoryId);
            return { success };
        }
        catch (error) {
            this.logger.error(`删除记忆失败: ${error.message}`);
            throw error;
        }
    }
    async getMemoryStats(agentType, userId) {
        this.logger.log(`获取记忆统计 - Agent: ${agentType}, 用户: ${userId}`);
        try {
            const stats = await this.memoryManager.getMemoryStats(agentType, userId);
            return stats;
        }
        catch (error) {
            this.logger.error(`获取记忆统计失败: ${error.message}`);
            throw error;
        }
    }
    async generateInsights(agentType, userId) {
        this.logger.log(`生成记忆洞察 - Agent: ${agentType}, 用户: ${userId}`);
        try {
            const insights = await this.memoryManager.generateUserInsights(agentType, userId);
            return insights;
        }
        catch (error) {
            this.logger.error(`生成记忆洞察失败: ${error.message}`);
            throw error;
        }
    }
    async triggerConsolidation() {
        this.logger.log('手动触发记忆巩固任务');
        try {
            const result = await this.memoryManager.triggerConsolidation();
            return result;
        }
        catch (error) {
            this.logger.error(`触发记忆巩固失败: ${error.message}`);
            throw error;
        }
    }
    async triggerCleanup() {
        this.logger.log('手动触发记忆清理任务');
        try {
            const result = await this.memoryManager.cleanupExpiredMemories();
            return result;
        }
        catch (error) {
            this.logger.error(`触发记忆清理失败: ${error.message}`);
            throw error;
        }
    }
    async semanticSearch(query, agentType, userId, threshold = 0.8, limit = 10) {
        this.logger.log(`语义搜索记忆 - 查询: ${query}`);
        const searchQuery = {
            agentType,
            userId,
            searchText: query,
            similarityThreshold: threshold,
            limit,
        };
        try {
            const memories = await this.memoryManager.retrieveMemory(searchQuery);
            return memories;
        }
        catch (error) {
            this.logger.error(`语义搜索失败: ${error.message}`);
            throw error;
        }
    }
    async healthCheck() {
        this.logger.log('记忆服务健康检查');
        try {
            const result = await this.memoryManager.healthCheck();
            return result;
        }
        catch (error) {
            this.logger.error(`健康检查失败: ${error.message}`);
            return {
                status: 'unhealthy',
                services: {
                    memoryManager: false,
                },
            };
        }
    }
};
exports.MemoryController = MemoryController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '存储记忆' }),
    (0, swagger_1.ApiBody)({ type: Object, description: '记忆数据' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: '记忆存储成功',
        type: Object
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "storeMemory", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '检索记忆' }),
    (0, swagger_1.ApiQuery)({ name: 'agentType', required: false, type: String, enum: ['almighty', 'taoist', 'freud', 'papa', 'shared'] }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'sessionId', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'memoryType', required: false, enum: memory_types_1.MemoryType }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'importance', required: false, enum: memory_types_1.MemoryImportance }),
    (0, swagger_1.ApiQuery)({ name: 'searchText', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'offset', required: false, type: Number }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '记忆检索成功',
        type: [Object]
    }),
    __param(0, (0, common_1.Query)('agentType')),
    __param(1, (0, common_1.Query)('userId')),
    __param(2, (0, common_1.Query)('sessionId')),
    __param(3, (0, common_1.Query)('memoryType')),
    __param(4, (0, common_1.Query)('category')),
    __param(5, (0, common_1.Query)('importance')),
    __param(6, (0, common_1.Query)('searchText')),
    __param(7, (0, common_1.Query)('limit')),
    __param(8, (0, common_1.Query)('offset')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, Number, String, Number, Number]),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "retrieveMemories", null);
__decorate([
    (0, common_1.Get)('contextual/:agentType/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '获取上下文相关记忆' }),
    (0, swagger_1.ApiParam)({ name: 'agentType', type: String, enum: ['almighty', 'taoist', 'freud', 'papa', 'shared'] }),
    (0, swagger_1.ApiParam)({ name: 'userId', type: String }),
    (0, swagger_1.ApiQuery)({ name: 'context', required: true, type: String, description: '上下文内容' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: '返回数量限制' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '上下文记忆获取成功',
        type: [Object]
    }),
    __param(0, (0, common_1.Param)('agentType')),
    __param(1, (0, common_1.Param)('userId')),
    __param(2, (0, common_1.Query)('context')),
    __param(3, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Number]),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "getContextualMemories", null);
__decorate([
    (0, common_1.Put)(':memoryId'),
    (0, swagger_1.ApiOperation)({ summary: '更新记忆' }),
    (0, swagger_1.ApiParam)({ name: 'memoryId', type: String }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '记忆更新成功',
        type: Object
    }),
    __param(0, (0, common_1.Param)('memoryId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "updateMemory", null);
__decorate([
    (0, common_1.Delete)(':memoryId'),
    (0, swagger_1.ApiOperation)({ summary: '删除记忆' }),
    (0, swagger_1.ApiParam)({ name: 'memoryId', type: String }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '记忆删除成功',
        schema: { type: 'boolean' }
    }),
    __param(0, (0, common_1.Param)('memoryId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "deleteMemory", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取记忆统计信息' }),
    (0, swagger_1.ApiQuery)({ name: 'agentType', required: false, type: String, enum: ['almighty', 'taoist', 'freud', 'papa', 'shared'] }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, type: String }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '记忆统计获取成功',
        type: Object
    }),
    __param(0, (0, common_1.Query)('agentType')),
    __param(1, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "getMemoryStats", null);
__decorate([
    (0, common_1.Get)('insights/:agentType/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '生成记忆洞察' }),
    (0, swagger_1.ApiParam)({ name: 'agentType', type: String, enum: ['almighty', 'taoist', 'freud', 'papa', 'shared'] }),
    (0, swagger_1.ApiParam)({ name: 'userId', type: String }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '记忆洞察生成成功',
        type: [Object]
    }),
    __param(0, (0, common_1.Param)('agentType')),
    __param(1, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "generateInsights", null);
__decorate([
    (0, common_1.Post)('consolidation/trigger'),
    (0, swagger_1.ApiOperation)({ summary: '手动触发记忆巩固' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '记忆巩固任务已触发',
        schema: {
            type: 'object',
            properties: {
                shortToMedium: { type: 'number' },
                mediumToLong: { type: 'number' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "triggerConsolidation", null);
__decorate([
    (0, common_1.Post)('cleanup/trigger'),
    (0, swagger_1.ApiOperation)({ summary: '手动触发记忆清理' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '记忆清理任务已触发',
        schema: {
            type: 'object',
            properties: {
                shortTerm: { type: 'number' },
                mediumTerm: { type: 'number' },
                longTerm: { type: 'number' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "triggerCleanup", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: '语义搜索记忆' }),
    (0, swagger_1.ApiQuery)({ name: 'query', required: true, type: String, description: '搜索查询' }),
    (0, swagger_1.ApiQuery)({ name: 'agentType', required: false, type: String, enum: ['almighty', 'taoist', 'freud', 'papa', 'shared'] }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'threshold', required: false, type: Number, description: '相似度阈值' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: '返回数量限制' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '语义搜索成功',
        type: [Object]
    }),
    __param(0, (0, common_1.Query)('query')),
    __param(1, (0, common_1.Query)('agentType')),
    __param(2, (0, common_1.Query)('userId')),
    __param(3, (0, common_1.Query)('threshold')),
    __param(4, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Number, Number]),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "semanticSearch", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: '记忆服务健康检查' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '健康检查成功',
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string' },
                services: { type: 'object' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MemoryController.prototype, "healthCheck", null);
exports.MemoryController = MemoryController = MemoryController_1 = __decorate([
    (0, swagger_1.ApiTags)('记忆管理'),
    (0, common_1.Controller)('memory'),
    __metadata("design:paramtypes", [memory_manager_service_1.MemoryManagerService])
], MemoryController);
//# sourceMappingURL=memory.controller.js.map