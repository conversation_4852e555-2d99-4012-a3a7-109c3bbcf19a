import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@smithy/protocol-http";
import { EventStreamSerdeContext as __EventStreamSerdeContext, SerdeContext as __SerdeContext } from "@smithy/types";
import { GetMedicalScribeStreamCommandInput, GetMedicalScribeStreamCommandOutput } from "../commands/GetMedicalScribeStreamCommand";
import { StartCallAnalyticsStreamTranscriptionCommandInput, StartCallAnalyticsStreamTranscriptionCommandOutput } from "../commands/StartCallAnalyticsStreamTranscriptionCommand";
import { StartMedicalScribeStreamCommandInput, StartMedicalScribeStreamCommandOutput } from "../commands/StartMedicalScribeStreamCommand";
import { StartMedicalStreamTranscriptionCommandInput, StartMedicalStreamTranscriptionCommandOutput } from "../commands/StartMedicalStreamTranscriptionCommand";
import { StartStreamTranscriptionCommandInput, StartStreamTranscriptionCommandOutput } from "../commands/StartStreamTranscriptionCommand";
/**
 * serializeAws_restJson1GetMedicalScribeStreamCommand
 */
export declare const se_GetMedicalScribeStreamCommand: (input: GetMedicalScribeStreamCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1StartCallAnalyticsStreamTranscriptionCommand
 */
export declare const se_StartCallAnalyticsStreamTranscriptionCommand: (input: StartCallAnalyticsStreamTranscriptionCommandInput, context: __SerdeContext & __EventStreamSerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1StartMedicalScribeStreamCommand
 */
export declare const se_StartMedicalScribeStreamCommand: (input: StartMedicalScribeStreamCommandInput, context: __SerdeContext & __EventStreamSerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1StartMedicalStreamTranscriptionCommand
 */
export declare const se_StartMedicalStreamTranscriptionCommand: (input: StartMedicalStreamTranscriptionCommandInput, context: __SerdeContext & __EventStreamSerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1StartStreamTranscriptionCommand
 */
export declare const se_StartStreamTranscriptionCommand: (input: StartStreamTranscriptionCommandInput, context: __SerdeContext & __EventStreamSerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_restJson1GetMedicalScribeStreamCommand
 */
export declare const de_GetMedicalScribeStreamCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetMedicalScribeStreamCommandOutput>;
/**
 * deserializeAws_restJson1StartCallAnalyticsStreamTranscriptionCommand
 */
export declare const de_StartCallAnalyticsStreamTranscriptionCommand: (output: __HttpResponse, context: __SerdeContext & __EventStreamSerdeContext) => Promise<StartCallAnalyticsStreamTranscriptionCommandOutput>;
/**
 * deserializeAws_restJson1StartMedicalScribeStreamCommand
 */
export declare const de_StartMedicalScribeStreamCommand: (output: __HttpResponse, context: __SerdeContext & __EventStreamSerdeContext) => Promise<StartMedicalScribeStreamCommandOutput>;
/**
 * deserializeAws_restJson1StartMedicalStreamTranscriptionCommand
 */
export declare const de_StartMedicalStreamTranscriptionCommand: (output: __HttpResponse, context: __SerdeContext & __EventStreamSerdeContext) => Promise<StartMedicalStreamTranscriptionCommandOutput>;
/**
 * deserializeAws_restJson1StartStreamTranscriptionCommand
 */
export declare const de_StartStreamTranscriptionCommand: (output: __HttpResponse, context: __SerdeContext & __EventStreamSerdeContext) => Promise<StartStreamTranscriptionCommandOutput>;
