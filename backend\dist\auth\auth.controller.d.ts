import { AuthService } from './auth.service';
import { GetChallengeDto, VerifySignatureDto, AuthResponseDto } from './dto/auth.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    getChallenge(getChallengeDto: GetChallengeDto): Promise<{
        message: string;
    }>;
    verifySignature(verifySignatureDto: VerifySignatureDto): Promise<AuthResponseDto>;
}
