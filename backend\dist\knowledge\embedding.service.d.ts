import { ConfigService } from '@nestjs/config';
import { EmbeddingRequest, EmbeddingResponse } from '../database/types/rag.types';
export declare class EmbeddingService {
    private configService;
    private readonly logger;
    private readonly openaiApiKey;
    private readonly defaultModel;
    constructor(configService: ConfigService);
    generateEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse>;
    generateBatchEmbeddings(texts: string[], model?: string): Promise<EmbeddingResponse[]>;
    cosineSimilarity(vectorA: number[], vectorB: number[]): number;
    private preprocessText;
    private callOpenAIEmbedding;
    private delay;
    validateEmbeddingDimension(embedding: number[], expectedDim?: number): boolean;
    getSupportedModels(): string[];
}
