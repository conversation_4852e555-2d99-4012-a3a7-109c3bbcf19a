import { RAGService } from './rag.service';
import { KnowledgeDataAccessService } from '../database/services/knowledge-data-access.service';
import { CreateKnowledgeBaseRequest, CreateKnowledgeEntryRequest, UpdateKnowledgeEntryRequest, SearchKnowledgeRequest, AgentType } from '../database/types/rag.types';
export declare class KnowledgeController {
    private ragService;
    private knowledgeDataAccess;
    constructor(ragService: RAGService, knowledgeDataAccess: KnowledgeDataAccessService);
    createKnowledgeBase(request: CreateKnowledgeBaseRequest, req?: any): Promise<{
        success: boolean;
        data: import("../database/types/rag.types").KnowledgeBase;
        message: string;
    }>;
    getKnowledgeBases(agentType?: AgentType, includePublic?: string, req?: any): Promise<{
        success: boolean;
        data: import("../database/types/rag.types").KnowledgeBase[];
        count: number;
    }>;
    getKnowledgeBase(id: string): Promise<{
        success: boolean;
        data: import("../database/types/rag.types").KnowledgeBase;
    }>;
    deleteKnowledgeBase(id: string, req?: any): Promise<{
        success: boolean;
        message: string;
    }>;
    createKnowledgeEntry(request: CreateKnowledgeEntryRequest, req?: any): Promise<{
        success: boolean;
        data: import("../database/types/rag.types").KnowledgeEntry;
        message: string;
    }>;
    createKnowledgeEntriesBatch(entries: CreateKnowledgeEntryRequest[], req?: any): Promise<{
        success: boolean;
        data: import("../database/types/rag.types").KnowledgeEntry[];
        message: string;
        success_count: number;
        total_count: number;
    }>;
    getKnowledgeEntries(knowledgeBaseId: string, limit?: string, offset?: string): Promise<{
        success: boolean;
        data: import("../database/types/rag.types").KnowledgeEntry[];
        count: number;
        limit: number;
        offset: number;
    }>;
    updateKnowledgeEntry(id: string, request: UpdateKnowledgeEntryRequest): Promise<{
        success: boolean;
        data: import("../database/types/rag.types").KnowledgeEntry;
        message: string;
    }>;
    deleteKnowledgeEntry(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
    searchKnowledge(request: SearchKnowledgeRequest, req?: any): Promise<{
        success: boolean;
        data: import("../database/types/rag.types").SearchResult[];
        count: number;
        query: string;
    }>;
    performRAG(body: {
        query: string;
        agent_type: AgentType;
        config?: any;
    }, req?: any): Promise<{
        success: boolean;
        data: import("../database/types/rag.types").RAGResponse;
        query: string;
        agent_type: AgentType;
    }>;
    reindexKnowledgeBase(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
    getUsageStats(agentType?: AgentType, days?: string, req?: any): Promise<{
        success: boolean;
        data: any;
        agent_type: AgentType;
        days: number;
    }>;
}
