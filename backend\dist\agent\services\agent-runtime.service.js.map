{"version": 3, "file": "agent-runtime.service.js", "sourceRoot": "", "sources": ["../../../src/agent/services/agent-runtime.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAmF;AACnF,2CAA+C;AAC/C,sEAAkE;AAsC3D,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAK9B,YACmB,aAA4B,EAC5B,eAAgC;QADhC,kBAAa,GAAb,aAAa,CAAe;QAC5B,oBAAe,GAAf,eAAe,CAAiB;QANlC,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;QACvD,aAAQ,GAAqB,IAAI,GAAG,EAAE,CAAC;QACvC,kBAAa,GAAG,KAAK,CAAC;IAK3B,CAAC;IAEJ,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QACjD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAChD,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IAKO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAQnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;IACvC,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,MAAmB;QACnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;QAEjE,IAAI,CAAC;YAcH,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE;gBAChC,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,WAAW,EAAE,IAAI;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC;YAC7C,OAAO,MAAM,CAAC,OAAO,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,OAAqB;QAErB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,SAAS,OAAO,YAAY,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,OAAO,SAAS,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAEnE,IAAI,CAAC;YAUH,MAAM,QAAQ,GAAkB;gBAC9B,OAAO,EAAE,oCAAoC,OAAO,CAAC,OAAO,wCAAwC;gBACpG,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE;oBACR,OAAO;oBACP,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,SAAS,EAAE,OAAO,CAAC,EAAE;iBACtB;aACF,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAKD,cAAc,CAAC,OAAe;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,OAAO;YACP,MAAM,EAAE,QAAQ;YAChB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,KAAK;YACzC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;IAKD,eAAe;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1C,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,OAAe;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,SAAS,OAAO,YAAY,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC;YAGH,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,OAAO,MAAM,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW;QAMf,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc;YACvD,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YAC9B,kBAAkB,EAAE,IAAI,CAAC,aAAa;YACtC,OAAO,EAAE;gBACP,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;gBACpC,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;gBAClC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;aACzB;SACF,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,OAAO;QACnB,KAAK,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/C,IAAI,CAAC;gBAGH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,OAAO,EAAE,CAAC,CAAC;YACzC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,OAAO,QAAQ,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;CAOF,CAAA;AA/MY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAOuB,sBAAa;QACX,kCAAe;GAPxC,mBAAmB,CA+M/B"}