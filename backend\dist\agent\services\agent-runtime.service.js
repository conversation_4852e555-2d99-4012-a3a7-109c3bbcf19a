"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AgentRuntimeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentRuntimeService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const database_service_1 = require("../../database/database.service");
let AgentRuntimeService = AgentRuntimeService_1 = class AgentRuntimeService {
    constructor(configService, databaseService) {
        this.configService = configService;
        this.databaseService = databaseService;
        this.logger = new common_1.Logger(AgentRuntimeService_1.name);
        this.runtimes = new Map();
        this.isInitialized = false;
    }
    async onModuleInit() {
        this.logger.log('正在初始化Agent Runtime Service...');
        try {
            await this.initializeElizaOS();
            this.isInitialized = true;
            this.logger.log('Agent Runtime Service初始化完成');
        }
        catch (error) {
            this.logger.error(`Agent Runtime Service初始化失败: ${error.message}`);
            throw error;
        }
    }
    async onModuleDestroy() {
        this.logger.log('正在清理Agent Runtime Service...');
        await this.cleanup();
    }
    async initializeElizaOS() {
        this.logger.log('初始化ElizaOS框架...');
        this.logger.log('ElizaOS框架初始化占位符完成');
    }
    async createAgent(config) {
        this.logger.log(`创建Agent: ${config.name} (${config.agentType})`);
        try {
            this.runtimes.set(config.agentId, {
                agentId: config.agentId,
                agentType: config.agentType,
                name: config.name,
                initialized: true,
                createdAt: new Date(),
            });
            this.logger.log(`Agent ${config.name} 创建成功`);
            return config.agentId;
        }
        catch (error) {
            this.logger.error(`创建Agent失败: ${error.message}`);
            throw new Error(`Failed to create agent: ${error.message}`);
        }
    }
    async processMessage(agentId, message) {
        const runtime = this.runtimes.get(agentId);
        if (!runtime) {
            throw new Error(`Agent ${agentId} not found`);
        }
        this.logger.log(`处理消息 - Agent: ${agentId}, 用户: ${message.userId}`);
        try {
            const response = {
                content: `Hello! I received your message: "${message.content}". I'm currently in development phase.`,
                type: 'text',
                metadata: {
                    agentId,
                    processedAt: new Date(),
                    messageId: message.id,
                },
            };
            return response;
        }
        catch (error) {
            this.logger.error(`消息处理失败: ${error.message}`);
            throw new Error(`Failed to process message: ${error.message}`);
        }
    }
    getAgentStatus(agentId) {
        const runtime = this.runtimes.get(agentId);
        if (!runtime) {
            return null;
        }
        return {
            agentId,
            status: 'active',
            initialized: runtime.initialized || false,
            createdAt: runtime.createdAt,
            messageCount: 0,
            lastActivity: new Date(),
        };
    }
    getActiveAgents() {
        return Array.from(this.runtimes.keys());
    }
    async stopAgent(agentId) {
        const runtime = this.runtimes.get(agentId);
        if (!runtime) {
            throw new Error(`Agent ${agentId} not found`);
        }
        try {
            this.runtimes.delete(agentId);
            this.logger.log(`Agent ${agentId} 已停止`);
        }
        catch (error) {
            this.logger.error(`停止Agent失败: ${error.message}`);
            throw error;
        }
    }
    async healthCheck() {
        return {
            status: this.isInitialized ? 'healthy' : 'initializing',
            agentCount: this.runtimes.size,
            elizaosInitialized: this.isInitialized,
            details: {
                activeAgents: this.getActiveAgents(),
                memoryUsage: process.memoryUsage(),
                uptime: process.uptime(),
            },
        };
    }
    async cleanup() {
        for (const [agentId, runtime] of this.runtimes) {
            try {
                this.logger.log(`清理Agent: ${agentId}`);
            }
            catch (error) {
                this.logger.error(`清理Agent ${agentId} 失败: ${error.message}`);
            }
        }
        this.runtimes.clear();
    }
};
exports.AgentRuntimeService = AgentRuntimeService;
exports.AgentRuntimeService = AgentRuntimeService = AgentRuntimeService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        database_service_1.DatabaseService])
], AgentRuntimeService);
//# sourceMappingURL=agent-runtime.service.js.map