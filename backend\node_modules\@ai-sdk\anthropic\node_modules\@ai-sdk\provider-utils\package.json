{"name": "@ai-sdk/provider-utils", "version": "1.0.22", "license": "Apache-2.0", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**/*", "test/dist/**/*", "CHANGELOG.md"], "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./test": {"types": "./test/dist/index.d.ts", "import": "./test/dist/index.mjs", "module": "./test/dist/index.mjs", "require": "./test/dist/index.js"}}, "dependencies": {"@ai-sdk/provider": "0.0.26", "eventsource-parser": "^1.1.2", "nanoid": "^3.3.7", "secure-json-parse": "^2.7.0"}, "devDependencies": {"@types/node": "^18", "msw": "2.3.1", "tsup": "^8", "typescript": "5.5.4", "zod": "3.23.8", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "peerDependenciesMeta": {"zod": {"optional": true}}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "homepage": "https://sdk.vercel.ai/docs", "repository": {"type": "git", "url": "git+https://github.com/vercel/ai.git"}, "bugs": {"url": "https://github.com/vercel/ai/issues"}, "keywords": ["ai"], "scripts": {"build": "tsup", "clean": "rm -rf dist && rm -rf test/dist", "dev": "tsup --watch", "lint": "eslint \"./**/*.ts*\"", "type-check": "tsc --noEmit", "prettier-check": "prettier --check \"./**/*.ts*\"", "test": "pnpm test:node && pnpm test:edge", "test:edge": "vitest --config vitest.edge.config.js --run", "test:node": "vitest --config vitest.node.config.js --run"}}