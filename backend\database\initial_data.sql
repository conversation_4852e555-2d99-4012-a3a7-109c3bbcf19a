-- Donny Web3 初始数据
-- 包含AI Agent类型定义和基础权限配置

-- ===== 插入Agent类型 =====

INSERT INTO agent_types (name, display_name, description, persona_config, tools_config) VALUES
(
    'almighty',
    'Almighty Donny',
    '全能型AI助手，负责用户引导、流程协调、状态监控和反馈收集',
    '{
        "personality": "wise, patient, helpful, coordinating",
        "role": "conductor",
        "specialties": ["user_guidance", "process_coordination", "status_monitoring", "feedback_collection"],
        "communication_style": "warm, professional, encouraging"
    }',
    '{
        "available_tools": ["user_management", "session_coordination", "agent_communication", "feedback_collection", "analytics"],
        "permissions": ["read_user_data", "coordinate_agents", "collect_feedback", "monitor_sessions"]
    }'
),
(
    'taoist',
    'Taoist Donny',
    '道家解梦专家，从阴阳五行、天人合一的角度解析梦境',
    '{
        "personality": "serene, wise, philosophical, harmonious",
        "role": "dream_interpreter",
        "specialties": ["taoist_philosophy", "yin_yang_theory", "five_elements", "nature_symbolism"],
        "communication_style": "poetic, metaphorical, gentle",
        "cultural_background": "traditional_chinese_philosophy"
    }',
    '{
        "available_tools": ["dream_analysis", "symbol_interpretation", "philosophy_reference", "meditation_guidance"],
        "permissions": ["read_dreams", "write_interpretations", "access_taoist_knowledge"]
    }'
),
(
    'freud',
    'Freud Donny',
    '精神分析专家，从潜意识、性心理学角度分析梦境内容',
    '{
        "personality": "analytical, insightful, direct, scientific",
        "role": "dream_interpreter",
        "specialties": ["psychoanalysis", "unconscious_mind", "dream_symbolism", "psychological_analysis"],
        "communication_style": "professional, detailed, analytical",
        "theoretical_framework": "freudian_psychoanalysis"
    }',
    '{
        "available_tools": ["psychoanalytic_interpretation", "symbol_analysis", "unconscious_exploration", "case_comparison"],
        "permissions": ["read_dreams", "write_interpretations", "access_psychology_knowledge"]
    }'
),
(
    'papa',
    'Papa Donny',
    '现代心理学专家，从认知行为、积极心理学角度提供实用建议',
    '{
        "personality": "caring, practical, supportive, modern",
        "role": "dream_interpreter",
        "specialties": ["cognitive_psychology", "behavioral_analysis", "positive_psychology", "practical_advice"],
        "communication_style": "warm, accessible, solution-oriented",
        "approach": "modern_therapeutic"
    }',
    '{
        "available_tools": ["cognitive_analysis", "behavioral_insights", "practical_guidance", "emotional_support"],
        "permissions": ["read_dreams", "write_interpretations", "access_modern_psychology"]
    }'
),
(
    'accountant',
    'Accountant Donny',
    '财务管理专家，负责支付处理、NFT管理和财务协调',
    '{
        "personality": "precise, reliable, professional, detail-oriented",
        "role": "financial_coordinator",
        "specialties": ["payment_processing", "nft_management", "financial_coordination", "transaction_monitoring"],
        "communication_style": "clear, professional, trustworthy"
    }',
    '{
        "available_tools": ["payment_processing", "nft_minting", "transaction_verification", "financial_reporting"],
        "permissions": ["manage_payments", "mint_nfts", "access_financial_data", "coordinate_transactions"]
    }'
) ON CONFLICT (name) DO UPDATE SET
    display_name = EXCLUDED.display_name,
    description = EXCLUDED.description,
    persona_config = EXCLUDED.persona_config,
    tools_config = EXCLUDED.tools_config;

-- ===== 插入Agent权限配置 =====

INSERT INTO agent_permissions (agent_type, resource, action, conditions) VALUES
-- Almighty Donny权限
('almighty', 'users', 'read', '{"scope": "basic_info"}'),
('almighty', 'users', 'update', '{"scope": "status_only"}'),
('almighty', 'conversation_sessions', 'read', '{}'),
('almighty', 'conversation_sessions', 'write', '{}'),
('almighty', 'conversation_messages', 'read', '{}'),
('almighty', 'conversation_messages', 'write', '{}'),
('almighty', 'agent_coordination', 'manage', '{}'),
('almighty', 'feedback', 'collect', '{}'),

-- Taoist Donny权限
('taoist', 'dreams', 'read', '{"privacy_level": ["private", "public"]}'),
('taoist', 'interpretations', 'write', '{"agent_type": "taoist"}'),
('taoist', 'knowledge_entries', 'read', '{"category": "taoist"}'),
('taoist', 'conversation_messages', 'write', '{"context": "dream_analysis"}'),

-- Freud Donny权限
('freud', 'dreams', 'read', '{"privacy_level": ["private", "public"]}'),
('freud', 'interpretations', 'write', '{"agent_type": "freud"}'),
('freud', 'knowledge_entries', 'read', '{"category": "freudian"}'),
('freud', 'conversation_messages', 'write', '{"context": "dream_analysis"}'),

-- Papa Donny权限
('papa', 'dreams', 'read', '{"privacy_level": ["private", "public"]}'),
('papa', 'interpretations', 'write', '{"agent_type": "papa"}'),
('papa', 'knowledge_entries', 'read', '{"category": "modern"}'),
('papa', 'conversation_messages', 'write', '{"context": "dream_analysis"}'),

-- Accountant Donny权限
('accountant', 'service_orders', 'read', '{}'),
('accountant', 'service_orders', 'write', '{}'),
('accountant', 'service_orders', 'update', '{}'),
('accountant', 'payments', 'process', '{}'),
('accountant', 'nfts', 'mint', '{}'),
('accountant', 'transactions', 'verify', '{}')

ON CONFLICT DO NOTHING;

-- ===== 插入基础知识库条目 (示例) =====

INSERT INTO knowledge_entries (title, content, category, tags, metadata, source) VALUES
(
    '道家梦境理论基础',
    '在道家哲学中，梦境被视为阴阳交替、天人感应的体现。梦中的符号往往反映了自然界的变化和个人内在的平衡状态。五行理论在解梦中起到重要作用：金代表收敛、木代表生长、水代表流动、火代表激烈、土代表稳定。',
    'taoist',
    ARRAY['阴阳', '五行', '天人合一', '自然象征'],
    '{"importance": "fundamental", "usage": "dream_interpretation_foundation"}',
    'traditional_taoist_texts'
),
(
    '弗洛伊德梦的解析理论',
    '弗洛伊德认为梦是潜意识的皇道。梦境通过象征、凝缩、移置等机制，将被压抑的欲望和冲突以伪装的形式呈现。梦的显意内容(manifest content)和隐意内容(latent content)的区别是分析的关键。',
    'freudian',
    ARRAY['潜意识', '象征', '压抑', '欲望', '梦的工作'],
    '{"importance": "fundamental", "usage": "psychoanalytic_interpretation"}',
    'freud_interpretation_of_dreams'
),
(
    '认知行为视角下的梦境分析',
    '现代认知心理学认为，梦境反映了大脑对日间经历的处理和整合过程。梦境内容往往与个人的认知模式、情绪状态和行为习惯相关。通过分析梦境可以了解个人的思维模式和情绪调节策略。',
    'modern',
    ARRAY['认知处理', '情绪调节', '记忆整合', '思维模式'],
    '{"importance": "fundamental", "usage": "cognitive_behavioral_analysis"}',
    'modern_psychology_research'
)

ON CONFLICT DO NOTHING;

-- ===== 创建数据库函数 =====

-- 获取Agent权限的函数
CREATE OR REPLACE FUNCTION get_agent_permissions(agent_name VARCHAR)
RETURNS TABLE(resource VARCHAR, action VARCHAR, conditions JSONB) AS $$
BEGIN
    RETURN QUERY
    SELECT ap.resource, ap.action, ap.conditions
    FROM agent_permissions ap
    WHERE ap.agent_type = agent_name;
END;
$$ LANGUAGE plpgsql;

-- 检查Agent是否有特定权限的函数
CREATE OR REPLACE FUNCTION check_agent_permission(
    agent_name VARCHAR,
    resource_name VARCHAR,
    action_name VARCHAR
) RETURNS BOOLEAN AS $$
DECLARE
    permission_exists BOOLEAN := FALSE;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM agent_permissions
        WHERE agent_type = agent_name
        AND resource = resource_name
        AND action = action_name
    ) INTO permission_exists;
    
    RETURN permission_exists;
END;
$$ LANGUAGE plpgsql;

-- 创建用户并关联钱包地址的函数
CREATE OR REPLACE FUNCTION create_user_with_wallet(
    wallet_addr VARCHAR,
    user_name VARCHAR DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    new_user_id UUID;
BEGIN
    -- 插入用户
    INSERT INTO users (username, primary_wallet_address, user_status)
    VALUES (user_name, wallet_addr, CASE WHEN user_name IS NULL THEN 'guest' ELSE 'registered' END)
    RETURNING id INTO new_user_id;
    
    -- 插入钱包地址记录
    INSERT INTO wallet_addresses (user_id, wallet_address, is_primary)
    VALUES (new_user_id, wallet_addr, TRUE);
    
    RETURN new_user_id;
END;
$$ LANGUAGE plpgsql; 