import { OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DatabaseService } from '../../database/database.service';
export interface AgentConfig {
    agentId: string;
    agentType: string;
    name: string;
    persona: any;
    tools: any[];
    modelProvider: string;
    apiKey?: string;
}
export interface AgentMessage {
    id: string;
    userId: string;
    content: string;
    type: 'text' | 'image' | 'audio' | 'system';
    timestamp: Date;
    metadata?: any;
}
export interface AgentResponse {
    content: string;
    type: 'text' | 'image' | 'audio' | 'action';
    metadata?: any;
    actions?: AgentAction[];
}
export interface AgentAction {
    type: string;
    parameters: any;
    description?: string;
}
export declare class AgentRuntimeService implements OnModuleInit, OnModuleDestroy {
    private readonly configService;
    private readonly databaseService;
    private readonly logger;
    private runtimes;
    private isInitialized;
    constructor(configService: ConfigService, databaseService: DatabaseService);
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    private initializeElizaOS;
    createAgent(config: AgentConfig): Promise<string>;
    processMessage(agentId: string, message: AgentMessage): Promise<AgentResponse>;
    getAgentStatus(agentId: string): any;
    getActiveAgents(): string[];
    stopAgent(agentId: string): Promise<void>;
    healthCheck(): Promise<{
        status: string;
        agentCount: number;
        elizaosInitialized: boolean;
        details: any;
    }>;
    private cleanup;
}
