{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../../../src/program/namespace/state.ts"], "names": [], "mappings": ";;;;;;AAAA,kEAAyC;AACzC,0DAAkC;AAClC,6CAKyB;AACzB,mDAA0D;AAE1D,mDAA6E;AAM7E,4CAA8E;AAC9E,qDAG+B;AAE/B,sEAA2D;AAC3D,sDAA2C;AAC3C,sEAA2D;AAG3D,MAAqB,YAAY;IACxB,MAAM,CAAC,KAAK,CACjB,GAAQ,EACR,KAAY,EACZ,SAAoB,EACpB,QAAmB;QAEnB,IAAI,GAAG,CAAC,KAAK,KAAK,SAAS,EAAE;YAC3B,OAAO,SAAS,CAAC;SAClB;QACD,OAAO,IAAI,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;CACF;AAZD,+BAYC;AAMD;;;;GAIG;AACH,MAAa,WAAW;IAgBtB;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAQD,YACE,GAAQ,EACR,SAAoB;IACpB;;OAEG;IACa,WAAqB,IAAA,yBAAW,GAAE;IAClD;;OAEG;IACa,QAAe,IAAI,qBAAU,CAAC,GAAG,CAAC;QAJlC,aAAQ,GAAR,QAAQ,CAA0B;QAIlC,UAAK,GAAL,KAAK,CAA6B;QAElD,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,oBAAoB;QACpB,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,GAIvC,EAAE;;YACF,IAAI,WAAW,GAAyB,EAAE,CAAC;YAC3C,IAAI,WAAW,GAAyB,EAAE,CAAC;YAC3C,IAAI,GAAG,GAAiB,EAAE,CAAC;YAE3B,MAAA,GAAG,CAAC,KAAK,0CAAE,OAAO,CAAC,OAAO,CACxB,CAAyC,CAAI,EAAE,EAAE;gBAC/C,4BAA4B;gBAC5B,MAAM,MAAM,GAAG,wBAA2B,CAAC,KAAK,CAC9C,CAAC,EACD,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EACzD,SAAS,CACV,CAAC;gBACF,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;oBAChC,MAAM,IAAI,GAAG,oBAAoB,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;oBACpE,OAAO,IAAI,CAAC,MAAM,CAChB,wBAA2B,CAAC,aAAa,CACvC,QAAQ,EACR,CAAC,CAAC,QAAQ,EACV,SAAS,EACT,CAAC,CAAC,IAAI,CACP,CACF,CAAC;gBACJ,CAAC,CAAC;gBACF,4BAA4B;gBAC5B,MAAM,MAAM,GAAG,wBAA2B,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;gBAC5D,oBAAoB;gBACpB,MAAM,OAAO,GAAG,gBAAmB,CAAC,KAAK,CACvC,CAAC,EACD,MAAM,EACN,IAAA,0BAAc,EAAC,GAAG,CAAC,EACnB,QAAQ,CACT,CAAC;gBAEF,kDAAkD;gBAClD,MAAM,IAAI,GAAG,IAAA,mBAAS,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC/B,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;gBAC3B,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;gBAC3B,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;YACtB,CAAC,CACF,CAAC;YAEF,OAAO;gBACL,WAAsE;gBACtE,WAAsE;gBACtE,GAAsD;aACvD,CAAC;QACJ,CAAC,CAAC,EAAE,CAAC;QACL,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QAQT,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACxE,IAAI,WAAW,KAAK,IAAI,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;SAC9D;QACD,+CAA+C;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACnD;QACD,MAAM,qBAAqB,GAAG,MAAM,IAAA,6BAAkB,EAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC1E,IAAI,qBAAqB,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAC/D,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,SAAS,CAAC,UAAuB;QAC/B,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YACtB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;SACrB;QACD,MAAM,EAAE,GAAG,IAAI,uBAAY,EAAE,CAAC;QAE9B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe,CACvD,IAAI,CAAC,OAAO,EAAE,EACd,CAAC,GAAG,EAAE,EAAE;YACN,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAClD,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC7B,CAAC,EACD,UAAU,CACX,CAAC;QAEF,IAAI,CAAC,IAAI,GAAG;YACV,EAAE;YACF,QAAQ;SACT,CAAC;QAEF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YACtB,IAAI,CAAC,QAAQ,CAAC,UAAU;iBACrB,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC/C,IAAI,CAAC,KAAK,IAAI,EAAE;gBACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACnB,CAAC,CAAC;iBACD,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzB;IACH,CAAC;CACF;AAnLD,kCAmLC;AAED,yEAAyE;AACzE,SAAS,mBAAmB,CAAC,SAAoB;IAC/C,IAAI,CAAC,cAAc,CAAC,GAAG,IAAA,kCAAsB,EAAC,EAAE,EAAE,SAAS,CAAC,CAAC;IAC7D,OAAO,IAAA,8BAAkB,EAAC,cAAc,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;AACtE,CAAC;AAED,2EAA2E;AAC3E,4BAA4B;AAC5B,SAAS,oBAAoB,CAC3B,SAAoB,EACpB,QAAkB,EAClB,CAAI,EACJ,QAAyC;IAEzC,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE;QACpB,qBAAqB;QACrB,MAAM,CAAC,aAAa,CAAC,GAAG,IAAA,kCAAsB,EAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QAC9D,mBAAmB;QACnB,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE;YACjC,MAAM,IAAI,KAAK,CACb,qFAAqF,CACtF,CAAC;SACH;QACD,OAAO;YACL;gBACE,mBAAmB;gBACnB,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,SAAS;gBACjC,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,MAAM,EAAE,mBAAmB,CAAC,SAAS,CAAC;gBACtC,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,KAAK;aAChB;YACD,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE;YAC7D;gBACE,MAAM,EAAE,uBAAa,CAAC,SAAS;gBAC/B,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,KAAK;aAChB;YAED,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE;SAC1D,CAAC;KACH;SAAM;QACL,IAAA,4BAAgB,EAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACvC,OAAO;YACL;gBACE,MAAM,EAAE,mBAAmB,CAAC,SAAS,CAAC;gBACtC,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,KAAK;aAChB;SACF,CAAC;KACH;AACH,CAAC"}