{"version": 3, "file": "instruction.d.ts", "sourceRoot": "", "sources": ["../../../../src/program/namespace/instruction.ts"], "names": [], "mappings": ";AAAA,OAAO,EACL,WAAW,EACX,SAAS,EACT,sBAAsB,EACvB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EACL,GAAG,EAEH,cAAc,EAEd,cAAc,EACf,MAAM,cAAc,CAAC;AAQtB,OAAO,EAAE,QAAQ,EAAmB,MAAM,eAAe,CAAC;AAE1D,OAAO,EACL,eAAe,EACf,kBAAkB,EAClB,oBAAoB,EAEpB,yBAAyB,EAC1B,MAAM,YAAY,CAAC;AAEpB,MAAM,CAAC,OAAO,OAAO,2BAA2B;WAChC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC,SAAS,eAAe,CAAC,GAAG,CAAC,EACjE,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,mBAAmB,CAAC,CAAC,CAAC,EAChC,SAAS,EAAE,SAAS,GACnB,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC;WA0CV,aAAa,CACzB,GAAG,EAAE,QAAQ,GAAG,SAAS,EACzB,QAAQ,EAAE,SAAS,cAAc,EAAE,EACnC,SAAS,EAAE,SAAS,EACpB,MAAM,CAAC,EAAE,MAAM,GACd,WAAW,EAAE;CA6CjB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,MAAM,oBAAoB,CAC9B,GAAG,SAAS,GAAG,GAAG,GAAG,EACrB,CAAC,SAAS,cAAc,GAAG,GAAG,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,IACpD,yBAAyB,CAC3B,GAAG,EACH,CAAC,EACD,sBAAsB,EACtB;KACG,CAAC,IAAI,MAAM,kBAAkB,CAAC,GAAG,CAAC,GAAG;QACpC,QAAQ,EAAE,CACR,GAAG,EAAE,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,KAC1D,OAAO,CAAC;KACd;CACF,CACF,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,aAAa,CACvB,GAAG,SAAS,GAAG,GAAG,GAAG,EACrB,CAAC,SAAS,eAAe,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,IACnD,oBAAoB,CAAC,GAAG,EAAE,CAAC,EAAE,sBAAsB,CAAC,GACtD,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAE3C,KAAK,OAAO,CAAC,CAAC,SAAS,QAAQ,IAAI;IACjC;;OAEG;IACH,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,WAAW,EAAE,CAAC;CACrC,CAAC;AAEF,MAAM,MAAM,mBAAmB,CAAC,CAAC,SAAS,cAAc,GAAG,cAAc,IAAI,CAC3E,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,EACjB,EAAE,EAAE,GAAG,KACJ,MAAM,CAAC"}